#!/usr/bin/env python

import fontforge
import sys
import os

def main():
    if len(sys.argv) < 2:
        print("Usage: fontforge -script extract_fonts.py <pdf_file>")
        sys.exit(1)

    pdf_file = sys.argv[1]

    # Check if the PDF file exists
    if not os.path.isfile(pdf_file):
        print(f"Error: File '{pdf_file}' not found.")
        sys.exit(1)

    # Get the list of fonts in the PDF
    font_list = fontforge.fontsInFile(pdf_file)

    if not font_list:
        print("No fonts found in the PDF.")
        sys.exit(1)

    print(f"Found {len(font_list)} fonts in '{pdf_file}'.")

    # Create an output directory
    output_dir = os.path.splitext(pdf_file)[0] + "_fonts"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Process each font
    for index, fontname in enumerate(font_list):
        print(f"Processing font {index + 1}/{len(font_list)}: {fontname}")
        # Open the font from the PDF
        font = fontforge.open(f"{pdf_file}({fontname})")

        # Determine the output filename
        # Use both the family name and font name to ensure uniqueness
        family_name = font.familyname.replace(" ", "_") if font.familyname else "UnknownFamily"
        font_name = font.fontname.replace(" ", "_") if font.fontname else f"Font{index}"

        output_filename = f"{family_name}_{font_name}.ttf"
        output_path = os.path.join(output_dir, output_filename)

        # Save the font as TTF
        font.generate(output_path)
        font.close()

        print(f"Saved: {output_path}")

    print("All fonts have been extracted and saved.")

if __name__ == "__main__":
    main()
