# portal.vanstraaten.test

## Modules systemwide
- Can have filters (lacodix/laravel-model-filter) for overviews
- should have configs (to enable or determine what columns/features are activated)
- Have routes (90% magicly)
- Can have traits:
  - Pageable (for frontend pages, should take care of: sluggable & hasTranslations, hasAttachments, hasTextEditor)
  - Treeable 
  - Translatable
  - Filterable
  - HasDocuments
  - HasAttachments
  - HasTextEditor

## Permissions

Permissions uses the spatie/laravel-permission package
There should be manageable roles to assign to users
With permissions for modules itself and all columns/inputs to view/edit

For roles there should also be settings. 
This is a Custom Model and settings are connected to Modules/Users using settingable 
Settings will have things like: Max spendable amount per day (things with values in it)

See Role Model 

## Morphable/Relatable models

All Models that are relatable will have tables: {{NAME}}_relations
Morphables will have columns: relatable_id & relatable_type or relatable is replaced with model name: imageable_id, taggable_id

- Images
- Documents
- Addresses
- Tags
- Settings
- Attachments
- Buttons

## Traits:
- Pageable: when a module/model must have a page/slug on the frontend
  This covers: attachments, tags, images, files, translations, slugs
- Treeable: When The models can have parents/siblings

- HasSlugs
- HasAttachments
- HasFiles
- HasTranslations
- HasFilters
- HasImages
- HasProperties
- HastextEditor (maybe hasText or HasContent)
- 