<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products_properties_values', function (Blueprint $table) {
            $table->id();
            $table->boolean('status')->default(0)->unsigned();
            $table->unsignedBigInteger('property_value_id');
            $table->foreign('property_value_id')
                ->references('id')
                ->on('properties_values')
                ->onDelete('cascade')
                ->onUpdate('no action');

            $table->unsignedBigInteger('product_id');
            $table->foreign('product_id')
                ->references('id')
                ->on('products')
                ->onDelete('cascade')
                ->onUpdate('no action');

            $table->timestampsDefault();
            $table->sortColumns();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products_properties_values');
    }
};
