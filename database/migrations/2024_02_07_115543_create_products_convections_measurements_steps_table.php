<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products_convections_measurements_steps', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('product_convection_measurement_id');

            $table->foreign('product_convection_measurement_id', 'pvm_id_foreign')
                ->references('id')
                ->on('products_convections_measurements')
                ->onDelete('cascade')
                ->onUpdate('no action');

            $table->unsignedInteger('amount_start');
            $table->unsignedInteger('amount_end');
            $table->decimal('value', 10, 2)->nullable();

            $table->timestampsDefault();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products_convections_measurements_steps');
    }
};
