<?php

namespace Database\Factories;

use App\Enums\Order\Type;
use App\Enums\OrderState;
use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_id' => Company::factory(),
            'type' => Type::SALES,
            'state' => OrderState::DRAFT,
            'order_number' => null, // Will be auto-generated
            'total_items_excl' => fake()->randomFloat(2, 100, 5000),
            'total_shipping_excl' => fake()->randomFloat(2, 10, 100),
            'total_excl' => function (array $attributes) {
                return $attributes['total_items_excl'] + $attributes['total_shipping_excl'];
            },
            'total_tax' => function (array $attributes) {
                return $attributes['total_excl'] * 0.21; // 21% VAT
            },
            'total_incl' => function (array $attributes) {
                return $attributes['total_excl'] + $attributes['total_tax'];
            },
            'description' => fake()->sentence(),
            'is_confirmed' => false,
            'is_rejected' => false,
            'is_customer' => true,
            'is_finished' => false,
            'is_urgent' => false,
            'is_standbuilder' => false,
            'is_foreign' => false,
            'is_locked' => false,
            'is_reproduction' => false,
            'is_tolate' => false,
            'is_sent' => false,
            'deadline_at' => fake()->dateTimeBetween('now', '+30 days'),
            'delivery_at' => fake()->dateTimeBetween('+1 week', '+6 weeks'),
            'order_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'sent_at' => null,
        ];
    }

    /**
     * Indicate that the order is a purchase order.
     */
    public function purchase(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => Type::PURCHASE,
        ]);
    }

    /**
     * Indicate that the order is a sales order.
     */
    public function sales(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => Type::SALES,
        ]);
    }

    /**
     * Indicate that the order is confirmed.
     */
    public function confirmed(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_confirmed' => true,
            'state' => OrderState::SUPPLIER_CONFIRMED,
        ]);
    }

    /**
     * Indicate that the order is rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_rejected' => true,
            'state' => OrderState::SUPPLIER_REJECTED,
        ]);
    }

    /**
     * Indicate that the order is sent.
     */
    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_sent' => true,
            'sent_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'state' => OrderState::SUPPLIER_PENDING,
        ]);
    }

    /**
     * Indicate that the order is urgent.
     */
    public function urgent(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_urgent' => true,
            'deadline_at' => fake()->dateTimeBetween('now', '+3 days'),
        ]);
    }

    /**
     * Indicate that the order is finished.
     */
    public function finished(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_finished' => true,
            'is_confirmed' => true,
            'state' => OrderState::SUPPLIER_CONFIRMED,
        ]);
    }

    /**
     * Indicate that the order is locked.
     */
    public function locked(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_locked' => true,
        ]);
    }

    /**
     * Create an order with specific state.
     */
    public function withState(OrderState $state): static
    {
        return $this->state(fn (array $attributes) => [
            'state' => $state,
        ]);
    }

    /**
     * Create an order that is waiting for sending.
     */
    public function waitingForSending(): static
    {
        return $this->state(fn (array $attributes) => [
            'state' => OrderState::WAITING_FOR_SENDING,
            'sent_at' => fake()->dateTimeBetween('now', '+1 day'),
        ]);
    }
}
