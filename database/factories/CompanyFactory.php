<?php

namespace Database\Factories;

use App\Enums\MeasurementSystem;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Company>
 */
class CompanyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uniq_id' => Str::uuid(),
            'locale_id' => 1,
            'country_id' => 152,
            'salesforce_id' => null,
            'exact_uniq_id' => null,
            'tax_identifier_id' => null,
            'admin_backoffice_id' => null,
            'admin_sales_id' => null,
            'pricing_group_a_id' => null,
            'pricing_group_b_id' => null,
            'pricing_group_c_id' => null,
            'systemname' => fake()->company(),
            'website' => fake()->domainName(),
            'phone' => fake()->phoneNumber(),
            'email' => fake()->companyEmail(),
            'email_financial' => fake()->companyEmail(),
            'coc_number' => fake()->numerify('########'),
            'currency' => 'EUR',
            'measurement_system' => fake()->randomElement(MeasurementSystem::values()),
            'days_production' => '00000011111',
            'days_deadline' => '1111100',
            'days_delivery' => '1111100',
            'is_main' => false,
            'is_reseller' => false,
            'is_supplier' => false,
            'is_customer' => true,
            'is_taxable' => true,
            'is_key' => false,
            'can_quote' => true,
            'can_order' => true,
            'can_api' => false,
            'can_cutout' => false,
            'can_cutout_economy' => false,
            'can_eproof' => false,
            'can_eproof_whitelabel' => false,
            'can_eproof_customer' => false,
            'can_selfchoice_delivery_carrier' => false,
            'can_selfchoice_delivery_time' => false,
            'can_selfchoice_packaging_roll_amount' => false,
            'has_po_number' => false,
            'has_confirmation' => false,
            'has_pricing_minimal_m1' => false,
            'has_pricing_startup' => false,
        ];
    }

    /**
     * Indicate that the company is a main company.
     */
    public function main(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_main' => true,
        ]);
    }

    /**
     * Indicate that the company is a supplier.
     */
    public function supplier(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_supplier' => true,
            'is_customer' => false,
        ]);
    }

    /**
     * Indicate that the company is a reseller.
     */
    public function reseller(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_reseller' => true,
        ]);
    }

    /**
     * Indicate that the company is a key customer.
     */
    public function keyCustomer(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_key' => true,
            'can_api' => true,
            'can_eproof' => true,
        ]);
    }

    /**
     * Configure the company with full capabilities.
     */
    public function withFullCapabilities(): static
    {
        return $this->state(fn (array $attributes) => [
            'can_quote' => true,
            'can_order' => true,
            'can_api' => true,
            'can_cutout' => true,
            'can_cutout_economy' => true,
            'can_eproof' => true,
            'can_eproof_whitelabel' => true,
            'can_eproof_customer' => true,
            'can_selfchoice_delivery_carrier' => true,
            'can_selfchoice_delivery_time' => true,
            'can_selfchoice_packaging_roll_amount' => true,
            'has_po_number' => true,
            'has_confirmation' => true,
            'has_pricing_minimal_m1' => true,
            'has_pricing_startup' => true,
        ]);
    }
}
