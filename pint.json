{"preset": "laravel", "rules": {"array_indentation": false, "blank_line_after_opening_tag": true, "type_declaration_spaces": false, "nullable_type_declaration": false, "nullable_type_declaration_for_default_null_value": false, "braces": false, "class_attributes_separation": {"elements": {"const": "none", "method": "one"}}, "class_definition": false, "concat_space": {"spacing": "one"}, "new_with_parentheses": {"anonymous_class": false, "named_class": true}, "constant_case": false, "declare_strict_types": false, "single_space_around_construct": false, "single_space_after_construct": true, "single_quote": false, "statement_indentation": false, "line_ending": false, "curly_braces_position": {"allow_single_line_anonymous_functions": false, "allow_single_line_empty_anonymous_classes": false, "anonymous_classes_opening_brace": "same_line", "anonymous_functions_opening_brace": "same_line", "classes_opening_brace": "next_line_unless_newline_at_signature_end", "control_structures_opening_brace": "same_line", "functions_opening_brace": "next_line_unless_newline_at_signature_end"}, "phpdoc_align": false, "phpdoc_separation": false, "function_typehint_space": false, "linebreak_after_opening_tag": false, "lowercase_static_reference": false, "phpdoc_indent": false, "single_class_element_per_statement": {"elements": {}}, "unary_operator_spaces": false, "whitespace_after_comma_in_array": false, "multiline_whitespace_before_semicolons": false, "single_line_comment_style": false}, "exclude": ["public", "resources"]}