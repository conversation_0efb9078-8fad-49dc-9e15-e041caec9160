{"document": {"classification": "", "locale": "", "representative": "", "date": "", "currency": "", "currency_exchange_rate": "", "id": "", "period": ""}, "invoice": {"id": ""}, "order": {"id": "", "date": ""}, "delivery": {"id": "", "type": "", "carrier": "", "condition": "", "date": "", "note": "", "tracking": "", "weight": "", "volume": ""}, "totals": {"items": "", "freight": "", "discount": "", "excl": "", "tax": "", "incl": ""}, "payment": {"terms": "", "condition": "", "method": "", "date": "", "due_date": "", "reference": "", "amount_paid_since_last_invoice": ""}, "reciever": {"id": "", "registration_id": "", "tax_id": "", "payee_id": "", "name": "", "representative": "", "reference": "", "iban": "", "bic": "", "swift": "", "email": "", "phone": "", "website": "", "address": {"street": "", "housenumber": "", "addition": "", "postalcode": "", "city": "", "state": "", "country": ""}, "all_other_and_unidentified_ids": [{"name": "", "value": "", "suggested_type": ""}]}, "supplier": {"id": "", "registration_id": "", "tax_id": "", "payee_id": "", "name": "", "representative": "", "reference": "", "iban": "", "bic": "", "swift": "", "email": "", "phone": "", "website": "", "address": {"street": "", "housenumber": "", "addition": "", "postalcode": "", "city": "", "state": "", "country": ""}, "all_other_and_unidentified_ids": [{"name": "", "value": "", "suggested_type": ""}]}, "line_item": {"code": "", "name": "", "description": "", "purchase_order": "", "warrenty": "", "period": "", "weight": "", "quantity": "", "unit": "", "price_unit_incl": "", "price_unit_excl": "", "price_total_incl": "", "price_total_excl": "", "discount": "", "price_tax": "", "price_tax_rate": ""}, "ship": {"from": {"company": "", "contact": "", "address": {"street": "", "housenumber": "", "addition": "", "postalcode": "", "city": "", "state": "", "country": ""}}, "to": {"company": "", "contact": "", "address": {"street": "", "housenumber": "", "addition": "", "postalcode": "", "city": "", "state": "", "country": ""}}}, "remit": {"to": {"company": "", "contact": "", "address": {"street": "", "housenumber": "", "addition": "", "postalcode": "", "city": "", "state": "", "country": ""}}}, "vat": [{"category_code": "", "tax_amount": "", "tax_rate": ""}], "all_other_and_unidentified_text": [{"name": "", "value": "", "suggested_type": ""}]}