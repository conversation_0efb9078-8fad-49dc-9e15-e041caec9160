import { defineConfig } from 'vite';

process.env.SASS_SILENCE_DEPRECATION_WARNINGS = true;

import laravel from 'laravel-vite-plugin';
import path from 'node:path';
import babel from 'vite-plugin-babel';
import {viteCommonjs, esbuildCommonjs} from '@originjs/vite-plugin-commonjs';
import inject from "@rollup/plugin-inject";
import { sync as globSync } from 'glob';

const moduleEntries = globSync('resources/admin/js/modules/**/*.js');


export default defineConfig({
    define: {
        'process.env.SASS_SILENCE_DEPRECATION_WARNINGS': JSON.stringify(true)
    },
    css: {
        preprocessorOptions: {
          scss: {
            api: 'modern-compiler',
            quietDeps: true,
            quiet: true,
            silenceDeprecations: ['mixed-decls', 'color-functions', 'global-builtin', 'import']
          },
          css: {
            quietDeps: true,
            quiet: true,
            silenceDeprecations: ['mixed-decls', 'color-functions', 'global-builtin', 'import']
          },
          sass: {
            quietDeps: true,
            quiet: true,
            silenceDeprecations: ['mixed-decls', 'color-functions', 'global-builtin', 'import']
          },
        }
    },
    plugins: [
        inject({
            $: 'jquery',
            jQuery: 'jquery',
            _: 'lodash',
            Alphine: 'alpinejs',
            bootstrap: 'bootstrap',

        }),
        viteCommonjs(),
        laravel({
            input: [
                'resources/app.js', 
                'resources/admin/js/app.js', 
                'resources/admin/js/widgets/filters.js',
                ...moduleEntries,
            ],
            refresh: true,
        }),
        babel({
            babelConfig: {
                babelrc: false,
                configFile: false
            },
            plugins: [
                [ '@babel/plugin-proposal-class-properties', { loose: true }]
            ],
            include: ['resources/**/*.js'],
            exclude: ['node_modules/**'],
        }),
        
    ],
    resolve: {
        alias: {
            '@': path.join(__dirname, '/resources'),
            'bootstrap': 'bootstrap/dist/js/bootstrap.bundle.js'
        },
    },
    optimizeDeps: {
        include: ['bootstrap', 'jquery', 'lodash']
    },
    build: {
        watch: process.env.NODE_ENV === 'development' ? {} : false,
        minify: true,
        target: "es2020",
        cssCodeSplit: true,
        preserveAssetsDir: true,
        commonjsOptions: { 
            transformMixedEsModules: true,
        },
        rollupOptions: {
            output: {
                manualChunks(id) {
                    if (id.includes('node_modules')) {
                        const parts = id.split('node_modules/')[1].split('/');
                        return parts[0].startsWith('@') ? parts.slice(0, 2).join('/') : parts[0];
                    }
                }
            }
        }
    }
});