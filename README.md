# portal.vanstraaten.test

==PLEASE USE THE OBTAINED MYSQL DB DUMP FIRST==

use: ```composer run dev```

==If you get expired sessions errors directly after logging in it has to do with the .ENV session settings==

Adminpanel = =={{URL}}/admin==

##SRVSQL
SQLSRV Setup on PHP 8.4 (ARM64, macOS)
This guide summarizes the steps to get the sqlsrv and pdo_sqlsrv extensions working with PHP 8.4.

1. Install Required Drivers
Microsoft ODBC Driver:
Install via Homebrew:

bash
Kopiëren
brew install msodbcsql18
Verify installation:

bash
Kopiëren
ls /opt/homebrew/opt/msodbcsql18/lib/libmsodbcsql.18.dylib
unixODBC:
Install via Homebrew:

bash
Kopiëren
brew install unixodbc
2. Install PHP Extensions via PECL
Use the PHP 8.4 binary path (adjust if necessary):
bash
Kopiëren
/opt/homebrew/opt/php@8.4/bin/pecl install sqlsrv pdo_sqlsrv
Update your php.ini to load the extensions (avoid duplicates):
ini
Kopiëren
extension=/opt/homebrew/Cellar/php/8.4.4/pecl/20240924/sqlsrv.so
extension=/opt/homebrew/Cellar/php/8.4.4/pecl/20240924/pdo_sqlsrv.so
3. Configure ODBC
Update odbcinst.ini:
Create or update /etc/odbcinst.ini with:
ini
Kopiëren
[ODBC Driver 18 for SQL Server]
Description=Microsoft ODBC Driver 18 for SQL Server
Driver=/opt/homebrew/opt/msodbcsql18/lib/libmsodbcsql.18.dylib
UsageCount=1
Also create an empty /etc/odbc.ini if not already present:
bash
Kopiëren
sudo touch /etc/odbc.ini
4. Set Environment Variables for PHP-FPM (Herd)
In your Herd PHP-FPM pool configuration (e.g., in your www.conf or pool config file), add:
ini
Kopiëren
env[DYLD_LIBRARY_PATH] = /opt/homebrew/lib:/opt/homebrew/opt/unixodbc/lib:/opt/homebrew/opt/msodbcsql18/lib
env[ODBCINSTINI] = /etc/odbcinst.ini
env[ODBCINI] = /etc/odbc.ini
Optionally, set the variable globally:
bash
Kopiëren
launchctl setenv DYLD_LIBRARY_PATH "/opt/homebrew/lib:/opt/homebrew/opt/unixodbc/lib:/opt/homebrew/opt/msodbcsql18/lib"
5. Restart PHP-FPM/Herd
Make sure to restart Herd/PHP-FPM so all configuration changes take effect.

6. Laravel SQL Server Configuration
In config/database.php (or your environment file), configure your SQL Server connection:
php
Kopiëren
'sqlsrv' => [
    // other settings...
    'encrypt' => env('DB_ENCRYPT', 'no'),
    'trust_server_certificate' => env('DB_TRUST_SERVER_CERTIFICATE', 'true'),
],

## Laravel
- Make sure: config/domains.php has the entries with the url you use.
    For example: 
    ```php
    return [
        'portal_local' => [
            'id' => 2,
            'name' => 'Portal',
            'systemname' => 'portal',
            'url' => 'portal.vanstraaten.test', // <-- this must match
            'group' => 'web'
        ],
        'portal_octane_local' => [
          'id' => 2,
          'name' => 'Portal',
          'systemname' => 'portal',
          'url' => 'octane.portal.vanstraaten.test',
          'group' => 'web'
      ],
    ];
    ```
- .env file defines that must match the url
    ```ini
    APP_URL=https://octane.portal.vanstraaten.test
    ASSET_URL=https://octane.portal.vanstraaten.test
    SESSION_DOMAIN=portal.vanstraaten.test
    SESSION_COOKIE=portal_vanstraaten_session
    ```
- You can 'create' a admin user in **database/seeders/AdminSeeder.php** (yes, i know..)

## External binaries

- Mutools
- ImageMagick (wil become deprecated)
- pdf2htmlEX
- tiff2pdf
- poppler packages
- Browsershot
- Chrome headless


## Database standards

- All tables have these columns
  - id
  - created_at timestamp(6), on create=timestamp(6) 
  - updated_at timestamp(6), on update=current_timestamp(6)
  - deleted_at (timestamp(6) maybe)
-  All indentifier fields are at the beginning of the table
-  All date/time columns are at the end of the table (yes i know autistic)
- Tablenames are plural
- !! there are NO abbriviations !!
- Alle columns and tablenames are english lowercase (snakecase)
- All tables will have varchar:systemname when there is a name or title column 
- All tables for a specific module starts with the same naming segmentexample:properties & properties_valuesunits & unit_types 
- framework/package tables should be prefixed with _ for clear distinction
- Foreign keys:are the singleton of the tablename; example: properties => property_id
- subset specific columns start with the origin columnname followed by the subset name{{COLUMNNAME_ORIGIN}}_{{COLUMNNAME_SUBSET}}
  - example:     
    - origin = admin_id    ->   subset = admin_backoffice_id    
    - origin = is_example  ->   subset = is_example_viewer
- indentifiers: 
  - {{NAME}}_id
-  special indentifiers (like hashes, guid etc)
   -  {{NAME}}_uniq_id
- booleans: (tinyint 1)
  - has_{{NAME}}
  - is_{{NAME}}
  - can_{{NAME}}
- date(timestamp)
  - {{NAME}}_at
- pricings
  - pricing_{{NAME}} debateable
- amounts/quantities
  - quantity_{{NAME}}. debateable
- translatables debateable
  - {{NAME}}_{{LANG-ISO-2}} sample: title_en

======================================================================
## Eloquent standards

- !! NEVER use Model::all() !!
- Eloquent query builders always starts with Model::query() for normalisation purposes
  All other methods starts on a new line indented




======================================================================
# MacOS

## Herd requirements/services
- Node
- Redis
- MySQL
- Octane/swoole

## ~/.zshrc
```bash
# Herd injected NVM configuration
export NVM_DIR="/Users/<USER>/Library/Application Support/Herd/config/nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm

# Source Herd's ZSH settings if they exist
[[ -f "/Applications/Herd.app/Contents/Resources/config/shell/zshrc.zsh" ]] && source "/Applications/Herd.app/Contents/Resources/config/shell/zshrc.zsh"

# Set Herd's PHP binaries as the priority in PATH
export PATH="/Users/<USER>/Library/Application Support/Herd/bin:$PATH"

# PHP INI Scan Directories (Herd specific)
export HERD_PHP_{{PHP_VERSION=83}}_INI_SCAN_DIR="/Users/<USER>/Library/Application Support/Herd/config/php/{{PHP_VERSION=83}}/"
export HERD_PHP_{{PHP_VERSION=84}}_INI_SCAN_DIR="/Users/<USER>/Library/Application Support/Herd/config/php/{{PHP_VERSION=84}}/"

# Herd injected PHP binary.
export PATH="/Users/<USER>/Library/Application Support/Herd/bin/":$PATH
```

##php.ini (84)
Only the overrules defined
```ini
curl.cainfo=/Users/<USER>/Library/Application Support/Herd/config/php/cacert.pem
openssl.cafile=/Users/<USER>/Library/Application Support/Herd/config/php/cacert.pem
upload_max_filesize=25000M
post_max_size=25000M
memory_limit=4960M
```

##OCTANE

install: 
```bash
pecl install swoole
brew install pibpq
```

Herd php.ini
```ini
extension=/opt/homebrew/lib/php/pecl/20240924/swoole.so
```

```bash
php artisan octane:install
```

Herd Site Nginx configuration file:
```/Users/<USER>/Library/Application Support/Herd/config/valet/Nginx/portal.vanstraaten.test```

Third server block is 'most important'
```bash
 # Octane server
# ISOLATED_PHP_VERSION=8.4
server {
    listen 127.0.0.1:80;
    #listen 127.0.0.1:80; # valet loopback
    server_name portal.vanstraaten.test www.portal.vanstraaten.test *.portal.vanstraaten.test;
    return 301 https://$host$request_uri;
}

server {
    listen 127.0.0.1:443 ssl;
    #listen VALET_LOOPBACK:443 ssl; # valet loopback
    server_name portal.vanstraaten.test www.portal.vanstraaten.test *.portal.vanstraaten.test;
    root /;
    charset utf-8;
    client_max_body_size 1024M;
    http2 on;

    location /41c270e4-5535-4daa-b23e-c269744c2f45/ {
        internal;
        alias /;
        try_files $uri $uri/;
    }

    ssl_certificate "/Users/<USER>/Library/Application Support/Herd/config/valet/Certificates/portal.vanstraaten.test.crt";
    ssl_certificate_key "/Users/<USER>/Library/Application Support/Herd/config/valet/Certificates/portal.vanstraaten.test.key";

    location / {
        rewrite ^ "/Applications/Herd.app/Contents/Resources/valet/server.php" last;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    access_log off;
    error_log "/Users/<USER>/Library/Application Support/Herd/Log/nginx-error.log";

    error_page 404 "/Applications/Herd.app/Contents/Resources/valet/server.php";

    location ~ [^/]\.php(/|$) {
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass $herd_sock_84;
        fastcgi_index "/Applications/Herd.app/Contents/Resources/valet/server.php";
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME "/Applications/Herd.app/Contents/Resources/valet/server.php";
        fastcgi_param HERD_HOME "/Users/<USER>/Library/Application Support/Herd";
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    location ~ /\.ht {
        deny all;
    }
}


server {
    listen 127.0.0.1:443 ssl;
    server_name octane.portal.vanstraaten.test;
    root /Users/<USER>/Herd/portal.vanstraaten/public;
    index index.php;

    # Apply headers for all responses
    add_header 'Access-Control-Allow-Origin' 'https://portal.vanstraaten.test' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
    add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;

    location / {
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' 'https://portal.vanstraaten.test' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;
            add_header 'Access-Control-Max-Age' 1728000 always;
            add_header 'Content-Type' 'text/plain; charset=utf-8' always;
            add_header 'Content-Length' 0 always;
            return 204;
        }

        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

server {
    listen 127.0.0.1:60;
    #listen 127.0.0.1:60; # valet loopback
    server_name portal.vanstraaten.test www.portal.vanstraaten.test *.portal.vanstraaten.test;
    root /;
    charset utf-8;
    client_max_body_size 1024M;

    add_header X-Robots-Tag 'noindex, nofollow, nosnippet, noarchive';

    location /41c270e4-5535-4daa-b23e-c269744c2f45/ {
        internal;
        alias /;
        try_files $uri $uri/;
    }

    location / {
        rewrite ^ "/Applications/Herd.app/Contents/Resources/valet/server.php" last;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    access_log off;
    error_log "/Users/<USER>/Library/Application Support/Herd/Log/nginx-error.log";

    error_page 404 "/Applications/Herd.app/Contents/Resources/valet/server.php";

    location ~ [^/]\.php(/|$) {
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass "unix:/Users/<USER>/Library/Application Support/Herd/herd.sock";
        fastcgi_index "/Applications/Herd.app/Contents/Resources/valet/server.php";
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME "/Applications/Herd.app/Contents/Resources/valet/server.php";
        fastcgi_param HERD_HOME "/Users/<USER>/Library/Application Support/Herd";
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    location ~ /\.ht {
        deny all;
    }
}
```

======================================================================

# Windows (junk, possibly for quickfix only)

#Memurai
    Install:    memurai.exe --service-install memurai.conf
    Uninstall:  memurai.exe --service-uninstall
    Start:      memurai.exe --service-start

#PHP Extensions
    extension=intl

#Headless
    #Printer
    remove:             lpadmin -x [PRINTERNAME]
    list all printers:  lpstat -s
    find printers:      lpstat -l -e
    find printers IP:   avahi-browse --all -t -r
    




        // $connection = new \Picqer\Financials\Exact\Connection();
        // $connection->setRedirectUrl('https://portal.vanstraaten.test/exact/oauth'); // Same as entered online in the App Center
        // $connection->setExactClientId('');
        // $connection->setExactClientSecret('');
        // $connection->redirectForAuthorization();