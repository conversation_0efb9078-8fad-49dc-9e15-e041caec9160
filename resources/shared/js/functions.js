
function camelCase2SnakeCase(str)
{
    return str.replace(/[A-Z]/g, function(letter, index) {
        return index == 0 
            ? letter.toLowerCase() 
            : '_'+ letter.toLowerCase();
    });
}

function groupBy(x, f) {
    return x.reduce( (a,b,i)=>((a[f(b,i,x)]||=[]).push(b),a),{} );
} 

function validateVisibility(elements, selectedValue) 
{
    let isVisible,
        matchedStatements,
        eventElement;

    elements.forEach(function(eventData) {

        isVisible = false;

        eventData.groups.forEach(function(statements) {
        
            matchedStatements = true;

            statements.forEach(function(statement) {
                
                eventElement = $('.row-wrapper .input #'+statement.element);
                if (eventElement.length == 0) {
                    return;
                }
          
                statement.value = statement.value.constructor === Array
                    ? statement.value
                    : [ statement.value ]
                ;

                if (eventElement.is('select')) {
                    selectedValue = eventElement.selectpicker('val');
                }

                if (eventElement.is(':checkbox')) {
                    selectedValue = eventElement.is(':checked') ? 1 : 0;
                    statement.value.forEach(function(val, idx) {
                        statement.value[idx] = ['true', '1', 'on', 1].indexOf(statement.value[idx]) != -1
                            ? 1
                            : 0
                        ;
                    });
                }

                if (eventElement.is(':text')) {
                    selectedValue = eventElement.val();
                }

                if ((['='].indexOf(statement.operator) != -1 && statement.value.indexOf(selectedValue) == -1) ||
                    (['!=', '!'].indexOf(statement.operator) != -1 && statement.value.indexOf(selectedValue) != -1) ||
                    (['>'].indexOf(statement.operator) != -1 && statement.value.pop() <= selectedValue) ||
                    (['<'].indexOf(statement.operator) != -1 && statement.value.pop() >= selectedValue) ||
                    (['>='].indexOf(statement.operator) != -1 && statement.value.pop() < selectedValue) ||
                    (['<='].indexOf(statement.operator) != -1 && statement.value.pop() > selectedValue)) {
                    matchedStatements = false;
                    return;
                }

            });

            if (matchedStatements) {
                isVisible = true;
                return;
            }
        });

        let elm = eventData.element;

        if (!elm.hasClass('card-wrapper') &&
            !elm.hasClass('row-wrapper')) {

            if (!eventData.element.is('button') &&
                eventData.element.closest('.row-wrapper').length > 0) {
                elm = eventData.element.closest('.row-wrapper');
            }
        }

        if (eventData.effect == 'slide') {
            elm[isVisible ? 'slideDown' : 'slideUp'](); 
        }
        else if (eventData.effect == 'fade') {
            elm[isVisible ? 'fadeIn' : 'fadeOut'](); 
        }
        else if (eventData.effect == 'disable') {
            elm.set('disabled', isVisible ? false : true);
        }        
    });
}

function setVisibilityElementEvents(visibilityElement)
{
    Object.keys(visibilityElement).forEach(function(element) {

        let eventElement = $('.row-wrapper .input #'+element),
            isExecuted = false;

        if (eventElement.length == 0) {
            return;
        }

        [
            ['select',      'changed.bs.select'],
            [':checkbox',   'change'],
            [':text',       'change keyup']
        ].forEach(function(event) {

            if (eventElement.is(event[0])) {
               
                console.log( $.data($('.row-wrapper .input #'+element).get(0), 'events') );

                $(document).on(event[1], '.row-wrapper .input #'+element, function() {
                    validateVisibility(visibilityElement[element]); 
                });
            }

            // if (isExecuted) {
            //     return;
            // }

            visibilityElement[element].forEach(function(data) {
                if (data.element.is('button') || (
                    data.element.is('div') && data.element.closest('.row-wrapper').length == 0
                )) {
                    isExecuted = true;
                    validateVisibility(visibilityElement[element]); 
                    return;
                }
            });            
        })
    });
}

function getVisibilityElements(selectorOrElement)
{
    let localElements = [];

    $(selectorOrElement).each(function() {

        let self = $(this),
            regex,
            elementEvents = [],
            elementElements = [],
            statements;

        $(this).data('visibility').split('OR').forEach(function(segment) {

            statements = [];
            segment.split('AND').forEach(function(statement) {

                if ((regex = /\[(.*?)(=|\!=|\!|>|<|<=|>=)(.*?)\]/gi.exec(statement)).length != 4 ||
                    $('.row-wrapper .input #'+regex[1]).length == 0) {
                    return;
                } 

                statements.push({
                    'element': regex[1],
                    'operator': regex[2],
                    'value': regex[3] == 'null' ? '' : regex[3]
                });

                if (elementElements.indexOf(regex[1]) == -1) {
                    elementElements.push(regex[1]);
                }
            });

            if (statements.length > 0) {
                elementEvents.push(statements);
            }
        });

        if (elementEvents.length == 0) {
            return;
        }

        elementElements.forEach(function(element) {

            let data = {
                element: self,
                effect: self.data('visibility-effect') 
                    ? self.data('visibility-effect') 
                    : 'slide',
                groups: elementEvents
            };
            
            if (typeof localElements[element] == 'undefined') {
                localElements[element] = [];
            }

            // if (typeof globalVisibilityElements[element] == 'undefined') {
            //     globalVisibilityElements[element] = [];
            // }

            // globalVisibilityElements[element].push(data);
            localElements[element].push(data);
        });
    });

    return localElements;
}




function getHTML(element)
{
    return $("<div />").append(element.clone()).html();
}

function uniqueId() 
{
    //return Math.random().toString(36).substr(2, 16);

    return (
        Math.random().toString(36).toString().substring(2, 11) + 
        Math.random().toString(36).toString().substring(2, 11) +
        Math.random().toString().substring(1, 10)
    ).toString();
}


function replaceInHTML(item, replacements)
{
    let html = item;

    if (typeof item == 'object') {
        html = getHTML(item);
    }

    replacements.forEach(function(v, i) {
        html = html
            .replace(
                new RegExp('\\[\\['+v[0].toUpperCase()+'\\]\\]', 'gmi'), 
                v[1]
            )
            .replace(
                new RegExp('\\{\\{'+v[0].toUpperCase()+'\\}\\}', 'gmi'), 
                v[1]
            );
    })

    if (typeof item == 'object') {
        return $(html);
    }

    return html;
}

function getTemplate(templateName, replacements = [])
{  
    let element = $('.templates .template[data-name="'+templateName+'"]');

    if (typeof element.data('wrapper') === 'undefined' ||
        element.data('wrapper') == true) {

        return $(replaceInHTML(
            element[0].outerHTML, 
            replacements
        )).removeClass('template').addClass(templateName);

    } else {

        return $(replaceInHTML(
            element.html(), 
            replacements
        )).removeClass('template').addClass(templateName);
    }
}



function setLoading(sSelector, state = 'add') {
    
    if (state == 'add' && $(sSelector).find('.loading').length == 0) {
        var sLoading = '<div class="loading"><i class="fa fa-cog fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span></div>'
        $(sSelector).append(sLoading);
    }
    else if (state == 'remove') {
        $(sSelector).find('.loading').remove();
    }
}

function normalizePrice(price) {


    //remove all non digit chars except . and ,
    price = price.toString().replace(/\s|[a-zA-Z]+/gi, '');

    //number contains more then 1 .
    if( (price.match(/\./gi) || []).length > 1 && 
        (price.match(/\,/gi) || []).length == 0) {
        price = price.replace(/\./gi, '');
    }

    //number contains more then 1 ,
    if( (price.match(/\,/gi) || []).length > 1 && 
        (price.match(/\./gi) || []).length == 0) {
        price = price.replace(/\,/gi, '');
    }

    //number contains atleast 1 , and 1 .
    if( (price.match(/\./gi) || []).length > 0 && 
        (price.match(/\,/gi) || []).length > 0) {

        if (price.replace(/\d/gi, '').charAt(0) == '.') {
            price = price.replace(/\./gi, '');
        } else {
            price = price.replace(/\,/gi, '');
        }
    }

    //replace , by .
    return parseFloat(price.replace(/\,/gi, '.'));
}

function formatPrice(price, decimals) {

    return normalizePrice(price).toFixed(decimals).replace('.', ',');
}

function getLocalePrice(price, decimals = 2) {
    return "&euro; "+formatPrice(price, decimals);
}

function inputDecimals(v, decimals = 2)
{
    v = v.toString().replace(/[^0-9.,]/g, '').replace(/(\..*?)\..*/g, '$1');
    if (v != '') {
        return parseFloat(normalizePrice(v).toFixed(decimals)).toString().replace(',', '.');
    }
    
    return v;
}

function isMoney(inputValue)
{
    if (inputValue == '' ||
        isNaN(formatPrice(inputValue))) {
        return false;
    }

    return true;
}



function updateSortOrder(element) {
    var list = [];
    let sortcol = 'sort';
    
    if (typeof element.data('sort') !== 'undefined') {
        sortcol = element.data('sort');
    }


    element.find('.row-item').each(function () {
        list.push({
            id: $(this).data('id'),
            parent_id: $(this).data('parent-id'),
        });
    });

    $.ajax({
        url: config.domain+'/'+config.app+"/functions/update_sort",
        type: 'post',
        data: {
            'type': element.closest('.card-overview').data('type'),
            'sortcol': sortcol,
            'sort': JSON.stringify(list)
        },
        success: function (data) {
            console.log(data);
        }
    });
}

export default function() { };

export {
    camelCase2SnakeCase,
    getHTML,
    uniqueId,
    replaceInHTML,
    getTemplate,
    setLoading,
    normalizePrice,
    formatPrice,
    groupBy,
    inputDecimals,
    isMoney,
    getLocalePrice
};