@extends('mail.sections.wrapper')

@section('content')

@php
	$salutation = $user->gender->value == 'male'
		?  __('mail/user/activation.label.salutation_male') 
		:  __('mail/user/activation.label.salutation_female');

@endphp

<table width="100%" border="0" cellpadding="0" cellspacing="0" backgroundcolor="#FFFFFF" style="margin: auto;">
	<tr>
		<td align="left">

			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td style="vertical-align:top">
						<h1 style="margin-top:75px;">{{ __('mail/user/activation.heading.title') }}</h1>
					</td>
					<td class="text-end">
						<img class="logo-top" style="float:right" src="{{ URL::asset('/img/logo.png') }}">
					</td>
				</tr>
				<tr>
					<td colspan="2">

                        <h3>{{ __('mail/user/activation.label.salutation') }} {{$salutation}} {{$user->getFullName()}}</h3>

						{!! __('mail/user/activation.message.intro',  [
							'link_portal' => '<a href="'.URL::to('/').'"> <u> portal.vanstraaten.com</u></a>.' ,
							'link' => '<a href="' . route('auth.activation', ['locale' => 'nl' ,'token' => $user->token]) . '">' . __('mail/user/activation.text.link') . '</a>',
						])!!}

						<br /><br />

						{!! __('mail/user/activation.message.contact_us', [
							'manager_name' => '<b><a href="mailto:<EMAIL>">Marc Hagens</a></b>'
						])!!}
						<br />
					</td>
				</tr>
			</table>

		</td>
	</tr>
	
	@include('mail.sections.footer')

</table>

@endsection