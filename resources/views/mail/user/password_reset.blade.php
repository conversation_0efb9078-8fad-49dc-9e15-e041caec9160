@extends('mail.sections.wrapper')

@section('content')

@php
	$salutation = $user->gender->value == 'male'
		?  __('mail/user/password_reset.label.salutation_male') 
		:  __('mail/user/password_reset.label.salutation_female');
@endphp

    <table width="100%" border="0" cellpadding="0" cellspacing="0" backgroundcolor="#FFFFFF" style="margin: auto;">
        <tr>
            <td align="left">

                <table width="100%" border="0" cellpadding="0" cellspacing="0">
                    <tr>
                        <td style="vertical-align:top">
                            <h1 style="margin-top:75px;">{{ __('mail/user/password_reset.heading.title') }}</h1>
                        </td>
                        <td class="text-end">
                            <img class="logo-top" style="float:right" src="{{ URL::asset('/img/logo.png') }}">
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <h3>{{ __('mail/user/password_reset.label.salutation') }} {{$salutation}} {{$user->getFullName()}}</h3>
 
                            {!! __('mail/user/password_reset.message.intro',  [
                                'link' => '<a href="' . route('auth.password.reset' , ['locale' => 'nl' ,'token' => $user->token]) . '">' . __('mail/user/password_reset.text.link') . '</a>',
                            ])!!}

                            <br /><br />

                            {!! __('mail/user/password_reset.message.contact_us', [
                                'manager_name' => '<b><a href="mailto:<EMAIL>">Marc Hagens</a></b>'
                            ])!!}
                            <br />

                        </td>
                    </tr>
                </table>
    
            </td>
        </tr>
	
	@include('mail.sections.footer')

</table>

@endsection