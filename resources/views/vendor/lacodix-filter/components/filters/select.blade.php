@php
    $multiple = $filter->mode->needsMultipleValues();
    $name = $filter->queryName();
    $classes = 'select ' . $name;
    $persistentFilters = $persistentFilters ?? collect($filter->model()->getFilters('__default'));
    
    $optionsCount = count($filter->options());
    $hasSelectedOptions = count(array_filter(Arr::wrap($persistentFilters->get($name, null)))) > 0;

    $emptyOptionTitle = method_exists($filter, 'optionEmptyTitle') 
        ? $filter->optionEmptyTitle()
        : 'All ' . Str::plural($filter->title())
    ;

    $optionsContent = $filter::$optionsContent ?? [];

    $classDisabled = $optionsCount <= 1 && !$hasSelectedOptions 
        ? ' disabled'
        : ''
    ;

    $classHasSelected = $hasSelectedOptions 
        ? ' filter-input-is-active' 
        : ''
    ; 
@endphp

<x-lacodix-filter::filters.layout
    :filter="$filter"
    :class="$classes"
>
    <select
        class="filter-input form-control selectpicker{{ $classDisabled.$classHasSelected }}"
        name="{{ $name . ($multiple ? '[]' : '') }}"
        @if (count($filter->options()) > 10)
            data-live-search="true"
        @endif
        {{-- onchange="this.form.submit()" --}}
        @if ($multiple)
            multiple
        @endif
    >
        <option value=""{{ $hasSelectedOptions === false ? ' selected' : '' }}>{{ $emptyOptionTitle }}</option>
        @foreach ($filter->options() as $key => $option)

            @php
                $selected = $multiple
                    ? in_array($option, is_array($persistentFilters->get($name, '')) 
                        ? $persistentFilters->get($name, '') 
                        : [ $persistentFilters->get($name, '') ])
                    : $persistentFilters->get($name, '') == $option
                ;
            @endphp

            <option 
                value="{{ $option }}"
                {!! ($optionsContent[$option] ?? false) !== false ? "data-content='".$optionsContent[$option]."'" : '' !!}
                {{ $selected ? ' selected' : '' }}
            >{{ is_numeric($key) ? $option : $key }}</option>
        @endforeach
    </select>
</x-lacodix-filter::filters.layout>
