@php
    $name = $filter->queryName();
    $classes = 'text ' . $name;
    $persistentFilters = $persistentFilters ?? collect($filter->model()->getFilters('__default'));
@endphp

<x-lacodix-filter::filters.layout
    :filter="$filter"
    :class="$classes"
>
    <input
        class="filter-input"
        name="{{ $name }}"
        type="text"
        {{-- onchange="this.form.submit()" --}}
        value="{{ $persistentFilters->get($name, '') }}"
    >
</x-lacodix-filter::filters.layout>