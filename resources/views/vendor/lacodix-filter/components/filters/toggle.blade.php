@php
    $name = $filter->queryName();
    $classes = 'boolean ' . $name;
    $persistentFilters = $persistentFilters ?? collect($filter->model()->getFilters('__default'));
@endphp

<x-lacodix-filter::filters.layout
    :filter="$filter"
    :class="$classes"
>

    <div class="form-check form-switch mb-0">
        <input 
            id="{{ $name }}" 
            name="{{ $name }}" 
            class="form-check-input" 
            type="checkbox" 
            value="1" 
            {{ $persistentFilters->get($name, false) ? 'checked' : '' }}
        >

        <input 
            type="hidden" 
            name="_{{ $name }}" 
            class="form-check-input-hidden" 
            value="{{ $persistentFilters->get($name, false) ? 1 : 0 }}"
        > 

        <label 
            class="form-label my-0" 
            for="{{ $name }}"
        >{{ $filter->title() }}</label>
    </div>

</x-lacodix-filter::filters.layout>