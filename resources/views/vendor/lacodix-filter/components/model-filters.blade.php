@props([
    'model',
    'method' => 'get',
    'group' => '__default',
])

@php
    if (is_string($model)) {
        $model = new $model();
    }

    $persistentFilters = collect($model->getFilters('__default'))
        ->flatten()
        ->filter()
        ->isNotEmpty()
    ;
@endphp

<div class="filters mb-3">
    <form {{ $attributes->merge(['method' => $method]) }}>
        
        @csrf
        
        <div class="row d-flex align-items-center">

            <div class="col-auto">
                <h5 class="p-0 m-0">Search:</h5>
            </div>
   
            @foreach ($model->filterInstances($group) as $filter)
                <div class="col-auto">
                    <x-dynamic-component
                        :component="$filter->component()"
                        :filter="$filter"
                    />
                </div>
            @endforeach

            <div class="col-auto ms-auto">

                @if ($persistentFilters)
                    <button type="submit" name="filters_reset" class="btn btn-info btn-sm me-1" title="Reset filters">
                        <x-admin::icon icon="filter-circle-xmark" />
                    </button>
                @endif
                
                <button type="submit" name="filters" class="btn btn-primary btn-sm"><x-admin::icon icon="filter" /> Filter</button>
            </div>
        </div>

        {{ $footer ?? '' }}
    </form>
</div>