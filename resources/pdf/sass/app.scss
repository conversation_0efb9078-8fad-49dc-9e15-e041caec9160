@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@600;700;800&family=Poppins:wght@400;600;700&display=swap');

@import "../../../node_modules/bootstrap/scss/_functions";
@import "../../../node_modules/bootstrap/scss/variables";
@import "../../../node_modules/bootstrap/scss/variables-dark";

@import 'variable.scss';

@import "../../../node_modules/bootstrap/scss/maps";
@import "../../../node_modules/bootstrap/scss/mixins";
@import "../../../node_modules/bootstrap/scss/utilities";
@import "../../../node_modules/bootstrap/scss/root";

// Layout & components
@import "../../../node_modules/bootstrap/scss/reboot";
@import "../../../node_modules/bootstrap/scss/type";
@import "../../../node_modules/bootstrap/scss/images";
@import "../../../node_modules/bootstrap/scss/containers";
@import "../../../node_modules/bootstrap/scss/grid";
@import "../../../node_modules/bootstrap/scss/tables";
@import "../../../node_modules/bootstrap/scss/forms";
@import "../../../node_modules/bootstrap/scss/buttons";
@import "../../../node_modules/bootstrap/scss/transitions";
@import "../../../node_modules/bootstrap/scss/dropdown";
@import "../../../node_modules/bootstrap/scss/button-group";
@import "../../../node_modules/bootstrap/scss/nav";
@import "../../../node_modules/bootstrap/scss/navbar";
@import "../../../node_modules/bootstrap/scss/card";
@import "../../../node_modules/bootstrap/scss/accordion";
@import "../../../node_modules/bootstrap/scss/breadcrumb";
@import "../../../node_modules/bootstrap/scss/pagination";
@import "../../../node_modules/bootstrap/scss/badge";
@import "../../../node_modules/bootstrap/scss/alert";
@import "../../../node_modules/bootstrap/scss/progress";
@import "../../../node_modules/bootstrap/scss/list-group";
@import "../../../node_modules/bootstrap/scss/close";
@import "../../../node_modules/bootstrap/scss/modal";
@import "../../../node_modules/bootstrap/scss/tooltip";
@import "../../../node_modules/bootstrap/scss/popover";
@import "../../../node_modules/bootstrap/scss/carousel";
//@import "node_modules/bootstrap/scss/spinners";
@import "../../../node_modules/bootstrap/scss/offcanvas";
@import "../../../node_modules/bootstrap/scss/placeholders";
@import "../../../node_modules/bootstrap/scss/helpers";
@import "../../../node_modules/bootstrap/scss/utilities/api";

@import 'documents/purchaseorder';


* {
	box-sizing: border-box;
}

html, body {
	box-sizing: border-box;
	font-size: 14px;
    line-height: 1.6em; 
    background: gray;
    color: #000 !important;
    // font-family: 'Open Sans' !important;
    // font-weight: normal !important;

    @media not print {
        width: 100% !important;
        max-width: 100% !important;
        
        .wrapper {
            zoom: 0.9;
        }
    }
}


.overview {
    .overview-header {
        .column {
            font-weight: bold;
        }
    }
}


a {
    color: #00afef !important;
    text-decoration: none !important;
}

.page-content {
    margin:0px;
    padding:0px;
    width: 100%;
    height: 100%;
}

.page-footer {
    flex: 1;
    display: flex;
    align-items: flex-end;
    flex-direction: row;
}


.font-xs {
	font-size: 11px;
}
.font-sm {
	font-size: 13px;
}
.font-md {
	font-size: 15px;
}
.font-lg {
	font-size: 17px;
}
.font-xl {
	font-size: 19px;
}

.font-14 {
	font-size: 14px !important;
}

.color-green {
	color:#41b57a;
}

.color-orange {
	color:#fa6819;
}

@media print {
    .no-print {
        display: none !important;
        visibility: hidden !important;
    }
}

.panel-controls {
    position: fixed;
    z-index: 100000;
    top: 25px;
    right: 25px;
    min-width: 300px;
}