
.description-text-segment {
    ul, p {
        margin-bottom: 5px;
    }
}

.line-height-fixed {
    line-height: 100% !important;
}



body:not(.production-overview, .eproof) {
    div.page {
        size: 21cm 29.65cm;

        display: flex;
        flex-flow: column;

        /* background: yellow; */
        margin:0px;
        padding-top:15mm;  
        padding-left:15mm;
        padding-right:15mm; 
        padding-bottom:12mm;
        height: 100%;
        width: 100%;   
        width:1000px;
        height: 1412px;
        position: relative;  
        background: white;

        @media not print {
            float: left;
            margin-left: 50px; 
            margin-top:15px;
            margin-bottom:15px;
            page-break-before: none;
            page-break-inside: none;
            zoom:0.9;
        }
    }
}

body.production-overview {

    div.page {
        size: a4 landscape ;

        display: flex;
        flex-flow: column;

        /* background: yellow; */
        margin:0px;
        padding-top:10mm;  
        padding-left:7mm;
        padding-right:7mm; 
        padding-bottom:5mm;
        
        width: 1415px;
        height: 999px;
        position: relative;  
        background: white;
 
        @media not print {
            float: left;
            margin-left: 50px; 
            margin-top:15px;
            margin-bottom:15px;
            page-break-before: none;
            page-break-inside: none;
            zoom:0.9;
        }
    }
}

.page-attachment {
    padding: 0px !important;
}