
$enable-negative-margins: true;

$theme-colors: (
  "primary": #FA6819,//orange
  "secondary": #009FE3,//blue
  "tertiary": #41B57A,//green
  "select": #000000//green
);

$body-color: #111;

$font-weight-bold: 700 !default;

// // Default
 $style-default-bg: #FCFAF8;


//  $font-family-sans-serif:      system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", "Liberation Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" !default;
//  $font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !default;
//  // stylelint-enable value-keyword-case
//  $font-family-base:            var(--#{$prefix}font-sans-serif) !default;
//  $font-family-code:            var(--#{$prefix}font-monospace) !default;

// // Palette font families
$font-family-sans-serif:'Open Sans';
$heading-font-family: 'Poppins';
$title-font-family: 'Poppins';
$text-font-family: 'Poppins';
$input-btn-font-family: 'Poppins';
$btn-font-family: 'Poppins';
$input-font-family: 'Poppins';

$border-color: #ddd;
$box-shadow: 0px 0px 12px #0000000D;
$border-radius: 4px; //for standard border radius used on class .rounded

$h1-font-size: 34px;
$h1-font-size-mobile: 26px;
$h2-font-size: 30px;
$h2-font-size-mobile: 24px;
$h3-font-size: 26px;
$h3-font-size-mobile: 22px;
$h4-font-size: 22px;
$h4-font-size-mobile: 20px;
$h5-font-size: 20px;
$h5-font-size-mobile: 18px;
$h6-font-size: 18px;
$h6-font-size-mobile: 16px;