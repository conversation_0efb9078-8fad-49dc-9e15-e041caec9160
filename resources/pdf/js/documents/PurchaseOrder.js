 
function createItem(data, depth = 0) 
{
    let item = getTemplate('item'),
        unitType = `${data.unit_type_warehouse}`;

    if (data.unit_type_sales !== null) {
        unitType += ` (${parseFloat(data.quantity_sales)} ${data.unit_type_sales})`;
    }
   
    item.find('.item-code').html(data.item_code);
    item.find('.name').html(data.name);
    item.find('.quantity').html(data.quantity_warehouse+'x');
    item.find('.unit-type').html(unitType);

    item.find('.price-unit').html(getLocalePrice(data.price_unit_type_warehouse_excl, 2));
    item.find('.price-total').html(getLocalePrice(data.price_unit_type_warehouse_excl * data.quantity_warehouse, 2));

    return item;
}

function repositionItemIfOverlapping(item = null)
{
    let page = getCurrentPage();

    //page exists
    if (page === false) {
        return;
    }

    //get last item if not present
    if (item === null) {
        if ((item = page.find('.items-container .row-item:last-child')).length == 0) {
            return;
        }
    }

    if (getDistanceFromFooter(item) < 30) {
        page = createPage();
        page.find('.items-container').append(item.clone());
        item.remove();
    }
}

function tranformLongItem(itemElement) 
{
    let page = getCurrentPage(),
        descriptionSegments = [],
        attempts = 0,
        offset, newItemElement
    ;

    //page exists, is itemElement overlapping bbox, has itemElement segmentable elements
    if (page === false ||
        (offset = getDistanceFromFooter(itemElement)) >= 30 ||
        itemElement.find('.description-text').length == 0) {
        return;
    }

    //remove segments one-by-one untill itemElement fits in container
    while (attempts < 10 && offset < 30) {
        descriptionSegments.push($(itemElement.find('.description-text .row:last-child')[0].outerHTML));
        itemElement.find('.description-text .row:last-child').remove();
        offset = getDistanceFromFooter(itemElement);
        ++attempts;
    }

    //clone itemElement, append previous removed segments into cloned itemElement
    newItemElement = itemElement.clone();
    rowItemNew.find('.description-text .row').remove();
    rowItemNew.find('.description-text').append(descriptionSegments.reverse());

    //create new page, add newItemElement to page
    page = createPage();    
    page.find('.items-container').append(rowItemNew);
}

function addPagination()
{
    $('.wrapper .page').each(function(idx) {
        $(this).find('.page-num').html((++idx)+' / '+$('.wrapper .page').length);
    });
}

$(document).ready(function(){

    let i = 0,
        page, item
    ;

    while (i < items.length) {

        page = getCurrentPage();

        if (page === false) {
            page = createPage();
        }

        item = createItem(items[i]);

        page.find('.items-container').append(item);

        tranformLongItem(item);

        repositionItemIfOverlapping(item);

        ++i;
    } 

    page.find('.overview-footer').append(getTemplate('overview-totals'));

    $('.wrapper .page:not(:last-child) .last-page-only').remove();
    page.find('.last-page-only').show();
    
    //new page if addresses is to close to page-footer
    if (page.find('.items-container .row-item').length > 1 && 
        getDistanceFromFooter(page.find('.last-page-only')) < 30) {

        elm = page.find('.items-container .item-top:last').clone();
        overviewFooter = page.find('.overview-footer').clone();
        lastPageOnly = page.find('.last-page-only').clone();

        page.find('.items-container .item-top:last').remove();
        page.find('.overview-footer').remove();
        page.find('.last-page-only').remove();
    
        page = createPage();

        page.find('.items-container').append(elm);
        page.find('.overview-footer').replaceWith(overviewFooter);
        page.find('.last-page-only').replaceWith(lastPageOnly);
    }

    page.find('.last-page-only').addClass('d-flex flex-column flex-grow-1');

    addPagination(); 
});