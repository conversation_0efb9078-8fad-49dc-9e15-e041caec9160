import '../sass/app.scss'; 
import '@fortawesome/fontawesome-free/css/all.min.css';

import jQuery from 'jquery';
import * as bootstrap from 'bootstrap';

window.$ = window.jQuery = jQuery;
window.bootstrap = bootstrap;

import * as functionsShared from './../../shared/js/functions';

import * as functions from './functions';

Object.keys(functionsShared).forEach((key) => {
    window[key] = functionsShared[key];
});

Object.keys(functions).forEach((key) => {
    window[key] = functions[key];
});

