function getCurrentPage()
{
    return $('.wrapper .page').length > 0
        ? $('.wrapper .page:last-child')
        : false
    ;
}

function createPage(template = 'page')
{
    $('.wrapper .page:last-child .first-page-only').remove();
    $('.wrapper').append(getTemplate(template));
    return $('.wrapper .page:last-child');
}

function getDistanceFromFooter(element)
{
    return parseInt(
        $('.wrapper .page:last-child .page-footer')[0].getBoundingClientRect().top - 
        element[0].getBoundingClientRect().bottom
    );
} 

export default function() { };

export {
    getCurrentPage,
    createPage,
    getDistanceFromFooter
};