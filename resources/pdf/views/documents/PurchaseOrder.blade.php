@extends('layouts.default')

@section('name', 'purchaseorder')

@section('content')
@endsection

@section('styles')
    @page {
        size: 21cm 29.7cm;
        margin: 0mm;
        padding: 0mm;
    }
@endsection

@section('scripts')
    let items = <?=$model->items->toJson();?>;
@endsection

@section('templates')

    <div class="template item row row-item item-top py-1" data-name="item">
        <div class="col-2 column item-code"></div>
        <div class="col column name"></div>
        <div class="col-1 column text-end quantity"></div>
        <div class="col-2 column unit-type"></div>
        <div class="col-3">
            <div class="row">
                <div class="col-6 column text-end price-unit"></div>
                <div class="col-6 column text-end price-total"></div>
            </div>
        </div>
    </div>

    <div class="template" data-name="overview-totals">

        <?php
        $totalsClass = [
            'left' => 'col-2 offset-8',
            'right' => 'col-2 text-end pr-0'
        ];
        ?>
        <div class="totals mt-3">

            @isset($model->total_items_excl)
                <div class="row py-1">    
                    <div class="<?=$totalsClass['left'];?> border-top">{{ __('.content.overview.footer.label.price_total_items_excl') }}</div>
                    <div class="<?=$totalsClass['right'];?> border-top">
                        <span class="price total-items">@money($model->total_items_excl)</span>
                    </div>
                </div>
            @endisset

            @isset($model->total_shipping_excl)
                <div class="row py-1">                
                    <div class="<?=$totalsClass['left'];?>">{{ __('.content.overview.footer.label.price_total_shippingcosts_excl') }}</div>
                    <div class="<?=$totalsClass['right'];?>">
                        <span class="price total-shipping">@money($model->total_shipping_excl)</span>
                    </div>
                </div>
            @endisset
            
            @isset($model->total_excl)
                <div class="row py-1">                
                    <div class="<?=$totalsClass['left'];?>">{{ __('.content.overview.footer.label.price_total_excl') }}</div>
                    <div class="<?=$totalsClass['right'];?>">
                        <span class="price total-shipping">@money($model->total_excl)</span>
                    </div>
                </div>
            @endisset
            
            @isset($model->total_tax)
                <div class="row py-1">                
                    <div class="<?=$totalsClass['left'];?>">{{ __('.content.overview.footer.label.price_total_tax') }}</div>
                    <div class="<?=$totalsClass['right'];?>">
                        <span class="price total-tax">@money($model->total_tax)</span>
                    </div>
                </div>
            @endisset

            @isset($model->total_incl)
                <div class="row py-1">                
                    <div class="<?=$totalsClass['left'];?> border-top fw-bold">{{ __('.content.overview.footer.label.price_total_incl') }}</div>
                    <div class="<?=$totalsClass['right'];?> border-top fw-bold">
                        <span class="price total-tax">@money($model->total_incl)</span>
                    </div>
                </div>
            @endisset
    
        </div>
       
    </div>

    <div class="template" data-name="page">

        <div class="page-content">

            <div class="container-fluid h-100 d-flex flex-column">

                <div class="row">
                    <div class="col">

                        <h1 class="mb-5 text-uppercase">{{ __('.header.heading.title') }}</h1>

                        <div class="row">
                           
                            <div class="col-8">
                        
                                <div class="row">
                                    <div class="col-5 fw-bold">{{ __('.header.panel.information.label.contact') }}:</div>
                                    <div class="col">{{ $model->company->name }}</div>
                                </div>
                                <div class="row">
                                    <div class="col-5 fw-bold">{{ __('.header.panel.information.label.description') }}:</div>
                                    <div class="col">{{ $model->description }}</div>
                                </div>
                                <div class="row">
                                    <div class="col-5 fw-bold">{{ __('.header.panel.information.label.order_number') }}:</div>
                                    <div class="col"><span class="border-bottom">{{ $model->order_number }}</span></div>
                                </div>
                                <div class="row">
                                    <div class="col-5 fw-bold">{{ __('.header.panel.information.label.order_date') }}:</div>
                                    <div class="col">{{ date("Y-m-d", strtotime($model->sent_at ?? date("Y-m-d"))) }}</div>
                                </div>
                                <div class="row">
                                    <div class="col-5 fw-bold">{{ __('.header.panel.information.label.delivery_date') }}:</div>
                                    <div class="col">
                                        @isset($model->delivery_at) 
                                        {{ date("Y-m-d", strtotime($model->delivery_at)) }}
                                        @else
                                            -
                                        @endisset
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-5 fw-bold">{{ __('.header.panel.information.label.delivery_method') }}:</div>
                                    <div class="col">Delivery method</div>
                                </div>
                                <div class="row">
                                    <div class="col-5 fw-bold">{{ __('.header.panel.information.label.payment_condition') }}:</div>
                                    <div class="col">Payment condition</div>
                                </div>
                                
                                @if (!is_null($model->comment))
                                    <div class="row">
                                        <div class="col-5 fw-bold">{{ __('.header.panel.information.label.comments') }}:</div>
                                        <div class="col">{{ $model->comment }}</div>
                                    </div>
                                @endif

                            </div>

                            <div class="col-auto">

                                <address>
                                    {{ $model->company->name }}
                                    {!! $model->company->addresses->first()->label !!}
                                </address>                      

                            </div>
                            
                        </div>
                    </div>
                    <div class="col-auto text-end">
                        <div class="row">
                            <div class="col-12">
                                <img src="{{ asset('img/logo/pdf_1x1.jpg') }}" />
                            </div>
                            <div class="col-12 text-end pe-3 mt-2">
                                {{ __('.header.label.page') }} <span class="page-num"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">

                    <div class="col-12">

                        <div class="overview mt-3 container-fluid">

                            <div class="overview-header row py-1">  
                                <div class="col-2 column">{{ __('.content.overview.header.label.itemcode') }}</div>
                                <div class="col column">{{ __('.content.overview.header.label.description') }}</div>
                                <div class="col-1 column text-end">{{ __('.content.overview.header.label.quantity') }}</div>
                                <div class="col-2 column">{{ __('.content.overview.header.label.unit_type') }}</div>
                                <div class="col-3">
                                    <div class="row">
                                        <div class="col-6 column column-small text-end price_unit">{{ __('.content.overview.header.label.price_per_unit') }}</div>
                                        <div class="col-6 column text-end price_total">{{ __('.content.overview.header.label.price_total_incl') }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="overview-body items-container">

                            </div>
                            <div class="overview-footer"></div>
                        </div>

                    </div>

                </div>

                <div class="last-page-only collapse collapsed">

                    <div class="row mt-5 addresses">

                        <div class="col-6 mt-3 text-start">
                            <div>
                                <h2 class="font-lg fw-bold">{{ __('.content.heading.invoice_address') }}:</h2>
                                <address>
                                    {!! $model->addresses->where('type', \App\Enums\AddressType::INVOICE)->first()->label !!}
                                </address>
                            </div>
                        </div>

                        <div class="col-6 mt-3 text-end">
                            <div>
                                <h2 class="font-lg fw-bold">{{ __('.content.heading.delivery_address') }}:</h2>
                                <address>
                                    {!! $model->addresses->where('type', \App\Enums\AddressType::DELIVERY)->first()->label !!}
                                </address>
                            </div>
                        </div>
                    </div>

                    <div class="row flex-grow-1 align-items-center">
                        <div class="col-12 text-center text-center pt-5 px-5 fw-bold">
                            
                        </div>
                    </div>
                </div>

            </div>
        </div>

        @include('sections.footer')

    </div>

@endsection