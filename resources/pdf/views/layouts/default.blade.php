<!doctype html>
<html lang="{{config('locales.'.App::getLocale())['locale']}}">
<head>

	<title>{{ config('app.name', env('APP_NAME')) }}</title>
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
	<meta charset="UTF-8">
	<meta name="format-detection" content="telephone=no">
	<meta name="author" content="Code31 B.V.">

    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">

    <link rel="shortcut icon" type="image/png" href="<?=asset('img/favicon/120x120.png');?>" defer>
    <link rel="apple-touch-icon-precomposed" sizes="114x114" href="<?=asset('img/favicon/114x114.png');?>" defer>
    <link rel="apple-touch-icon-precomposed" sizes="120x120" href="<?=asset('img/favicon/120x120.png');?>" defer>
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="<?=asset('img/favicon/144x144.png');?>" defer>
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="<?=asset('img/favicon/152x152.png');?>" defer>
	<link rel="apple-touch-icon-precomposed" sizes="180x180" href="<?=asset('img/favicon/180x180.png');?>" defer>

	<link rel="preconnect" href="fonts.gstatic.com" crossorigin> 
	<link rel="preconnect" href="fonts.googleapis.com" crossorigin> 

    <link rel="modulepreload" href="/build/vendors/jquery-ui/dist/jquery-ui.min.js">

    @foreach ($assets_static as $asset) 
        <link rel="modulepreload" href="/build{{ $asset }}">
    @endforeach

    @vite([
        'resources/pdf/js/app.js',
    ])

    <script type="module" src="/build/vendors/jquery-ui/dist/jquery-ui.min.js"></script>

    @foreach ($assets_static as $asset) 
        <script type="module" src="/build{{ $asset }}"></script>
    @endforeach
    
    @vite($assets)

    @hasSection("styles")
        <style>
        @yield('styles')
        </style>
    @endif

</head>
<body class="@yield('name', '')">
   
    <div class="wrapper"></div>

     @hasSection('templates')
        <div class="templates d-none">
            @yield('templates')
        </div>
    @endif  

    @hasSection('scripts')
        <script type="text/javascript">
            @yield('scripts')
        </script>
    @endif

</body>
</html>