#
# generic mappings, specific territories get mapped to these
#
# postcode before city
generic1: &generic1 |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{state}}} {{/first}}
        {{{country}}}

# postcode after city
generic2: &generic2 |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{county}}} {{/first}} {{{postcode}}}
        {{#first}} {{{country}}} || {{{state}}} {{/first}}

# postcode before city
generic3: &generic3 |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{state}}} {{/first}}
        {{{country}}}

# postcode after state
generic4: &generic4 |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}}, {{#first}} {{{state_code}}} || {{{state}}} {{/first}} {{{postcode}}}
        {{{country}}}

# no postcode
generic5: &generic5 |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} 
        {{#first}} {{{state_district}}} || {{{state}}} {{/first}}
        {{{country}}}

# no postcode, county
generic6: &generic6 |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} 
        {{{county}}} 
        {{{country}}}

# city, postcode
generic7: &generic7 |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}} 
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}, {{{postcode}}} 
        {{{country}}}

# postcode and county
generic8: &generic8 |
        {{{attention}}}
        {{{house}}}
        {{{road}}}, {{{house_number}}} 
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} {{#first}} {{{county_code}}} || {{{county}}} {{/first}}
        {{{country}}}

generic9: &generic9 |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{state_district}}} {{/first}}
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{state}}} {{/first}}
        {{{country}}}

generic10: &generic10 |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{{suburb}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{{state}}}
        {{{country}}}
        {{{postcode}}}

generic11: &generic11 |
        {{{country}}}
        {{{state}}}
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{{suburb}}}
        {{{road}}}, {{{house_number}}}
        {{{house}}}
        {{{attention}}}

# city - postcode
generic12: &generic12 |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}}, {{{road}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{state_district}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} - {{{postcode}}}
        {{{state}}}
        {{{country}}}

generic13: &generic13 |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{suburb}}} || {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} || {{{region}}} {{/first}} {{#first}} {{{state_code}}} || {{{state}}} {{/first}} {{{postcode}}}
        {{{country}}}

# postcode and state
generic14: &generic14 |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{state_district}}} {{/first}}
        {{{state}}}
        {{{country}}}

# postcode and comma before house number
generic15: &generic15 |
        {{{attention}}}
        {{{house}}}
        {{{road}}}, {{{house_number}}}
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{state}}} || {{{county}}} {{/first}}
        {{{country}}}

# no postcode, no state, just city
generic16: &generic16 |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{county}}} || {{{state_district}}} || {{{state}}} {{/first}} 
        {{{country}}}

# no postcode, no state, just city
generic17: &generic17 |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}} 
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{county}}} || {{{state_district}}} || {{{state}}} {{/first}} 
        {{{country}}}

# no postcode, just city comma after house number
generic18: &generic18 |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}}, {{{road}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} || {{{state}}} {{/first}} 
        {{{country}}}

# suburb and postcode after city
generic19: &generic19 |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} {{{postcode}}} 
        {{{country}}}

# suburb and postcode after city
generic20: &generic20 |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} {{{postcode}}} 
        {{{country}}}

# suburb and city, no postcode
generic21: &generic21 |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}} 
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{state}}} {{/first}} 
        {{{country}}}

# comma after housenumber, postcode before city
generic22: &generic22 |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}}, {{{road}}}
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{state}}} {{/first}}
        {{{country}}}

fallback1: &fallback1 |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{#first}} {{{county}}} || {{{state_district}}} || {{{state}}} {{/first}}
        {{{country}}}

fallback2: &fallback2 |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{{suburb}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{county}}} || {{{island}}} {{/first}}, {{#first}} {{{state_code}}} || {{{state}}} {{/first}}
        {{{country}}}

fallback3: &fallback3 |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{{suburb}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{{county}}}
        {{{state}}}
        {{{country}}}

fallback4: &fallback4 |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{{suburb}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{#first}} {{{state}}} || {{{county}}} {{/first}}
        {{{country}}}

default:
    address_template: *generic1
    fallback_template: *fallback1
    
# country / territory specific mappings
# please keep in alpha order by country code
#


# Andorra
AD:
    address_template: *generic3

# United Arab Emirates
AE:
    address_template: *generic5

# Angola
AF:
    address_template: *generic21

# Antigua and Barbuda
AG:
    address_template: *generic16

# Anguilla
AI:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}} 
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{{postcode}}} {{{country}}}

# Albania
AL:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}} 
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}}
        {{{country}}}
    postformat_replace:
        # fix the postcode to add - after numbers
        - ["\n(\\d{4}) ([^,]*)\n","\n$1-$2\n"]

# Armenia
AM:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{{postcode}}} 
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{#first}} {{{state_district}}} || {{{state}}} {{/first}}
        {{{country}}}

# Angola
AO:
    address_template: *generic7

# Antarctica
AQ:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{#first}} {{{country}}} || {{{continent}}} {{/first}}

# Argentina
AR: 
    address_template: *generic9
    replace:
        - ["^Autonomous City of ",""]
    postformat_replace:
        # fix the postcode to make it \w\d\d\d\d \w\w\w
        - ["\n(\\w\\d{4})(\\w{3}) ","\n$1 $2 "]

# American Samoa
AS: 
    use_country: US
    change_country: United States of America
    add_component: state=American Samoa

# Austria
AT: 
    address_template: *generic1

# Australia
AU: 
    address_template: *generic13

# Aruba
AW: 
    address_template: *generic17

# Åland Islands, part of Finnland
AX:
    use_country: FI
    change_country: Åland, Finland

# Azerbaijan
AZ:
    address_template: *generic3

# Bosnia
BA:
    address_template: *generic1

# Barbados
BB:
    address_template: *generic16

# Bangladesh
BD:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{state_district}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} - {{{postcode}}}
        {{{country}}}

# Belgium
BE: 
    address_template: *generic1

# Burkina Faso
BF:
    address_template: *generic6

# Bulgaria
BG:
    address_template: *generic9

# Bahrain
BH:
    address_template: *generic2

# Burundi
BI:
    address_template: *generic17

# Benin
BJ:
    address_template: *generic18

# Saint Barthélemy - same as FR
BL:
    use_country: FR
    change_country: Saint-Barthélemy, France     

# Bermuda
BM:
    address_template: *generic2

# Brunei
BN:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}}, {{{road}}} 
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} 
        {{#first}} {{{county}}} || {{{state_district}}} || {{{state}}} {{/first}} {{{postcode}}} 
        {{{country}}}


# Bolivia
BO:
    address_template: *generic17
    replace:
        - ["^Municipio Nuestra Senora de ",""]

# Dutch Caribbean / Bonaire
BQ:
    use_country: NL
    change_country: Caribbean Netherlands

# Brazil
BR:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}}, {{{house_number}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}} - {{#first}} {{{state_code}}} || {{{state}}} {{/first}}
        {{{postcode}}}
        {{{country}}}
    postformat_replace:
        - ["\\b(\\d{5})(\\d{3})\\b","$1-$2"]

# Bahamas
BS:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}} 
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} 
        {{{county}}} 
        {{{country}}}

# Bhutan
BT:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}, {{{house}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{state}}} {{/first}} {{{postcode}}} 
        {{{country}}}

# Bouvet Island
BV:
    use_country: "NO"
    change_country: Bouvet Island, Norway

# Botswana
BW:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}} 
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} 
        {{{country}}}

# Belarus
BY:
    address_template: *generic11

# Belize
BZ:
    address_template: *generic16

# Canada
CA: 
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{#first}} {{{house_number}}} {{{road}}} || {{{suburb}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}}, {{#first}} {{{state_code}}} || {{{state}}} {{/first}} {{{postcode}}}
        {{{country}}}
    fallback_template: |
        {{{attention}}}
        {{{house}}}
        {{#first}} {{{house_number}}} {{{road}}} || {{{suburb}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}}, {{#first}} {{{state_code}}} || {{{state}}} {{/first}} {{{postcode}}}
        {{{country}}}
    postformat_replace:
        # fix the postcode to make it \w\w\w \w\w\w
        - [" (\\w{2}) (\\w{3})(\\w{3})\n"," $1 $2 $3\n"]


# Cocos (Keeling) Islands
CC:
    use_country: AU
    change_country: Australia

# Democratic Republic of the Congo
CD:
    address_template: *generic18

# Central African Republic
CF:
    address_template: *generic17

# Republic of the Congo / Congo-Brazzaville
CG:
    address_template: *generic18

# Switzerland
CH: 
    address_template: *generic1

# Côte d'Ivoire
CI: 
    address_template: *generic16

# Cook Islands
CK: 
    address_template: *generic16

# Chile
CL: 
    address_template: *generic1
    postformat_replace:
        # fix the postcode to make it \d\d\d \d\d\d\d
        - ["\n(\\d{3})(\\d{4}) ","\n$1 $2 "]

# Cameroon
CM: 
    address_template: *generic17

# China
CN:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{county}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}} {{#first}} {{{state_code}}} || {{{state}}} {{/first}} {{{postcode}}} 
        {{{country}}}

# China - English
CN_en:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{county}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}} {{#first}} {{{state_code}}} || {{{state}}} {{/first}} {{{postcode}}} 
        {{{country}}}

# China - Chinese
CN_zh:
    address_template: |
        {{{country}}}
        {{{postcode}}} 
        {{#first}} {{{state_code}}} || {{{state}}} {{/first}}
        {{#first}} {{{state_district}}} || {{{county}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{road}}}
        {{{house_number}}} 
        {{{house}}}
        {{{attention}}}

# Colombia
CO:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}}, {{#first}} {{{state_code}}} || {{{state}}} {{/first}}
        {{{country}}}
    postformat_replace:
        - ["Bogota, Bogota","Bogota"]

# Costa Rica
CR:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{{state}}}, {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}}, {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{postcode}}} {{{country}}}

# Cuba
CU:
    address_template: *generic7

# Cape Verde
CV:
    address_template: *generic1
    postformat_replace:
        # fix the postcode to add - after numbers
        - ["\n(\\d{4}) ([^,]*)\n","\n$1-$2\n"]

# Curaçao
CW: 
    address_template: *generic17

# Christmas Island - same as Australia
CX: 
    use_country: AU
    add_component: state=Christmas Island
    change_country: Australia

# Cyprus
CY: 
    address_template: *generic1

# Czech Republic
CZ: 
    address_template: *generic1
    postformat_replace:
        # fix the postcode to make it \d\d\d \d\d 
        - ["\n(\\d{3})(\\d{2}) ","\n$1 $2 "]

# Germany
DE:
    address_template: *generic1
    fallback_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{county}}} {{/first}}
        {{#first}} {{{state}}} || {{{state_district}}} {{/first}}
        {{{country}}}
    
    replace:
        - ["^Stadtteil ",""]
        - ["^Stadtbezirk (\\d+)",""]
        - ["^Gemeinde ",""]
        - ["^Landkreis ",""]
        - ["^Kreis ",""]
        - ["^Grenze ",""]
        - ["^Free State of ",""]
        - ["^Freistaat ",""]
        - ["^Regierungsbezirk ",""]
        - ["^Gemeindefreies Gebiet ",""]
        - ["city=Alt-Berlin","Berlin"]
    postformat_replace:
        - ["Berlin\nBerlin","Berlin"]
        - ["Bremen\nBremen","Bremen"]
        - ["Hamburg\nHamburg","Hamburg"]

# Djibouti
DJ:
    address_template: *generic16
    replace:
        - ["city=Djibouti","Djibouti-Ville"]

# Denmark
DK:
    address_template: *generic1

# Dominica
DM:
    address_template: *generic16

# Dominican Republic
DO:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}} 
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}, {{{state}}} 
        {{{postcode}}} 
        {{{country}}}
    postformat_replace:
        - [", Distrito Nacional",", DN"]



# Algeria
DZ:
    address_template: *generic3

# Ecuador
EC:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{{postcode}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}}
        {{{country}}}

# Egypt
EG:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} 
        {{{postcode}}} 
        {{{country}}}

# Estonia
EE:
    address_template: *generic1

# Western Sahara
EH:
    address_template: *generic17

# Eritrea
ER:
    address_template: *generic17

# Spain
ES:
    address_template: *generic15
    fallback_template: *fallback4

# Ethiopia
ET:
    address_template: *generic1

# Finnland
FI: 
    address_template: *generic1

# Fiji
FJ: 
    address_template: *generic16

# Falkland Islands
FK: 
    use_country: GB
    change_country: United Kingdom
    add_component: county=Falkland Islands

# Federated States of Micronesia
FM: 
    use_country: US
    change_country: United States of America
    add_component: state=Micronesia

# Faroe Islands 
FO: 
    address_template: *generic1
    postformat_replace:
        - ["Territorial waters of Faroe Islands","Faroe Islands"]

# France
FR:
    address_template: *generic3
    replace:
        - ["Polynésie française, Îles du Vent \\(eaux territoriales\\)","Polynésie française"]
        - ["France, Mayotte \\(eaux territoriales\\)","Mayotte, France"]
        - ["France, La Réunion \\(eaux territoriales\\)","La Réunion, France"]
        - ["Grande Terre et récifs d'Entrecasteaux",""]
        - ["France, Nouvelle-Calédonie","Nouvelle-Calédonie, France"]
        - ["\\(eaux territoriales\\)",""]

# Gabon
GA:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} || {{{village}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{county}}} || {{{state_district}}} || {{{state}}} {{/first}}
        {{{country}}}

GB:
    address_template: *generic2
    fallback_template: *fallback3
    replace:
        - ["^Borough of ",""]
        - ["^County( of)? ",""]
        - ["^Parish of ",""]
        - ["^Central ",""]
        - ["^Greater London","London"]
        - ["^London Borough of .+","London"]
        - ["Royal Borough of ",""]
    postformat_replace:
        - ["London, London","London"]
        - ["London, Greater London","London"]
        - ["City of Westminster","London"]
        - [", United Kingdom$","\nUnited Kingdom"]
        - ["London\nEngland\nUnited Kingdom","London\nUnited Kingdom"]

# Grenada
GD:
    address_template: *generic17

# Georgia
GE:
    address_template: *generic1

# French Guiana - same as FR
GF:
    use_country: FR
    change_country: France     

# Guernsey - same format as UK, but not part of UK
GG:
    use_country: GB
    change_country: Guernsey, Channel Islands

# Ghana
GH:
    address_template: *generic16

# Gibraltar
GI: 
    address_template: *generic16

# Greenland
GL:
    address_template: *generic1

# The Gambia
GM:
    address_template: *generic16

# Guinea
GN:
    address_template: *generic14

# Guadeloupe - same as FR
GP:
    use_country: FR
    change_country: Guadeloupe, France     

# Equatorial Guinea
GQ: 
    address_template: *generic17

# Greece
GR:
    address_template: *generic1
    postformat_replace:
        # fix the postcode to make it \d\d\d \d\d
        - ["\n(\\d{3})(\\d{2}) ","\n$1 $2 "]

# South Georgia and the South Sandwich Islands - same as UK
GS:
    use_country: GB
    change_country: United Kingdom
    add_component: county=South Georgia

# Guatemala
GT:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{{postcode}}}-{{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{state}}} {{/first}}
        {{{country}}}
    postformat_replace:
        - ["\n(\\d{5})- ","\n$1-"]
        - ["\n -","\n"]

# Guam
GU: 
    use_country: US
    change_country: United States of America
    add_component: state=Guam

# Guinea-Bissau
GW:
    address_template: *generic1

# Guyana
GY:
    address_template: *generic16

# Hong Kong
HK:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{{state_district}}}
        {{{state}}}

# Hong Kong - English
HK_en:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{{state_district}}}
        {{{state}}}
        {{{country}}}

# Hong Kong - Chinese
HK_zh:
    address_template: |
        {{{country}}}
        {{{state}}}
        {{{state_district}}}
        {{{road}}}
        {{{house_number}}}
        {{{house}}}
        {{{attention}}}


# Heard Island and McDonald Islands - same as Australia
HM: 
    use_country: AU
    change_country: Australia
    add_component: state=Heard Island and McDonald Islands

# Honduras
HN: 
    address_template: *generic1

# Croatia
HR: 
    address_template: *generic1

# Haiti
HT:
    address_template: *generic1
    postformat_replace:
        - [" Commune de"," "]

# Hungary
HU:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} 
        {{{road}}} {{{house_number}}} 
        {{{postcode}}}
        {{{country}}}

# Indonesia
# https://en.wikipedia.org/wiki/Address_%28geography%29#Indonesia
ID:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} {{{postcode}}}
        {{{state}}}
        {{{country}}}

# Ireland
# https://en.wikipedia.org/wiki/Postal_addresses_in_the_Republic_of_Ireland
IE: 
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{{county}}}
        {{{postcode}}}
        {{{country}}}
    replace:
        - [" City$",""]

# Israel
IL: 
    address_template: *generic1

# Isle of Man
IM: 
    address_template: *generic2

# India
# http://en.wikipedia.org/wiki/Address_%28geography%29#India
IN:
    address_template: *generic12

# British Indian Ocean Territory - same as UK
IO:
    use_country: GB
    change_country: British Indian Ocean Territory, United Kingdom

# Iraq
IQ: 
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{city_district}}} 
        {{{house_number}}} {{{road}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{state}}} || {{{village}}} {{/first}}
        {{{postcode}}}
        {{{country}}}

# Iran
IR:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{road}}}
        {{{house_number}}}
        {{#first}} {{{province}}} || {{{state}}} {{/first}}
        {{{postcode}}}
        {{{country}}}

IR_en:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{road}}}
        {{{house_number}}}
        {{#first}} {{{province}}} || {{{state}}} {{/first}}
        {{{postcode}}}
        {{{country}}}

IR_fa:
    address_template: |
        {{{country}}}
        {{#first}} {{{state}}} || {{{province}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{road}}}
        {{{house_number}}}
        {{{house}}}
        {{{attention}}}
        {{{postcode}}}

# Iceland
IS: 
    address_template: *generic1

# Italy
IT: 
    address_template: *generic8

# Jersey - same format as UK, but not part of UK
JE:
    use_country: GB
    change_country: Jersey, Channel Islands

# Jamaica
JM:
    address_template: *generic20

# Jordan
JO:
    address_template: *generic1

# Japan
JP:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}, {{#first}} {{{state}}} || {{{state_district}}} {{/first}} {{{postcode}}}
        {{{country}}}
    postformat_replace:
        # fix the postcode to make it \d\d\d-\d\d\d\d
        - [" (\\d{3})(\\d{4})\n"," $1-$2\n"]



# Japan - English
JP_en:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}, {{#first}} {{{state}}} || {{{state_district}}} {{/first}} {{{postcode}}}
        {{{country}}}
    postformat_replace:
        # fix the postcode to make it \d\d\d-\d\d\d\d
        - [" (\\d{3})(\\d{4})\n"," $1-$2\n"]


# Japan - Japanese
JP_ja:
    address_template: |
        {{{country}}}
        {{{postcode}}}
        {{#first}} {{{state}}} || {{{state_district}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{road}}}
        {{{house_number}}}
        {{{house}}}
        {{{attention}}}
    postformat_replace:
        # fix the postcode to make it \d\d\d-\d\d\d\d
        - [" (\\d{3})(\\d{4})\n"," $1-$2\n"]

# Kenya
KE:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{state}}} || {{{village}}} {{/first}}
        {{{postcode}}}
        {{{country}}}

# Kyrgyzstan
KG:
    address_template: *generic11

# Cambodia
KH:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} {{{postcode}}}
        {{{country}}}

# Kiribati
KI:
    address_template: *generic17

# Comoros
KM:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{country}}}

# Saint Kitts and Nevis
KN: 
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}, {{#first}} {{{state}}} || {{{island}}} {{/first}}
        {{{country}}}

# Democratic People's Republic of Korea / North Korea
KP: 
    address_template: *generic21

# Republic of Korea / South Korea
KR: 
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}, {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}, {{#first}} {{{state}}} {{/first}} {{{postcode}}} 
        {{{country}}}

# South Korea - English
KR_en:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}, {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}, {{#first}} {{{state}}} {{/first}} {{{postcode}}} 
        {{{country}}}

# South Korea - Korean
KR_ko:
    address_template: |
        {{{country}}}
        {{#first}} {{{state}}} {{/first}} 
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{road}}}
        {{{house_number}}} 
        {{{house}}}
        {{{attention}}}
        {{{postcode}}}

# Kuwait
KW:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}

        {{{road}}} 
        {{{house_number}}} {{{house}}}
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{{country}}}

# Cayman Islands
KY:
    address_template: *generic2

# Kazakhstan
KZ:
    address_template: *generic11

# Laos
LA: 
    address_template: *generic22

# Lebanon
LB: 
    address_template: *generic2
    postformat_replace:
        # fix the postcode to make it nonbreaking space
       - [" (\\d{4}) (\\d{4})\n"," $1 $2\n"]
 #       - ["\n(\\d{4}) (\\d{4}) ","\n$1 $2 "]


# Saint Lucia
LC:
    address_template: *generic17

# Liechtenstein, same as Switzerland
LI: 
    use_country: CH

# Sri Lanka
LK:
    address_template: *generic20

# Liberia
LR:
    address_template: *generic1

# Lesotho
LS:
    address_template: *generic2

# Lithuania
LT:
    address_template: *generic1

# Luxemburg
LU:
    address_template: *generic3

# Latvia
LV:
    address_template: *generic7

# Libya
LY:
    address_template: *generic17

# Morocco
MA:
    address_template: *generic3

# Monaco
MC:
    address_template: *generic3

# Moldova
MD:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}}, {{{house_number}}}
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{state}}} {{/first}}
        {{{country}}}

# Montenegro
ME:
    address_template: *generic1

# Collectivité de Saint-Martin
MF:
    use_country: FR
    change_country: France     

# Marsall Islands
MH:
    use_country: US
    add_component: state=Marsall Islands

# Madagascar
MG:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} 
        {{{country}}}

# Macedonia
MK:
    address_template: *generic1

# Mali
ML:
    address_template: *generic17

# Myanmar (Burma)
MM:
    address_template: | 
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{state}}} {{/first}}, {{{postcode}}} 
        {{{country}}}


# Mongolia
MN:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{city_district}}} 
        {{#first}} {{{suburb}}} || {{{neighbourhood}}} {{/first}}
        {{{road}}} 
        {{{house_number}}} 
        {{{postcode}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} 
        {{{country}}}

# Macau
MO:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{#first}} {{{suburb}}} || {{{village}}} || {{{state_district}}} {{/first}}
        {{{country}}}

# Macao - Portuguese
MO_pt:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{#first}} {{{suburb}}} || {{{village}}} || {{{state_district}}} {{/first}}
        {{{country}}}

# Macao - Chinese
MO_zh:
    address_template: |
        {{{country}}}
        {{#first}} {{{suburb}}} || {{{village}}} || {{{state_district}}} {{/first}}
        {{{road}}}
        {{{house_number}}}    
        {{{house}}}
        {{{attention}}}

# Northern Mariana Islands
MP:
    use_country: US
    change_country: United States of America
    add_component: state=Northern Mariana Islands

# Montserrat
MS:
    address_template: *generic16

# Malta
MT:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{#first}} {{{city}}} || {{{town}}} || {{{suburb}}} || {{{village}}} {{/first}} 
        {{{postcode}}}
        {{{country}}}

# Martinique - overseas territory of France (FR)
MQ:
    use_country: FR
    change_country: Martinique, France     

# Mauritania
MR:
    address_template: *generic18

# Mauritius
MU:
    address_template: *generic18

# Maldives
MV:
    address_template: *generic2

# Malawi
MW:
    address_template: *generic16

# Mexico
MX:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}}, {{#first}} {{{state_code}}} || {{{state}}} {{/first}}
        {{{country}}}

# Malaysia
MY:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{{state}}}
        {{{country}}}

# Mozambique
MZ:
    address_template: *generic15
    fallback_template: *fallback4

# Namibia
NA: 
    address_template: *generic2

# New Caledonia, special collectivity of France
NC:
    use_country: FR
    change_country: Nouvelle-Calédonie, France     

# Niger
NE: 
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} 
        {{{road}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{{country}}}

# Norfolk Island - same as Australia
NF: 
    use_country: AU
    add_component: state=Norfolk Island
    change_country: Australia

# Nigeria
NG: 
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} {{{postcode}}}
        {{{state}}}
        {{{country}}}

# Nicaragua
NI:
    address_template: *generic21

# Netherlands
NL:
    address_template: *generic1
    postformat_replace:
        # fix the postcode to make it \d\d\d\d \w\w
        - ["\n(\\d{4})(\\w{2}) ","\n$1 $2 "]
        - ["\nKoninkrijk der Nederlanden$","\nNederland"]


# Norway
# quoted since python interprets it as a boolean. Silly python!
"NO":
    address_template: *generic1

# Nepal
NP: 
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}} 
        {{#first}} {{{suburb}}} || {{{neighbourhood}}} || {{{city}}} {{/first}}
        {{#first}} {{{county}}} || {{{state_district}}} || {{{state}}} {{/first}} {{{postcode}}} 
        {{{country}}}

# Nauru
NR:
    address_template: *generic16

# Niue
NU:
    address_template: *generic16

# New Zealand
NZ:
    address_template: *generic20

# Oman
OM:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{{postcode}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}}
        {{{state}}}
        {{{country}}}

# Panama
PA:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{postcode}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}}
        {{{state}}}
        {{{country}}}
    replace:
        - ["city=Panama","Panama City"]
        - ["city=Panamá","Ciudad de Panamá"]

# Peru
PE:
    address_template: *generic19

# French Polynesia - same as FR
PF:
    use_country: FR
    change_country: Polynésie française, France     
    replace:
        - ["Polynésie française, Îles du Vent \\(eaux territoriales\\)","Polynésie française"]


# Papau New Guinea
PG:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} {{{postcode}}} {{{state}}} 
        {{{country}}}

# Philippines
PH:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{suburb}}} || {{{state_district}}} {{/first}}
        {{{postcode}}} {{{state}}}
        {{{country}}}

# Pakistan
PK:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{state}}} {{/first}} {{{postcode}}} 
        {{{country}}}

# Poland
PL:
    address_template: *generic1
    postformat_replace:
        # fix the postcode to make it \d\d-\d\d\d 
        - ["\n(\\d{2})(\\w{3}) ","\n$1-$2 "]


# Saint Pierre and Miquelon - same as FR
PM:
    use_country: FR
    change_country: Saint-Pierre-et-Miquelon, France     

# Pitcairn Islands
PN:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{island}}} {{/first}} 
        {{{country}}}

# Puerto Rico, same as USA
PR:
    use_country: US
    change_country: United States of America
    add_component: state=Puerto Rico

# Palestine
PS:
    use_country: IL

# Portugal
PT:
    address_template: *generic1

# Palau
PW: 
    address_template: *generic1

# Parguay
PY: 
    address_template: *generic1

# Qatar
QA:
    address_template: *generic17

# Réunion - same as FR
RE:
    use_country: FR
    change_country: La Réunion, France     


# Romania
RO: 
    address_template: *generic1

# Serbia
RS:
    address_template: *generic1

# Russia
RU:
    address_template: *generic10

# Rwanda
RW:
    address_template: *generic16

# Saudi Arabia
SA:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}, {{#first}} {{{village}}} || {{{city_district}}} || {{{suburb}}} || {{{neighbourhood}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} {{/first}} {{{postcode}}}
        {{{country}}}

# Solomon Islands
SB: 
    address_template: *generic17

# Seychelles
SC: 
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{island}}} {{/first}} 
        {{{island}}}
        {{{country}}}

# Sudan
SD: 
    address_template: *generic1

# Sweden
SE: 
    address_template: *generic1
    postformat_replace:
        # fix the postcode to make it \d\d\d \d\d
        - ["\n(\\d{3})(\\d{2}) ","\n$1 $2 "]

# Singapore
SG:
    address_template: *generic2

# Saint Helena, Ascension and Tristan da Cunha - same as UK
SH:
    use_country: GB
    change_country: $state, United Kingdom

# Slovenia
SI: 
    address_template: *generic1

# Svalbard and Jan Mayen - same as Norway
SJ:
    use_country: "NO"
    change_country: Norway

# Slovakia
SK: 
    address_template: *generic1
    replace:
        - ["^District of ",""]

# Sierra Leone
SL:
    address_template: *generic16

# San Marino - same as IT
SM: 
    use_country: IT

# Senegal
SN:
    address_template: *generic3

# Somalia
SO: 
    address_template: *generic21

# Suriname
SR:
    address_template: *generic21

# South Sudan
SS: 
    address_template: *generic17

# São Tomé and Príncipe
ST:
    address_template: *generic17

# El Salvador
SV: 
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}} 
        {{{postcode}}} - {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} 
        {{{state}}} 
        {{{country}}}
    postformat_replace:
        - ["\n- ","\n "]

# Sint Maarten
SX: 
    address_template: *generic17

# Syria
SY: 
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}}, {{{house_number}}}
        {{#first}} {{{village}}} || {{{city_district}}} || {{{neighbourhood}}} || {{{suburb}}} {{/first}}
        {{{postcode}}} {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{state}}} {{/first}}
        
        {{{country}}}


# Swaziland
SZ: 
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{state}}} {{/first}}
        {{{postcode}}} 
        {{{country}}}

# Turks and Caicos Islands - same as UK
TC:
    use_country: GB

# Chad
TD: 
    address_template: *generic21

# French Southern and Antarctic Lands
TF:
    use_country: FR
    change_country: Terres australes et antarctiques françaises, France     

# Togo
TG:
    address_template: *generic18

# Thailand
TH:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{village}}}
        {{{road}}} 
        {{#first}} {{{neighbourhood}}} || {{{city}}} || {{{town}}} {{/first}}, {{#first}} {{{suburb}}} || {{{city_district}}} || {{{state_district}}} {{/first}}
        {{{state}}} {{{postcode}}} 
        {{{country}}}

# Tajikistan
TJ:
    address_template: *generic1

# Tokelau, territory of New Zealand
TK:
    use_country: NZ
    change_country: Tokelau, New Zealand

# Timor-Leste/East Timor
TL: 
    address_template: *generic17

# Turkmenistan
TM: 
    address_template: *generic22

# Tunisia
TN:
    address_template: *generic3

# Tonga
TO: 
    address_template: *generic16

# Turkey
TR: 
    address_template: *generic1

# Trinidad and Tobago
TT:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{state_district}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}}, {{{postcode}}}
        {{{country}}}

# Tuvalu
TV: 
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{#first}} {{{county}}} || {{{state_district}}} || {{{state}}} || {{{island}}} {{/first}}
        {{{country}}}

# Taiwan
TW: 
    address_template: *generic20

TW_en:
    address_template: *generic20

TW_zh:
    address_template: |
        {{{country}}}
        {{{postcode}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}
        {{{city_district}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}
        {{{road}}}
        {{{house_number}}}
        {{{house}}}
        {{{attention}}}

# Tanzania
TZ:
    address_template: *generic14
    fallback_template: *generic14
    postformat_replace:
        - ["Dar es Salaam\nDar es Salaam","Dar es Salaam"]

# Ukraine
UA:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}}, {{{house_number}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{state_district}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{state}}} {{/first}}
        {{{postcode}}} 
        {{{country}}}

# Uganda
UG:
    address_template: *generic16

# US Minor Outlying Islands, same as USA
UM:
    fallback_template: *fallback3
    use_country: US
    change_country: United States of America
    add_component: state=US Minor Outlying Islands

# USA
US: 
    address_template: *generic4
    fallback_template: *fallback2
    replace:
        - ["state=United States Virgin Islands","US Virgin Islands"]
        - ["state=USVI","US Virgin Islands"]
    postformat_replace:
        - ["\nUS$","\nUnited States of America"]
        - ["\nUSA$","\nUnited States of America"]
        - ["\nUnited States$","\nUnited States of America"]

# Uzbekistan
UZ:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}} 
        {{#first}} {{{state}}} || {{{state_district}}} {{/first}} 
        {{{country}}}
        {{{postcode}}}

# Uruguay
UY: 
    address_template: *generic1

# Vatican City - same as IT
VA: 
    use_country: IT

# Saint Vincent and the Grenadines
VC: 
    address_template: *generic17

# Venezuela
VE:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{road}}} {{{house_number}}}
        {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}} {{{postcode}}}, {{#first}} {{{state_code}}} || {{{state}}} {{/first}}
        {{{country}}}

# British Virgin Islands
VG:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}} 
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} {{/first}}, {{{island}}}
        {{{country}}}, {{{postcode}}}

# US Virgin Islands, same as USA
VI: 
    use_country: US
    change_country: United States of America
    add_component: state=US Virgin Islands

# Vietnam
VN:
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}}, {{{road}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{neighbourhood}}} {{/first}}, {{#first}} {{{city}}} || {{{town}}} || {{{state_district}}} || {{{village}}} {{/first}}
        {{{state}}} {{{postcode}}}
        {{{country}}}

# Vanuatu
VU:
    address_template: *generic17

# Wallis and Futuna, same as France
WF:
    use_country: FR
    change_country: Wallis-et-Futuna, France     

# Samoa
WS:
    address_template: *generic17

# Yemen
YE:
    address_template: *generic18

# Mayotte - same as FR
YT:
    use_country: FR
    change_country: Mayotte, France     

# South Africa
ZA: 
    address_template: |
        {{{attention}}}
        {{{house}}}
        {{{house_number}}} {{{road}}}
        {{#first}} {{{suburb}}} || {{{city_district}}} || {{{state_district}}} {{/first}}
        {{#first}} {{{city}}} || {{{town}}} || {{{village}}} || {{{state}}} {{/first}} 
        {{{postcode}}}
        {{{country}}}

# Zambia
ZM:
    address_template: *generic3

# Zimbabwe
ZW:
    address_template: *generic16
