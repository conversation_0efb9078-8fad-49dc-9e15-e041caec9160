.cart-items {

    font-size: $font-size-md;
    
    .images {
        .main-image {
            border: 1px solid #ddd;
            height: 100%;
            width: 100%;
        }

        .image {
            border: 1px solid #ddd;
            height: 100%;
            width: 100%;
            overflow: hidden;
        }
    }   

    .product {

        .title {
            text-decoration: underline;
            line-height: $title-line-height;
            color: $title-color;
        }

        .specs {
            .row {
                
                align-items: center;
                
                .value {
                    line-height: $title-line-height;
                    color: $text-color;
                    font-weight: $title-font-weight;
                }

            }
        }
    }

    .amount {
        color: $text-color;
    }

    .remove-cart,
    .edit {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: end;
    }

    .remove-cart {
        height: 21px;
        width: 21px;

        i {
            font-size: 19px;
            color: #575756;
        }
    }
    
    .edit {
        position: relative;
        bottom: -16px;
        height: 22px;
        width: 22px;

        i {
            font-size: 16px;
            color: #575756;
        }
    }
}

@include media-breakpoint-down(md) {
    .cart-items {
        font-size: $font-size-sm !important;
    }
}