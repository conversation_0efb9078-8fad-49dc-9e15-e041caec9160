.pagination {
    
    .page-item {
        display: inline-flex;
        height: 40px;
        min-width: 40px;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        cursor: pointer;

        @media (max-width: 375px) {
            height: 35px;
            min-width: 35px;
        }

        @media (max-width: 320px) {
            &.number {
                &:not(.active) {
                    display: none;
                }
            }
        }

        
        &:not(:last-child) {
            margin-right: 4px;
        }

        &.dots {
            min-width: 25px;
        }

        &.active {
            .page-link {
                background-color: #fff;
                border: 1px solid #009fe3;
                color: #009fe3;
            }
        }

        .page-link {
            border-radius: 4px;
            margin: 0px;
            color: #777777;
            font-size: 16px !important;
            display: inline-flex;
            width: 100%;
            height: 100%;
            justify-content: center;
            align-items: center;
            padding: 0.8rem;
            background-color: transparent;
            border: none;
            
            @include media-breakpoint-down(lg) { 
                font-size: 16px !important;
            }

            &:hover {
                background-color: $primary;
                color: #fff;
                border: none;
            }

        }
        
    }
}