.block-checkout-steps {
    
    padding: 0px 12px;

    .step {

        position: relative;
        background-color: #ffffff;
        border: 1px solid #ddd;

        .chevron {
            display: none;
            color: #41b57a;
            font-size: 11px;
        }

        .title {
            font-weight: $title-font-weight;
            font-size: 14px;
            line-height: $title-line-height;
            color: #41b57a;
            text-decoration: underline;

            @include media-breakpoint-down(md) {
                font-size: 12px;
            }
        }
    
        &:not(.active) {
            .title {
                opacity: .5;
                color: $title-color;
            }
        }
    
        &.active {
            background-color: #ffffff;
            border: 1px solid #41b57a;
    
            .chevron {
                display: inline-block;
            }
        }

        &.checked {
            background-color: #EEF8F2;
            border: 1px solid #41b57a;
            border-right: 0px solid transparent;
    
            .check {
                color: #41b57a;
                font-size: 11px;
            }
        }
        

        &:not(:last-child) {

            &.active,
            &.checked,
            &:not(.active) {
                &:after {
                    content: "";
                    position: absolute;
                    top: 4px;
                    width: 24.5px;
                    right: -12px;
                    height: 24.5px;
                    z-index: 1;  
                    transform: rotateZ(45deg);
                    border: 1px solid #ddd;
                    border-left-color: #fff;
                    border-bottom-color: #fff;
                    background: #ffffff;
                }
            }
            
            &.active {
                border-right: 0px solid transparent;

                &:after {
                    border-top-color: #41b57a;                    
                    border-right-color: #41b57a;                    
                }
            }

            &:not(.active):not(.checked) {
                &:after {
                    border-top-color: #ddd;
                    border-right-color: #ddd;
                }
            }

            &.checked {

                &:after {
                    border-top-color: #41b57a;                    
                    border-right-color: #41b57a;
                    border-left-color: #EEF8F2;
                    border-bottom-color: #EEF8F2;
                    background: #EEF8F2;                    
                }
            }
        }
    
        @include media-breakpoint-down(md) { 
            &:not(.active) {
                .title {
                    display: none;
                }

                .chevron {
                    color: #030304;
                    opacity: .5;
                    margin-left: 0px !important;
                }
            }
    
            .chevron {
                display: inline-block !important;
                font-size: 11px;
            }

            &.checked {    
                .check {
                    font-size: 11px;
                }
            }

            &:not(:last-child) {

                &.active,
                &.checked,
                &:not(.active) {
                    &:after {
                        content: "";
                        top: 4px;
                        width: 21px;
                        right: -10px;
                        height: 23px;
                    }
                }
            }
        }

    }

}