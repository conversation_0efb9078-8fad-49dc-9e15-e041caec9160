

header {

    z-index: 1040;
    box-shadow: 0px 0px 12px 1px #DDDDDD;
    background-color: #FFFFFF;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;

    .header-container {

        height: 90px;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #dee2e6;

        .logo {
            height: 58px;
            margin-left: 5px;
        }

        .nav {

            .header-item {

                // position: relative;
                display: flex;
                min-height: 90px;
                align-items: center;

                a, span {
                    z-index: 1;
                    font-size: 14px;
                    color: #444;
                    font-family: "Open Sans";
                    font-weight: 700;
                    text-decoration: none;
                    display: flex;

                    &:hover {
                        text-decoration: underline;
                    }
                }

                .submenu {
                    display: flex;
                    left: -100%;
                    top: 0px;
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    flex-direction: column;
                    background: #fcfaf8;
                    transition: left 0.25s;
                    flex-grow: 1;
                    overflow-y: auto;
                }

                &.active {
                    > .container > .submenu,
                    > .submenu {
                        left: 0px;
                        transition: left 0.4s;
                    }
                }


            }
        }

        .menu-items {
            ul {
                .header-item {
                    > a,span {
                        padding-right: 24px;
                    }
                }
            }
        }

        .languages {

            height: 100%;
            justify-content: center;
            display: flex;
            position: relative;
            z-index: 3;

            &:hover {
                .languages-list {
                    bottom: -99px;
                    z-index: 1;                    
                    transition: 0.3s bottom ease-out;
                }
            }

            .overlay {
                position: absolute;
                z-index: 2 !important;
                background: rgb(255, 255, 255);
                width: 250px;
                left: -12px;
                top: -1px;
                height: 100%;
            }       
            
            .languages-list {
                position: absolute;
                background: white;
                padding: 16px;
                border: 1px solid #dddddd;
                border-top: none !important;
                width: 175px;
                bottom: 150px;
                z-index: 0;
                height: 100px;
                transition: 0.3s bottom ease-in;
                left: -5px;

                border-radius: 0px 0px 4px 4px !important;
                background: #fff;
                box-shadow: 0px 1px 12px 1px #ddd;

                ul {
                    li:not(:last-child) {
                        margin-bottom: 8px !important;
                    }
                }

                ul li a {
                    color: #777;
                    text-decoration: none;
                    
                    &:hover {
                        text-decoration: none;
                        font-weight: bold;
                    }
                }
            }

            img {
                width: 24px;
                z-index: 3;
                border-radius: 4px !important;
                text-decoration: none !important;
            }
        
        }
        
        .search-block {

            z-index: 4 ;

            .search {
            
                .search-input {
                    opacity: 0;
                    position: absolute;
                    right: 36px;
                    width: 0px;
                    top: -1px;
                    bottom: 0;
                    z-index: 3;

                    input {
                        border-right: none;
                        height: 42px;
                        border-radius: 4px 0px 0px 4px !important;
                    }
                }

                .search-content {
                    position: absolute;
                    right: 0px;
                    z-index: 1;
                    cursor: default;
                    width: calc(100vw - 72px + 25px);
                    border: 1px solid #DDDDDD;
                    background-color: #fff;
                    top: 100%;
                    border-radius: 0 0 4px 4px;
                    
                    .search-titles {
                        div {
                            line-height: 20px;
                        }
                    }    

                    .search-image-container {
                        width: 80px;
                        height: 53px;
                        border: 1px solid $border-color;
                    }
                }
                
            }

            &:hover {
                
                .search {
                    border-radius: 0px 4px 4px 0px !important;
                    border-left: none;

                    .search-input {
                        border-right: none;
                        opacity: 1;
                        width: calc((100vw - 640px) / 2);
                        transition: .2s;
                    }
                }
            }
        }
        

        .account-information {

            &:hover {
                .account-list {
                    bottom: -149px;            
                    transition: 0.3s bottom ease-out;
                }
            } 

            a {
                z-index: 0 !important;
            }
            
            
            .overlay {
                position: absolute;
                z-index: -1 !important;
                background: #FFFFFF;
                width: 165px;
                right: -13px;
                top: 0px;
                height: calc(10vh - 7px);
            }       

            .account-list {
                position: absolute;
                background: #FFFFFF;
                padding: 16px;
                border: 1px solid #dddddd;
                border-top: none !important;
                width: 165px;
                bottom: 175px;
                z-index: -2;
                height: 150px;
                transition: 1s all;
                right: -13px;

                .item {
                    font-size: $font-size-sm;
                    font-weight: $title-font-weight;
                    
                    .icon-box {
                        height: 15px;
                        width: 15px;
                    }
                }
            }
        
        }
        
        .controls {

            z-index: 3;
        }
        
                
        .header-icon-item {

            position: relative;
            height: 40px;
            width: 40px;
            background-color: #fff;
            box-shadow: 0px 0px 0px 1px #ddd;
            border-radius: 4px;
            color: #444;
            text-decoration: none;
            cursor: pointer;
            font-size: 20px !important;
            align-items: center;
            justify-content: center;
            display: flex;
            font-weight: 700; 
            text-decoration: none !important;
        }

        .header-icons {
                
    
            .header-item {

                padding-right: .25rem !important;
                padding-left: .25rem !important;
                    
                    

            }
        }

        .company-change {

            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold !important;
            margin-bottom: 16px;
            font-size: 14px;
            width: 100%;
            z-index: 4;
            background-color: #F2F2F2;
            border: #d9d1d1 1px solid;

            .icon {
                color: #fa6819 !important;
            }
        }
    }

    .mobile-hubloader-container {
        display: none;
    }


    .btn-hubloader {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold !important;
        background: linear-gradient(135deg, rgb(250 104 25 / 7%) 33%, rgb(250 104 25 / 18%) 84%) !important;
        color: #fa6819 !important;
        border: 1px solid #fa6819 !important;
        margin-bottom: 16px;
        font-size: 14px;
        width: calc(80vw - 32px);
        height: 75px;
        z-index: 4;

        .title-text {
            opacity: 1;
            display: inline;
            transition: 0.5s opacity;
            color: #FA6819 !important;
            padding-right: 0px !important;
        }

        .icon {
            position: absolute;
            left: 20px;
            top: 24px;
            font-size: 26px;
            width: 26px;
            height: 26px;
        }

        .more-info {
            position: absolute;
            border: 1px solid #3d3d3d;
            background: #fff;
            color: #6b6b6b;
            width: 20px;
            height: 20px;
            border-radius: 10px;
            top: 4px;
            right: 4px;
            font-size: 11px;
            padding-top: 1px;
            opacity: 0.3;
            

            .more-info-box {
                position: absolute;
                width: 329px;
                right: 1000px;
                background: white;
                padding: 16px;
                height: auto;
                display: flex;
                flex-direction: column;
                text-align: left;
                border-radius: 4px;
                border: 2px solid #dddddd;
                text-transform: none;
                font-weight: normal;
                font-size: 12px;
                bottom: 33px;
                box-shadow: 3px 0px 39px 3px rgba(227,227,227,1);
                opacity: 0.5;
                transition: 0.5s right;
            }

            &:hover {

                opacity: 1;
                outline: 1px solid #3d3d3d;

                .more-info-box {
                    opacity: 1;
                    transition: 0.5s all;
                    right: -7px;
                }
            }
        }

        &:hover {

            outline: 1px solid #fa6819;
            background: linear-gradient(135deg, rgb(250 104 25 / 7%) 33%, rgb(250 104 25 / 18%) 84%);
            color: #fa6819;
            font-weight: bold !important;
            font-size: 14px;

            .icon {
                position: absolute;
                left: 20px;
                top: 24px;
                font-size: 26px;
                width: 26px;
                height: 26px;
            }
        }
    }
}

body.is-mobile {

    @include media-breakpoint-down(lg) {
        .mobile-hubloader-container {
            padding-right: 1rem !important;
            padding-left: 1rem !important;
        }
    }
}

@include media-breakpoint-down(md) {

    .header-item-depth-0 {
        > .container {
            > .overlay-block {
                display: block !important;
            }
        }
    }

    .account-information {

        &:hover {
            .account-list {
                bottom: -64px !important;            
                transition: 0.3s bottom ease-out;
            }
        }
    } 

    .search-block {

        height: 60px;
        border-top: 1px solid #dee2e6 !important;
        padding-right: 0.7rem !important;
        align-items: center;
        display: flex;
        z-index: 2 !important;
                
        // .search-content {
        //     width: calc((100vw - 66px) + 25px);
        // }

        .header-icons {

            width: 100%;
            
            .header-item {
    
                width: 100%;
                display: block !important;
                min-height: initial !important;
                
                .search {
        
                    border-radius: 0px 4px 4px 0px !important;
                    border-left: none;
                    width: 100% !important; 

                    .search-input {
                        opacity: 1;
                        width: calc(100% - 35px) !important;

                        input {
                            border-right: none;
                            height: 37px;
                        }
                    }

                    .search-icon {
                        right: 10px;
                        position: absolute;
                    }
                }
    
            }

        }
    }

    .languages {
        z-index: 4 !important;
    }
    .controls {
        z-index: 4 !important;
    }
}

@include media-breakpoint-down(lg) {

    body.menu-open {
        
        overflow: hidden;
        
        .menu-items {

            display: flex !important;
            z-index: 10 !important;
            flex-direction: column;
            left:0px !important;
            background-color: rgba(0, 0, 0, 0.5) !important;
            transition: background-color 0.5s;

            .menu-mobile {

                position: relative;
                display: flex;
                flex-direction: column;
                width: calc(80vw);
                left: 0px !important; 
                height: calc(100vh);
                transition: left 0.5s;

                .top {
                    flex-grow: 1;
                    display: flex;
                    flex-direction: column;
                    overflow-y: auto;

                    .header {
                        justify-content: space-between;
                        padding: 0.5rem 1rem 0.5rem 1rem !important;
                        min-height: unset;
                        border-bottom: 1px solid #dee2e6;
                        background-color: #fff;
                        align-items: center;
            
                        .icon-box , .close {
                            font-size: 20px;
                            color: #444;
                            cursor: pointer;
                        }
                    
                        .title {
                            margin-left: 24px;
                            font-size: 16px;
                            color: #444;
                            font-family: "Open Sans";
                            font-weight: 800;
                            text-transform: uppercase;
                        }

                        .text {
                            display: none;
                        }
                    }

                    .body {

                        flex-grow: 1;
                        display: flex;
                        flex-direction: column;
                        overflow-y: auto;
                        background-color: #fcfaf8;

                        .nav {

                            .header-item {
                                display: block;
                                border-bottom: 1px solid #dee2e6;
                                min-height: unset;
                                font-size: 14px;
                                padding: 0.5rem;
                                width: 100%;

                                &.header-item-depth-0 {

                                    &.active {
                                        > a {
                                            background-color: #ddd;
                                            border-radius: 8px;
                                        }
                                    }
                                }

                                &.product {
                                    .chevron-right {
                                        display: none;
                                    }
                                }

                                a, 
                                span {
                                    display: flex;
                                    width: 100%;
                                    align-items: center;
                                    padding-right: 12px;
                                    color: #777;
                                    font-weight: 700;
                                    text-decoration: none;
                                    z-index: 1;
                                    cursor: pointer;

                                    &:hover {
                                        color : #FA6819;

                                        .chevron-right {
                                            font-size: 17px !important;
                                        }
                                    }
                                }

                                .icon-box {
                                    padding-left: 6px;
                                    width: 25px;
                                    height: 25px;
                                    cursor: pointer;
                                }
                            }
                        }
                    }
                }

                .footer {
                    background-color: #fcfaf8;
                    color: #444;
                    line-height: 16px;
                    font-size: 13px;
                    text-transform: uppercase;
                    position: relative;
                    z-index: 3;
                }
            }
        }   
    
    }
    
    header {

        .header-container {
            height: initial;

            > div:not(.search-block):not(.logo-block) {
                height: 90px;
                display: flex;
                justify-content: center;
            }

            .menu-items {
                position: fixed;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0);
                transition: background-color 0.5s;
                top: 0px;
                left: -100%;
                
                .menu-mobile {
                    left: -100%;
                    transition: 0.5s left;
                }
            }

            .search {

                input {
                    height: 37px !important;
                }
                
                &:hover {
                    .search-input {
                        width: 50vw !important ;
                    }
                }

            }

            .header-icon-item {
                height: 35px !important;
                width: 35px !important;
                font-size: 18px !important;
            }
        }

    }
}

@include media-breakpoint-only(lg) {

    .sidemenu {
        left: -140px !important;
        
        &:hover {

            left: 0px !important;

            .nav {
                display: flex !important;
            }
            
            .company-change {
                display: flex !important;
                transition: all 0.15s;
                left: 9px !important;
            }

            .toggle {
                display: none !important;
            }
        }

        .nav {
            display: none !important;
        }

        .toggle {
            display: flex;
            position: absolute;
            right: 0px;
            top: calc(100vh - 50% - #{90px} - 15px);
            align-items: center;
            justify-content: center;
            width: 25px;
            height: 30px;
            border-radius: 4px 0px 0px 4px;
            background: #cccccc9c;
            cursor: pointer;
        }

        .company-change {
            left: -50% !important;
        }
        
        // .header-item {
        //     display: none !important;
        // }

    }


    main, 
    footer {
        margin-left: 80px !important;
    }
}

@include media-breakpoint-up(md) {
    .search-content {
        width: calc(100vw - 385px) !important;
    }
}

@include media-breakpoint-up(lg) {
    
    .company-change {
        position: fixed !important;
        left: 9px !important;
        bottom: 100px !important;
        width: 63px !important;
        margin-bottom: 0 !important;
        width: 200px !important;
        transition: left 0.4s;
    }

    @for $i from 0 through 10 {
        
        .header-item-depth-1:nth-child(#{$i}) > .container {
            margin-top: calc(#{($i * 16px) * -1});
        
            .overlay-block {
                top: calc(6px + #{$i * 16}px);
            }
        }
    }

    .submenu {
        overflow-y: initial !important;
    }

    .search-block {
        z-index : 4 !important;

        .search-content {
            width: calc((100vw - 570px) / 2) !important;
        }
    }

    .account-information {

        &:hover {
            .account-list {
                bottom: -149px !important;            
            }
        } 
    }
    .header-item:not(.header-item-depth-0) {
        
        position: relative;

        > .container {
            display: none !important;
            background-color: #fff;
            min-height: 167px;
        }

        &:hover {

            a, span {

                text-decoration: none !important; 

                .header-item-text {
                    color: #fa6819;
                }

                .chevron-right {
                    font-size: 12px;
                }
            }

            > .container {
                display: block !important;
                position: absolute;
                border: 1px solid #ddd;
                top:0px;
                left: calc(100%);
                height: auto;
                padding-bottom:26px;
                
                .overlay-block {
                    display: block !important;
                    position: absolute;
                    width: 26px;
                    height: 26px;
                    rotate: 45deg;
                    background: #fff;
                    left: -14px;
                    border-left: 1px solid #ddd;
                    border-bottom: 1px solid #ddd;
                    border-radius: 0px 0px 0px 4px;
                    z-index: 5;
                }
                
                .submenu {
                    left: initial !important;
                    position: initial !important;
                    overflow-y: initial !important;
                    background: #fff;

                    .nav {
                        background-color: #fff;
                    }
                }
            }
        }

        

    }

    .header-item-depth-0 {
        .chevron-right {
            display: none;
        }
    }

    .sidemenu {
        left: 0px;
        top: 90px !important;
        height: calc(100vh - 90px) !important;
        position: absolute;
        flex-direction: column;
        background: #fcfaf8 !important;
        padding-top: 24px;
        width: 220px !important;
        transition: left 0.25s;
        border-right: 1px solid #ddd;
        
        &:hover {

            .btn-hubloader {
                display: flex !important;
                width: 200px !important;
                
                .title-text {
                    display: inline !important;
                }

                .icon {
                    left: 20px !important;
                }

                .more-info:hover .more-info-box {
                    right: -135px !important;
                }
            }

            .company-change {
                display: flex;
            }
        }

        >.header {
            display: none !important;
        }


        .btn-hubloader {
            transition: width 0.25s;
            position: fixed !important;
            left: 9px !important;
            bottom: 9px !important;
            width: 63px !important;
            height: 75px !important;
            margin-bottom: 0 !important;

        
            .icon {
                left: unset !important;
            }
            
            .title-text {
                display: none !important;
            }
        }


        .nav {
            background: #fcfaf8;
            flex-direction: column;
                
            .header-items-configurators {
                
                .container {
                    width: 650px;

                    .configurator {

                        width : 25%;

                        &:hover {
                            img {
                                transform-origin: center;
                                transform: scale(1.1);
                                border: 1px solid #ddd;
                                transition: 0.5s;
                            }
                        }

                        a {
                            flex-direction: column;
                        }

                        .header-item-text {
                            font-size: 14px !important;
                            line-height: 1.2 !important;
                            color: #444 !important;
                            font-weight: 800 !important;
                            text-transform: uppercase !important;
                        }
                    }
                }
            }

            .category-items-amount-1 {
                width: 325px;
            }

            .category-items-amount-2 {
                width: 650px;
            }

            .category-items-amount-3 {
                width: 975px;
            }

            a, span {
                width: 100%;
                font-weight: 500;
                color: #777;
                padding-bottom: 8px;
                padding-top: 8px;
                height: 40px;
            }
            
            .header-item {
                padding-left: 18px;
                min-height: 41px !important;
                
                .header-item-text {
                    font-weight: 500;
                    color: #777;
                    height: 25px;                
                    font-size: 16px;
                }

                .chevron-right {
                    font-size: 10px;
                    display: flex !important;
                    color: #777 !important;
                }

                .overlay-block {
                    display: none !important; 
                    background-color: #fff;
                }   
            }

            .submenu {

                >.header {

                    flex-direction: column;
                    padding-left: 18px;
                    padding-bottom: 18px;

                    .icon-box,
                    .close {
                        display: none;
                    }

                    .title {
                        padding-top: 2rem !important;
                        font-size: 20px;
                        line-height: 1.2;
                        font-family: "Open Sans";
                        color: #444;
                        font-weight: 800;
                        text-transform: uppercase;
                    }
                    .text {
                        font-size: 14px !important;
                        padding-top: 1rem ;
                    }
                }
                
                .header-item {

                    flex-direction: column;

                    &.header-item-depth-2 {
                        padding-left: 0px !important;
                    }

                    &.product {

                        height: unset !important;
                        min-height: unset !important;

                        a, span {
                            height: unset;
                            padding-bottom: 0px !important;
                            line-height: 17px;                        
                        }
                        
                        .header-item-text {
                            position: relative !important;
                            font-weight: normal;
                            text-transform: none;
                            height: unset !important;
                        }
                    }

                    .header-item-text {
                        font-weight: 800;
                        text-transform: uppercase;
                        color: #444;
                        position: relative;
                        font-size: 14px;

                        &:hover {
                            text-decoration: underline;
                        }
                    }
                    
                    .chevron-right {
                        display: none !important;
                    }
                }

                .submenu >.header {
                    display: none !important;
                }
            }
        }
    }

}

@include media-breakpoint-up(xl) {

    .sidemenu {
        left: 0px !important;

        .btn-hubloader {
            display: flex !important;
            width: 200px !important;

            .title-text {
                display: inline !important;
            }
    
            .icon {
                left: 20px !important;
            }
    
            .more-info:hover .more-info-box {
                right: -135px !important;
            }
        }
    
    }

    .toggle {
        display: none;
    }
    
    main, 
    footer {
        margin-left: 220px !important;
    }
    
    main.login, 
    footer.login {
        margin-left: 0px !important;
    }
}






