footer {
    background-color: #fff;
    
    .contact-stroke {    
        background-color: #252525;
        max-height: 140px;

        .image {
        
            width: 85px;
            height: 85px;
    
            img {
                box-shadow: 0px 0px 0px 1.5px #fff;
                border-radius: 50%;
                object-fit: cover;
                width: 100%;
                height: 100%;
            }
        }

        .title {
            font-size: 15px;
            line-height: 1.2;
            font-family: $font-family;
            color: #fff;
        }
    
        .text {
            font-size: 14px;
            line-height: 1.8;
            font-family: $font-family;
            color: #fff;
    
            @include media-breakpoint-down(md) { 
                font-size: 13px;
            }
        }
    
        .contact {
            font-weight: bold;
            font-size: 15px;
            line-height: 1.8;
            font-family: $font-family;
            color: #fff;
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }

            @include media-breakpoint-down(md) { 
                font-size: 16px;
            }
        }
    }

    .footer-body {

        .title {
            font-size: 14px;
            line-height: 1.8;
            font-family: $font-family;
            color: #444444;
            font-weight: 800;
            text-transform: uppercase;
        }
        
        .text {
            font-size: 12px;
            line-height: 1.8;
            font-family: $font-family;
            color: #777777;
        }     

        .socialmedia {
            border: 1px solid #252525;
            height: 25px;
            width: 25px;
            line-height: 25px;
            background-color: #252525;
            font-size: 10px;
            display: inline-block;
            border-radius: 4px;
            text-align: center;
        

            i {
                color: #fff;
                margin-right: 0rem;
            }
        }

        
        ul {

            li {

                a {
                    text-decoration: none;
                    font-size: 14px;
                    line-height: 1.8;
                    font-family: $font-family;
                    color: #777777;
                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }

        .label {
            margin-bottom: 0px !important;
            color: #444 !important;
            font-size: 15px !important;
            font-family: "Open Sans" !important;
            font-weight: bold;
            cursor: pointer;
        }


        i {
            margin-right: 0.25rem;
            font-size: 12px;
            color: #444444;
        }

        .contact {
            a {
                font-weight: bold;
                text-decoration: none;
                font-size: 14px;
                line-height: 1.2;
                font-family: $font-family;
                color: #444444;

                @include media-breakpoint-down(md) { 
                    font-size: 13px;
                }

                &:hover {
                    text-decoration: underline;
                }
            }

            p {
                font-size: 14px;
                line-height: 1.8;
                font-family: "Poppins";
                color: #777;
            }
            
        }

        .footer-border {
            display: block;

            height: 1px;
            background-color: #DDDDDD;
            margin-top: 2px;
            margin-bottom: 7px;
        }
    }

    .footer-bottom {
        background-color: #fcfaf8;

        ul {
            list-style-type: none;
            margin: 0;
            padding: 0;
            overflow: hidden;

            li {
                float: left;

                &:not(:first-child) {
                    margin-left: 15px;
                }

                a {
                    text-decoration: none;
                    font-size: 10px;
                    line-height: 1.8;
                    font-family: $font-family;
                    color: #777777;
    
                    @include media-breakpoint-down(md) { 
                        font-size: 13px;
                    }
    
                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
                
        }
        
        .code31 img {
            width: 50px !important;
        }

    }
}


@include media-breakpoint-up(md) { 

    footer {
        
        .collapse.dont-collapse {
            display: block;
            height: auto !important;
            visibility: visible;
        }
    
        .collapsed {
            pointer-events: none !important;
            cursor: default !important;
        }
    }
}

@include media-breakpoint-up(md) { 

    .newsletter-button {
        width: auto !important;
    }
}

