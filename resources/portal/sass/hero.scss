.hero {
    position: relative;
    background-color: #777;
    overflow: hidden;
    display: flex !important;
    align-items: center;
    margin-bottom: 1.5rem;

    &.hero-zoom {


        &:hover {

            img {
                transform-origin: center;
                transform: scale(1.1);
                transition: 0.5s;
            }
        }
    }

    &.hero-large {
        max-height: 400px;

        @include media-breakpoint-down(lg) {
            max-height: 225px;
        }
    
    }

    &.hero-medium {
        max-height: 200px;

        @include media-breakpoint-down(lg) {
            max-height: 175px;
        }
    
    }

    &.hero-small {
        max-height: 100px;
    }
    
    img {
        transition: 0.5s;
    }

    
    .wrapper {
        position: absolute;
        z-index: 2;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        display: flex;

        .info {
            
            h2 {
                color: #fff;
                font-weight: 800;
            }

            .title-sub {
                font-size: 20px;
                line-height: 24px;
                color: #fff;
            }

            .buttons {
                
                i,
                svg,
                img {
                    width: 11px;
                    height: 11px;
                    font-size: 11px !important;
                    color: #ffffff;
                }
            
                svg {
            
                    ellipse,
                    image,
                    line,
                    path,
                    polygon,
                    polyline,
                    rect,
                    circle {
                        fill: #ffffff;
                        stroke: #ffffff;
                    }
                }
    
                .text {
                        
                    font-size: 12px;
                    line-height: 14px;
                    color: #ffffff;
                }
            }
        }
    }
}