main.products-index {

    .sort-mobile {
        display: none;
    }

    .sort {
        label {
            font-size: $font-size-md;
            color: $title-color;
            font-weight: $title-font-weight;
        }
        
        select {
            font-size: $font-size-md;
        }
    }

    .filters {


        ul {
            padding-left: 0px;
            list-style-type: none;

            .filter {
                border-top: 1px solid #ddd;
                padding-top: 1rem ;
                padding-bottom: 1rem;

                &.filter-open {
                    i {
                        transform: rotate(180deg);
                    }
                }

                i {
                    transition: all 0.5s;
                    font-size: 12px;
                    color: #ccc;
                }
            

                &:first-child {
                    border-top: unset;
                    margin-top: 0;
                    padding-top: 0;
                }
                
                .title {
                    cursor: pointer;
                    align-items: center;
                    margin-bottom: 0px !important;
                    
                    p {
                        font-weight: $title-font-weight;
                        color: $title-color;
                        line-height: $title-line-height;
                        font-size: $font-size-md; 
                        text-transform: uppercase;
                        margin-bottom: 0px;
                    }
                }

                .items {
                    li {
                        
                        p {
                            color: $title-color;
                            line-height: $title-line-height;
                            font-size: $font-size-md; 
                        }
    
                        .quantity {
                            font-size: 12px;
                            background-color: #f9f9f9;
                            color: $title-color;
                        }
                        
                    }
                }

            }
        }
    }

    .filters-header {
        display: none;
    } 

    .filters-footer {
        display: none;
    } 
}


@include media-breakpoint-down(lg) {

    .sort {
        display: none;
    }

    .filters-mobile-toggle {
        position: fixed;
        border: 1px solid #ddd;
        bottom: 0;
        left: 0;
        z-index: 80;
        background-color: #fff;
        margin: 0 !important;
        width: 100%;
        
        button {
            width: 100%;
        }

    }

    .filters {
        display: none;
    }

    body.filter-open {

        overflow: hidden;

        .filters-overlay {
            position: fixed;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5) !important;
            transition: background-color 0.5s;
            top: 0px;
            display: flex;
            z-index: 1041;
            flex-direction: column;
            left:0px;

            .sort-mobile {
                display: block;
                padding: 0.5rem 1rem;

                .title {
                    font-size: $font-size-md !important;
                    line-height: $title-line-height!important;
                    color: $title-color !important;
                    font-weight: $title-font-weight;
                    margin-bottom: 5px;
                }

                .sort-pill {
                    background-color: #f9f9f9;
                    color: #404040;
                    border: 1px solid #ccc;
                    cursor: pointer;
                    overflow: hidden;
                    text-decoration: none !important;
                    font-size: $font-size-sm;
                    font-weight: $title-font-weight;
                    padding: 0.25rem 0.5rem;
                    margin-right: 0.5rem !important;
                }
                
            }
            .filters-header {
                background-color: #f9f9f9;
                width: calc(80vw);
                padding: 0.625rem 0.875rem;
                display: flex;
                justify-content: space-between;
                border-bottom: 1px solid #ddd;
    
                .title {
                    font-size : $font-size-md;
                    color : $title-color;
                    font-weight : $title-font-weight;
                }
    
                .close {
                    cursor: pointer;
                    color : $title-color;
                }
            }

            .filters {
                position: relative;
                display: flex;
                flex-direction: column;
                width: calc(80vw);
                left: 0px !important; 
                height: calc(100vh);
                transition: left 0.5s;
                background-color: #fff;
                flex-grow: 1;
                overflow-y: auto;

                .filter {
                    padding: 0.75rem 0.875rem !important;
                }
            }

            .filters-footer {
                background-color: #fff;
                width: calc(80vw);
                border-top: 1px solid #ddd;
                display: block;  
                padding: {
                    top: 0;
                    bottom: 1.375rem;
                    left: 1rem;
                    right: 1rem;
                };
                
                .filters-reset {
                    color: #FA6819;
                }

                .filters-submit {
                    width: 100%;
                    font-size: $font-size-md;
                }
            }
        }

    }
}