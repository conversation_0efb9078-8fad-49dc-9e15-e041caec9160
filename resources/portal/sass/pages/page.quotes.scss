main.quotes-index {

    .title {
        color: $title-color;
    }

    .item {
        &:hover {
            background-color: #f9f9f9;
        }
    }

    .form-icon-container{
        display: flex;
        background-color: #fff;

        > input {
            border-width: 1px 0px 1px 1px !important;
            border-style:solid !important;
            border-color: #ddd transparent #ddd #ddd !important;
            border-radius: 4px 0px 0px 4px !important;
        }
        

        ::placeholder {
            color: #bebebe;
        }

        label,
        span {
            min-width: 30px;
            border-left-color: transparent !important;
            border-radius: 0px 4px 4px 0px !important;
            border-width: 1px 1px 1px 0px !important;
            border-style:solid !important;
            border-color:#ddd #ddd #ddd transparent;

            cursor: pointer;
        }
    }

    .result-sorting {
        color: #777 !important;
    }

}