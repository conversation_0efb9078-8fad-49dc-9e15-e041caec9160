main.products-specific {

    
    .wrapper {

        width : 100% !important;
        display: grid;
        grid-template-areas:
        'visual title'
        'visual text';
        
        grid-template-columns: 47% 47%;
        grid-template-rows: auto 1fr;
        column-gap: 6%;

        .wrapper-left {
            grid-area: visual;
            overflow: hidden;

            .wrapper-left-top { 
                position: relative;
                z-index: 0;
    
                .arrow-left,
                .arrow-right {
                    z-index: 1;
                    position: absolute;
                    width: 30px;
                    height: 30px;
                    top: 50%;
                    margin-top: -20px;
                    background: #cccccc 0% 0% no-repeat padding-box;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 4px;
                    cursor: pointer;
    
                    &:hover {
                        border-color: $primary !important;
    
                    }
                }
    
                
                .arrow-left {
                    left: 1rem;
                }
                .arrow-right {
                    right: 1rem;
                }

            }

            .image-container {
                max-height: 375px;
            }
            
            .owl-carousel {

                .selected {
                    .image-container {
                        border: 1px solid green;
                        border-radius: 4px;
                    }
                }

                .link:hover {
                    .image-container {
                        border: 1px solid green;
                        border-radius: 4px;
                    }
                }

                .owl-nav {
                    
                    .owl-prev {
                        position: absolute;
                        height: 100%;
                        left: 0px;
                        cursor: pointer;
                        color: $primary !important;
                        background: #FFFFFFBF 0% 0% no-repeat padding-box;

                        span {
                            padding-left: 0.5rem;
                            padding-right: 0.5rem;
                            font-size: 33px;
                        }
                    }

                    .owl-next {
                        position: absolute;
                        height: 100%;
                        right: 0px;
                        cursor: pointer;
                        color: $primary !important;
                        background: #FFFFFFBF 0% 0% no-repeat padding-box;

                        span {
                            padding-left: 0.5rem;
                            padding-right: 0.5rem;
                            font-size: 33px;
                        }
                    }
                }
            }
        }

        .wrapper-right-top {
            grid-area: title;
        } 
        .wrapper-right-bottom {
            grid-area: text;
        }
    }


    .usp {

        gap: 10px;

        .icon {
            width: 18px;
            font-size: 13px;
            color: #00b900;
            align-self: center;

            i {
                font-weight: $title-font-weight;
            }
        }
    }

    .specifications {

        background-color: #fff;
        border: 1px solid #ddd;
        overflow: hidden;

        > .row {
            margin: 0 !important;

            &:not(:last-child) {
                border-bottom: 1px dashed #ddd;
            }

            &:nth-child(even) {
                background-color: #f9f9f9;
            }


        }
        
        .title {
            text-decoration: underline;
            font-weight: $title-font-weight;
            font-size: $font-size-md;
            line-height: $title-line-height;
            color: $title-color;
        }
        .text {
            font-size: $font-size-md;
        }

        .title,
        .text {
            padding: 12px;
        }
    }
}


@include media-breakpoint-down(lg) {

    main.products-specific {

        .wrapper {
            grid-template-areas:
            'title'
            'visual'
            'text';
            
            grid-template-columns: 100%;
    
            .wrapper-left {
                grid-area: visual;
                overflow: hidden;
                        
                .image-container {
                    max-height: 325px;
                }
            }
    
            .wrapper-right-top {
                grid-area: title;
            } 
            .wrapper-right-bottom {
                grid-area: text;
            }
        }
    }
}

@include media-breakpoint-down(md) {
    
    main.products-specific {

        
        .wrapper {
            width : 100% !important;
        }

        .specifications {
            
            .row:last-child {
                .specs-col:last-child {
                    border-bottom: 0px !important;
                }
            }

            .row {

                background-color: unset !important;
                border-bottom: unset !important;
                
                .specs-col {
                    
                    border-bottom: 1px dashed #ddd;

                    &:nth-child(even) {
                        background-color: #f9f9f9;
                    }
                    

                }
            }
            
        }
    }
}