main.login {
    
    .row {

        .form-group {
            
            margin-bottom: 1rem;

            label { 
                margin-bottom: 0px !important;
                color: #444 !important;
                font-size: 15px !important;
                font-family: "Open Sans" !important;
                font-weight: bold;
                cursor: pointer;
            }
        
            ::placeholder {
                font-weight: 300 !important;
            }
        }
    }

    .company-block {

        text-decoration: none !important;
        border : 1px solid #dee2e6;
        overflow: hidden;

        &.active {

            background-color: #41b57a12;
            border : 1px solid #58b57a !important;
            position: relative;

            &:hover {
                background-color: #41b57a12;
            }

            .icon-check {
                z-index: 1;
                display: block;
            }
        }

        &:hover {
            background-color: #F9F9F9;

            .card-footer {
                background-color: #F9F9F9;
            }
        }

        .company-name {
            margin-bottom: 0px !important;
            color: #444 !important;
            font-size: 15px !important;
            font-family: "Open Sans" !important;
            font-weight: bold;
            cursor: pointer;
        }
    }
}