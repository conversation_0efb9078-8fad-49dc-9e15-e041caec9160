
//@import "node_modules/bootstrap/scss/bootstrap";
@import "../../../node_modules/bootstrap/scss/functions";
@import "../../../node_modules/bootstrap/scss/variables";

@import 'variables';

@import 'bootstrap';


@import 'buttons';
@import 'style';
@import 'cards/front';
@import 'cards/panel';
@import 'cards/default';
@import 'cards/product';
@import 'sections/header';
@import 'sections/footer';
@import 'pages/page.dashboard';
@import 'pages/page.login';
@import 'pages/page.products';
@import 'pages/page.quotes';
@import 'pages/page.quotes.specific';
@import 'pages/page.orders';
@import 'pages/page.importer';
@import 'pages/page.orders.specific';
@import 'pages/page.products.specific';
@import 'pages/page.contactpersons';
@import 'pages/page.addressbook';
@import 'blocks/cart_items';
@import 'blocks/checkout_steps';
@import 'blocks/menu_account';
@import 'blocks/pagination';
@import 'page';
@import 'hero';
@import 'breadcrumb';
@import 'headings';

html, body {

    font-family: $font-family;
    background-color: #fcfaf8;
    font-size: $font-size-lg;
    font-weight: $text-font-weight;
    line-height: $text-line-height;
    color: $text-color;
    text-align: left;
    display: flex;
    min-height: calc(100vh);
    flex-direction: column;
}