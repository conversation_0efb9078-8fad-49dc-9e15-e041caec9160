

.card-front {
    
    box-shadow: 0px 0px 12px #3B3B3B0A;     
    width: 100%;         
    background-color: #fff;
    margin: 0 auto;
    padding: 1rem !important;

    .card-header {
        border: none;
        background-color: #fff;

        .title {
            margin-bottom: 0 !important;
        }
    }

    .card-body {

        .text {
            color: #777;
            margin-bottom: 1rem;
        }

        .form-group {

            input::placeholder {
                font-size: 14px;
                opacity: 0.7;
            }

            .input-group-text {
                
                background-color: #fff;
                cursor: pointer;

                i {    
                    color: #000000;
                    width: 20px; 
                }
            }
            
        }
        
        .btn-link {
            padding-bottom: 0 !important;
        }
    }

    .card-footer {
        border: none;
        background-color: #fff;
        padding: 0.5rem 1rem 1rem 1rem;

        button {
            width: 100%;
        }
    }
}