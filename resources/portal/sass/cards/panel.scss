.card-panel {
    
    border-radius: 0px;
    margin-bottom: 1rem;
    border-color: #ddd;
    box-shadow: 0px 0px 12px #0000000D;
    font-size: $font-size-md;

    .card-header,
    .card-footer {
        background-color: #fff;
    }

    .card-header {
        line-height: 1.2;
        color: #444;
        font-weight: 800;
        text-transform: uppercase;
        padding-bottom: 1rem;
        padding-top: 1rem;

        .row {
            align-items: center;
        }

        .title {
            line-height: $title-line-height;
            color: $title-color;
            font-weight: $title-font-weight;
            text-transform: uppercase;

            h2 {
                font-size: $font-size-base;
                line-height: $title-line-height;
                color: $title-color;
                font-weight: $title-font-weight;
                margin-bottom: 0px;
            }   
        }
    }

    .card-body {

        .title {
            color: #444;
            font-weight: 800;
        }

        .text {
            font-family: "Poppins";
            line-height: 1.8;
            color: #777;
        }

        .item {
            &:not(:last-child) {
                border-bottom: 1px solid #ddd;
            }
        }
    }

    .card-footer {
        border-top: unset;
        padding-bottom: 1rem;
        padding-top: 0px;
    }
}