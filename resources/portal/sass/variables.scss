@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@600;700;800&family=Poppins:wght@400;600;700&display=swap');

// $theme-colors: (
//     "primary": #FA6819,//orange
//     "secondary": #009FE3,//blue
//     "tertiary": #41B57A,//green
// );

$enable-negative-margins: true;

$primary: #FA6819;
$secondary : #009FE3;

$custom-colors: (
  "primary": #FA6819, //orange
  "secondary" : #009FE3, //blue
  "tertiary": #41B57A //green
);

// Merge the maps
$theme-colors: map-merge(
    $theme-colors, 
    $custom-colors
);

$container-max-widths: (
  sm: 90%,
  md: 90%,
  lg: 960px,
  xl: 1140px,
  xxl: 1420px
);

$aspect-ratios: (
    "1x1": 100%,

    "1x2": calc(2 / 1 * 100%),
    "2x1": calc(1 / 2 * 100%),

    "3x1": calc(1 / 3 * 100%),
    "1x3": calc(3 / 1 * 100%),

    "3x2": calc(2 / 3 * 100%),
    "2x3": calc(3 / 2 * 100%),

    "3x4": calc(4 / 3 * 100%),
    "4x3": calc(3 / 4 * 100%),

    "16x9": calc(9 / 16 * 100%),
);

// @each $ratio, $ratio-value in $aspect-ratios {

//     @each $media, $media-value in $grid-breakpoints {

//         @include media-breakpoint-up(#{$media}) { 

//             .ratio-#{$media}-#{$ratio} {
//                 display: none !important;
//             }
//         }
//     }
// }

$font-size-base: 1rem;

$h1-font-size: $font-size-base * 2.125;
$h2-font-size: $font-size-base * 1.875;
$h3-font-size: $font-size-base * 1.625;
$h4-font-size: $font-size-base * 1.375;
$h5-font-size: $font-size-base * 1.25;
$h6-font-size: $font-size-base * 1.125;

$font-sizes: (
  1: $h1-font-size,
  2: $h2-font-size,
  3: $h3-font-size,
  4: $h4-font-size,
  5: $h5-font-size,
  6: $h6-font-size
);

$font-size-sm : 0.75rem;
$font-size-md : 0.875rem;
$font-size-lg : 1rem;

$text-color : #777;
$title-color : #444;

$text-line-height : 1.8;
$title-line-height : 1.2;

$text-font-weight : 400;
$title-font-weight : 700;

$style-default-bg: #FCFAF8;
$style-second-bg: #F9F9F9;

$font-family: 'Poppins';
$btn-font-size: 14px;
$btn-font-size-mobile: 12px;