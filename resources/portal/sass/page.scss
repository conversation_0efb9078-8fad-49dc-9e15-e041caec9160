.page {

    margin-top: 90px;
    padding-top: 40px;
    padding-bottom: 50px;

    .page-top {
        display: flex;
        justify-content: space-between;
    }

    .page-heading {

        padding-bottom: 16px;
        
        .page-title {
            margin-bottom: 12px;
        }
    }

    .page-aside {

        &.page-aside-sticky {
            align-self: flex-start;
            position: sticky;
            top: 119px;
        }

        
    }
}


@include media-breakpoint-down(md) { 

    .page {
        margin-top: 151px;
    }
}

@include media-breakpoint-down(lg) { 
    .page-aside {
        padding-top: 25px;
    }
}

@include media-breakpoint-down(xl) { 

    .page {
        padding-top: 25px;
    }
}