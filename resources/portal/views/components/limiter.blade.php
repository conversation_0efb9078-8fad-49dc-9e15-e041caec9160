
@if ($count >= $paginationOptions[0])

    <div class="col-auto mb-n2">
        <div class="row d-flex align-items-center">

            <select class="col form-select result-limiter ms-3" id="result-limiter">

                @for ($i = 0; $i < count($paginationOptions); ++$i)
                    
                    @if ($count >= $paginationOptions[$i] ||
                        ($count < $paginationOptions[$i] && !$isExeeced) ||
                        $filterData >= $paginationOptions[$i])

                        @php
                            $name = $paginationOptions[$i]
                        @endphp

                        @if ($count < $paginationOptions[$i])
                            @php
                                $isExeeced = true;
                                $name = 'Alles';
                            @endphp
                        @endif
                        @php
                
                        $selected = $filterData == $paginationOptions[$i] 
                            ? ' selected' 
                            : '';
                        @endphp

                        <option value="{{$paginationOptions[$i] }}"<?=$selected;?>><?=$name;?></option>
                    @endif
                    
                @endfor

            </select>

            <label class="col-auto" for="result-limiter">Items per pagina</label>
        </div>
    </div>
    
@endif