@if ($any->count() >= $maxItems)
    <div class="col mb-n2 text-end float-right">
        <nav>
            <ul class="pagination justify-content-end">

                <li data-page="1" class="page-item flex-y-center">
                    <i class="fas fa-angle-double-left"></i>
                </li>
                
                <li data-page="{{ $currentPage - 1 }}" class="page-item flex-y-center">
                    <i class="fas fa-angle-left"></i>
                </li>

                @if ($any->total() > $maxItems)

                    <ul class="pagination justify-content-end">

                        @foreach (range($leftItems, $rightItems) as $currentPage)

                            <li data-page="{{$currentPage}}" class="page-item flex-y-center {{ $currentPage == $any->currentPage() ? 'active' : '' }}">
                                <div class="page-link">{{ $currentPage }}</div>
                            </li>

                        @endforeach

                    </ul>
                    
                @endif

                <li data-page="{{ $currentPage + 1 }}" class="page-item flex-y-center">
                    <i class="fas fa-angle-right"></i>
                </li>

                <li data-page="{{ $any->lastPage() }}" class="page-item flex-y-center">
                    <i class="fas fa-angle-double-right"></i>
                </li>
            </ul>
        </nav>
    </div>
@endif