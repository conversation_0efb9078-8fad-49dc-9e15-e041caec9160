
<div class="row text-decoration-none item mx-0 px-1 py-3 cart-items">
    <div class="col-3 col-md-2">

        <div class="images">

            <a data-fancybox="gallery" href="{{$data['image']}}">
                <div class="ratio ratio-1x1 ratio-cover main-image rounded">
                    <img class="rounded p-1" src="{{$data['image']}}" alt="employee-image" />
                </div>
            </a>

            <div class="row pt-2 gx-1">
                <div class="col-6">
                    <div class="image rounded">
                        <a data-fancybox="gallery" href="{{$data['image']}}">
                            <div class="ratio ratio-1x1 ratio-cover ">
                                <img class="p-1" src="{{$data['image']}}"/>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="col-6">
                    <div class="image rounded">
                        <a data-fancybox="gallery" href="{{$data['image']}}">
                            <div class="ratio ratio-1x1 ratio-cover ">
                                <img class="p-1" src="{{$data['image']}}"/>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-9 col-md-10">
        <div class="row">    
            <div class="col">
                <div class="title mb-2">{{$data['title']}}</div>
            </div>
            <div class="col-3 amount text-end d-none d-lg-block">
                € 127,16
            </div>
        
            <div class="col-2 col-lg-1 text-end">
        
                @if(isset($removeable) && $removeable)
                
                <div class="remove-cart">
                    <i class="fa-solid fa-xmark"></i>
                </div>
                
                <div class="edit">
                    <i class="fa-solid fa-pen-to-square"></i>
                </div>
                
                @else 
                    &nbsp;
                @endisset
                
            </div>
        </div>
        <div class="row">
            <div class="col product">
                <div class="specs">
                    <div class="row mb-n1 mb-md-2 ">
                        <div class="col-5 col-md-4 text">Reference:</div>
                        <div class="col-7 col-md-8 value">{{$data['reference']}}</div>
                    </div>
                    <div class="row mb-n1 mb-md-2 ">
                        <div class="col-5 col-md-4 text">Size:</div>
                        <div class="col-7 col-md-8 value">{{$data['size']}}</div>
                    </div>
                    <div class="row mb-n1 mb-md-2 ">
                        <div class="col-5 col-md-4 text">Confection:</div>
                        <div class="col-7 col-md-8 value">{{$data['confection']}}</div>
                    </div>
                    <div class="row mb-n1 mb-md-2 ">
                        <div class="col-5 col-md-4 text">Printed:</div>
                        <div class="col-7 col-md-8 value">{{$data['printed']}}</div>
                    </div>
                    <div class="row mb-n1 mb-md-2 ">
                        <div class="col-5 col-md-4 text">Contour cutting:</div>
                        <div class="col-7 col-md-8 value">{{$data['countour_cutting']}}</div>
                    </div>
                    <div class="row mb-n1 mb-md-2 ">
                        <div class="col-5 col-md-4 text">Package:</div>
                        <div class="col-7 col-md-8 value">{{$data['packaging']}}</div>
                    </div>
                    <div class="row mb-n1 mb-md-2 ">
                        <div class="col-5 col-md-4 text">Number:</div>
                        <div class="col-7 col-md-8 value">{{$data['numbers']}}</div>
                    </div>
                    <div class="row mb-n1 mb-md-2 ">
                        <div class="col-5 col-md-4 text">Price per item:</div>
                        <div class="col-7 col-md-8 value">{{$data['price_per_item']}}</div>
                    </div>
                    
                    @isset ($data['items'])
                        <div class="mb-n1 mb-md-2 mt-2 mt-md-2 ms-3">
                            @foreach ($data['items'] as $k => $items)
                                <div class="title mb-2">{{$k}}</div>
        
                                    @foreach ($items as $title => $value)
                                        <div class="row mb-n1 mb-md-2 ">
                                            <div class="col-5 col-md-4 text">{{$title}}: </div>
                                            <div class="col-7 col-md-8 value">{{$value}}</div>
                                        </div>
                                    @endforeach
                            @endforeach
                        </div>
                    @endisset
        
                    <div class="row mb-n1 mb-lg-2 d-lg-none">
                        <div class="col-5 col-md-4 text">Price:</div>
                        <div class="col-7 col-md-8 value">€ 127,16</div>
                    </div>
                </div>
            </div>
        </div>
        
    </div>

    @if(isset($editable) && $editable)
        <div class="col-12 offset-lg-2">
            <div class="mt-3">
                <a href="" class="btn btn-sm btn-outline-secondary btn-md-block mb-0 font-md upload">
                    Bestanden uploaden (3 / 6) 
                </a>
            </div>
        </div>
    @endif
</div>