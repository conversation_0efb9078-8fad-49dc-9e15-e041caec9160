@extends($app.'.layouts.app')
@section('content')
<div class="row">
    <div class="page-top col-12">
        <x-portal.breadcrumb page="Winkelwagen"/>
    </div>
    
    <div class="page-heading col-12">
        <div class="page-title">
            <h1>Winkelwagen</h1>
        </div>
    </div>
    
    <div class="col-12">
        <div class="block-checkout-steps my-3 row">

            <div class="step d-flex justify-content-center align-items-center py-2 active col-6 col-md-4">
                <span class="title">Winkelwagen</span>
                <i class="fas fa-chevron-right chevron ms-2"></i>
            </div>

            <div class="step d-flex justify-content-center align-items-center py-2 col-3 col-md-4">
                <span class="title">Checkout</span>
                <i class="fas fa-chevron-right chevron ms-2"></i>
            </div>

            <div class="step d-flex justify-content-center align-items-center py-2 col-3 col-md-4 ">
                <span class="title">Bevestiging</span>
                <i class="fas fa-chevron-right chevron ms-2"></i>
            </div>
        </div>
    </div>
    
    <div class="page-content col-12 col-lg-8">
        <div class="card card-panel ">
            <div class="card-header d-none d-lg-block">
                <div class="row align-items-center">
                    <div class="col-2 title text-truncate">Product</div>
                    
                    <div class="col text-end title text-truncate">
                        <div class="row justify-content-end">
                            <div class="col-3 text-end">Total amount</div>
                            <div class="col-1"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0 ">
                @foreach ($data as $item) 

                @include($app.'.templates.cart_item' , [
                    'data' => $item,
                    'removeable' => true,
                    'editable' => true,
                ])
                    
                @endforeach
            </div>
        </div>
    </div>
    
    <aside class="page-aside page-aside-sticky col-12 col-lg-4">
        <div class="row">
            <div class="col-12">
                <div class="card card-panel">

                    <div class="card-header">Totaalbedrag</div>

                    <div class="card-body">

                        <div class="row text">
                            <div class="col">Totaal excl. BTW</div>
                            <div class="col-auto text-end">€ 1.725,42</div>
                        </div>
                        <div class="row text">
                            <div class="col">Energie toeslag (€ 0,14 per m2)</div>
                            <div class="col-auto text-end">€ 5,74</div>
                        </div>
                        <div class="row text">
                            <div class="col">BTW 21%</div>
                            <div class="col-auto text-end">€ 363,54</div>
                        </div>



                        <hr class="my-2">

                        <div class="row text fw-bold">
                            <div class="col-8">Totaal</div>
                            <div class="col-4 text-end"> € 2.094,71</div>
                        </div>

                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="alert alert-warning mb-2">
                                    Voor één of meer items bevatten de printbestanden niet de vereiste snijpaden
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="alert alert-warning mb-0">
                                    Voor 1 of meer items zijn nog niet alle bestanden ingesteld!
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="card-footer to-checkout">
                        <a href="/afrekenen/" class="btn btn-tertiary w-100">Ga verder</a>
                    </div>
                </div>
            </div>
        </div>
    
    </aside>

</div>

@endsection