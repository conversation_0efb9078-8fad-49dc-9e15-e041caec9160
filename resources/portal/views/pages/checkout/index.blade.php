@extends($app.'.layouts.app')
@section('content')
<div class="row">
    <div class="page-top col-12">
        <x-portal.breadcrumb page="Afrekenen"/>
    </div>
    
    <div class="page-heading col-12">
        <div class="page-title">
            <h1>Afrekenen</h1>
        </div>
    </div>
    
    <div class="col-12">
        <div class="block-checkout-steps my-3 row">

            <div class="step d-flex justify-content-center align-items-center py-2 active col-6 col-md-4 checked">
                <i class="fas fa-check check"></i>
            </div>

            <div class="step d-flex justify-content-center align-items-center py-2 col-3 col-md-4 active">
                <span class="title">Afrekenen</span>
                <i class="fas fa-chevron-right chevron ms-2"></i>
            </div>

            <div class="step d-flex justify-content-center align-items-center py-2 col-3 col-md-4 ">
                <span class="title">Bevestiging</span>
                <i class="fas fa-chevron-right chevron ms-2"></i>
            </div>
        </div>
    </div>
    
    <div class="page-content col-12 col-lg-8">

        <div class="card card-panel rounded">
            <div class="card-header">
                <div class="title">
                    <h2>Order informatie</h2>
                </div>
                
            </div>

            <div class="card-body">

                <div class="row pb-3">
                    <div class="col-12 col-md-4 title">
                        <label for="reference" class="label-required">Referentie</label>
                    </div>
                    <div class="col-12 col-md text"> 
                        <input type="text" id="reference" placeholder="Reference" name="reference" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm id">
                    </div>
                </div>

                <div class="row pb-3">
                    <div class="col-12 col-md-4 title">
                        <label for="event">Stand/Event</label>
                    </div>
                    <div class="col-12 col-md text"> 
                        <input type="text" id="event" placeholder="event" name="event" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm id">
                    </div>
                </div>
            </div>
        </div>

        <div class="card card-panel rounded">
            <div class="card-header d-flex justify-content-between">
                <div class="title">
                    <h2>Bezorgadres</h2>
                </div>
                <i class="fas fa-pencil-alt color-green ms-2 btn-edit cursor-pointer float-right"></i>
            </div>

            <div class="card-body">

                <div 
                    class="font-md text checkout-data" 
                    data-edited="0" 
                    data-origin="TestCo BV<br />Dhr. Marc Hagens<br /><br /><EMAIL><br /><br />Schipholweg 939<br />2143CE Boesingheliede<br />Nederland">
                    TestCo BV
                    <br>
                    Dhr. Marc Hagens
                    <br><br>
                    <EMAIL>
                    <br><br>
                    Schipholweg 939
                    <br>
                    2143CE Boesingheliede
                    <br>
                    Nederland
                </div>

                <div class="checkout-data-edit collapse">

                    <div class="address-deliver-customer">

                        <div class="row salution pb-3" data-value="male">

                            <span class="col-auto title d-block label label-required">Aanhef</span>

                            <div class="col-auto"> 
                                <input class="radio address-gender address-gender-male" type="radio" id="address_1_gender_male" name="address_1_gender" value="male" data-value="Dhr." checked="checked"> 
                                <label for="address_1_gender_male" class="label-text">Dhr.</label>
                            </div>

                            <div class="col-auto"> 
                                <input class="radio address-gender address-gender-female" type="radio" id="address_1_gender_female" name="address_1_gender" value="female" data-value="Mvr.">
                                <label for="address_1_gender_female" class="label-text">Mvr.</label> 
                            </div> 
                        </div> 

                        <div class="row pb-3"> 

                            <div class="col-12 col-md-4"> 

                                <div class="form-group"> 

                                    <label for="company" class="title">Bedrijfsnaam</label> 
                                    <input type="text" id="company" name="address_1_company" autocomplete="organization" class="form-control form-control-sm company" placeholder="Bedrijfsnaam" value="TestCo BV" data-value="TestCo BV"> 
                                </div> 

                            </div> 
                            <div class="col-12 col-md-4"> 
                                
                                <div class="form-group"> 

                                    <label for="address_1_firstname" class="label-required title">Voornaam</label> 
                                    <input type="text" id="address_1_firstname" placeholder="Uw voornaam" name="address_1_firstname" autocomplete="given-name" class="form-control form-control-sm firstname" value="Marc" data-value="Marc" novalidate="" label-required=""> 
                                </div> 
                            </div> 
                            <div class="col-12 col-md-4"> 
                                
                                <div class="form-group"> 

                                    <label for="address_1_lastname" class="label-required title">Achternaam</label>
                                    <input type="text" id="address_1_lastname" placeholder="Uw achternaam" name="address_1_lastname" autocomplete="family-name" class="form-control form-control-sm lastname" value="Hagens" data-value="Hagens" label-required=""> 
                                </div> 
                            </div> 
                        </div> 
                        
                        <div class="row pb-3"> 

                            <div class="form-group col-12 col-md-6"> 
                                <label for="address_1_email" class="title">E-mailadres</label> 
                                <input type="email" id="address_1_email" placeholder="<EMAIL>" name="address_1_email" autocomplete="email" class="form-control form-control-sm email" value="<EMAIL>" data-value="<EMAIL>" label-required=""> 
                            </div> 

                            <div class="form-group col-12 col-md-6"> 
                                <label for="address_1_phone" class="title">Telefoonnummer</label> 
                                <input type="text" id="address_1_phone" placeholder="Uw telefoonnummer" name="address_1_phone" autocomplete="tel" class="form-control form-control-sm phone" value="" data-value="">
                            </div> 
                        </div> 
                    </div> 

                    <div class="row pb-3"> 

                        <div class="form-group col-12 col-sm-4"> 
                            <label for="address_1_postalcode" class="label-required title">Postcode</label> 
                            <input type="text" id="address_1_postalcode" name="address_1_postalcode" class="address-postalcode form-control form-control-sm" value="2143CE" data-value="2143CE" autocomplete="postal-code" placeholder="1234 AB" label-required=""> 
                        </div> 

                        <div class="form-group col-12 col-sm-4"> 
                            <label for="address_1_housenumber" class="label-required title">Huisnummer</label> 
                            <input type="text" id="address_1_housenumber" name="address_1_housenumber" autocomplete="new-password" class="address-housenumber form-control form-control-sm" value="939" data-value="939" placeholder="Huisnummer" label-required=""> 
                        </div> 
                        
                        <div class="form-group col-12 col-sm-4"> 
                            <label for="address_1_addition" class="title">Toevoeging</label> 
                            <input type="text" id="address_1_addition" name="address_1_addition" autocomplete="new-password" class="address-addition form-control form-control-sm" value="" data-value="" placeholder="A"> 
                        </div> 
                    </div> 

                    <div class="row pb-3"> 

                        <div class="form-group col-12 col-sm-6"> 
                            <label for="address_1_street" class="label-required title">Straat</label>
                            <input type="text" id="address_1_street" placeholder="Uw straatnaam" name="address_1_street" autocomplete="new-password" class="address-street form-control form-control-sm" value="Schipholweg" data-value="Schipholweg" label-required="" readonly=""> 
                        </div> 

                        <div class="form-group col-12 col-sm-6"> 
                            <label for="address_1_city" class="label-required title">Plaats</label>
                            <input type="text" id="address_1_city" name="address_1_city" autocomplete="new-password" class="address-city form-control form-control-sm" value="Boesingheliede" data-value="Boesingheliede" placeholder="De plaatsnaam" label-required="" readonly=""> 
                        </div> 
                    </div>

                    <div class="row pb-3"> 

                        <div class="col-12 col-md"> 
                            <div class="custom-control custom-switch address-switch-container"> 
                                <input type="checkbox" class="custom-control-input address-switch" name="address_1_default" id="address-1-switch"> 
                                <label class="custom-control-label" for="address-1-switch">Opslaan in adresboek</label> 
                            </div> 
                        </div> 

                        <div class="col-12 col-md-auto float-right text-end"> 
                            <button type="button" class="btn btn-outline-tertiary btn-cancel btn-sm-block btn-md-inline my-3 my-md-0 me-md-2">Annuleren</button> 
                            <button type="button" class="btn btn-tertiary btn-ok btn-sm-block btn-md-inline ">Ok</button> 
                        </div> 
                    </div> 
                </div>
            </div>
        </div>

        <div class="card card-panel rounded">
            <div class="card-header">
                <div class="title">
                    <h2>
                        Afhalen of bezorgen
                    </h2>
                </div>
            </div>
            <div class="card-body">
                
                <div class="delivery-types mb-1"> 
                    <div class="row px-2 row-cols-2 row-cols-md-3">
                        <div class="col px-2 pb-3"> 
                            <div class="table-title border rounded mb-2 delivery-type h-100 delivery-type-many" data-type="delivery" data-carrier="combi"> 
                                <i class="me-3 icon-type fas fa-leaf"></i> 
                                <div class="naming position-relative title">
                                    <p>Combi shipment</p> 
                                </div> 
                                <div class="info">
                                    <i class="fas fa-info-circle">
                                        <div class="border bg-white p-3 rounded font-sm font-weight-normal">
                                            Combine the shipment with other orders, to reduce co<sup>2</sup> emissions
                                        </div>
                                    </i>
                                </div> 
                                <i class="icon-check fas fa-check"></i> 
                            </div> 
                        </div>
                        <div class="col px-2 pb-3"> 
                            <div class="table-title border rounded mb-2 delivery-type h-100 delivery-type-many" data-type="pickup" data-carrier="none"> <i class="me-3 icon-type fas fa-dolly"></i> 
                                <div class="naming position-relative title">
                                    <p>Pickup</p> 
                                </div> 
                                <div class="info"><i class="fas fa-info-circle">
                                    <div class="border bg-white p-3 rounded font-sm font-weight-normal">
                                        You can pick up the order on your chosen day from 16:00
                                    </div>
                                </i>
                            </div> 
                            <i class="icon-check fas fa-check"></i>
                        </div> 
                    </div>
                    <div class="col px-2 pb-3"> 
                        <div class="table-title border rounded mb-2 delivery-type h-100 delivery-type-many" data-type="delivery" data-carrier="courier"> 
                            <i class="me-3 icon-type fas fa-truck"></i> 
                            <div class="naming position-relative title">
                                <p>Courier</p> 
                            </div> 
                            <div class="info">
                                <i class="fas fa-info-circle">
                                    <div class="border bg-white p-3 rounded font-sm font-weight-normal">
                                        Choose this option for extra delivery security when the order has to be delivered to a trade fair or event.
                                    </div>
                                </i>
                            </div> 
                            <i class="icon-check fas fa-check"></i> 
                        </div> 
                    </div>
                    <div class="col px-2 pb-3"> 
                        <div class="table-title border rounded mb-2 delivery-type h-100 delivery-type-many" data-type="delivery" data-carrier="overnight"> 
                            <i class="me-3 icon-type fas fa-truck-loading"></i> 
                            <div class="naming position-relative title">
                                <p>Transport overnight</p> 
                            </div> 
                            <div class="info">
                                <i class="fas fa-info-circle">
                                    <div class="border bg-white p-3 rounded font-sm font-weight-normal">
                                        Choose this option for extra delivery security when the order has to be delivered to a trade fair or event.
                                    </div>
                                </i>
                            </div> 
                            <i class="icon-check fas fa-check"></i> 
                        </div> 
                    </div>
                    <div class="col px-2 pb-3"> 
                        <div class="table-title border rounded mb-2 delivery-type h-100 delivery-type-many" data-type="delivery" data-carrier="regular"> 
                            <i class="me-3 icon-type fab fa-ups"></i> 
                            <div class="naming position-relative title">
                                <p>Regular delivery</p> 
                            </div> 
                            <div class="info">
                                <i class="fas fa-info-circle">
                                    <div class="border bg-white p-3 rounded font-sm font-weight-normal">
                                        Your order will be delivered by regular delivery service.
                                    </div>
                                </i>
                            </div> 
                            <i class="icon-check fas fa-check"></i> 
                        </div> 
                    </div>
                </div> 
            </div>
            </div>
        </div>

        <div class="card shadow mb-3 delivery-time-panel card-panel">
            <div class="card-header"> 
                <div class="sidebar-head">Bezorgmoment</div> 
            </div> 
            <div class="card-body py-4 mb-n3"> 
                <div class="delivery-times position-relative mb-2 row">
                    <div class="col-6 col-md"> 
                        <div class="table-title border rounded px-3 py-4 mb-2 delivery-time" data-id="allday"> 
                            <div class="row"> 
                                <div class="col-12 col text-center">All</div> 
                                <div class="col-12 col text-center">day</div> 
                            </div> 
                            
                            <i class="icon-check fas fa-check"></i> 
                        </div> 
                    </div>
                    <div class="col-6 col-md"> 
                        <div class="table-title border rounded px-3 py-4 mb-2 delivery-time" data-id="1200"> 
                            <div class="row"> 
                                <div class="col-12 col text-center">Before</div> 
                                <div class="col-12 col text-center">12:00</div> 
                            </div> 
                            <i class="icon-check fas fa-check"></i> 
                        </div> 
                    </div>
                </div> 
            </div> 
        </div>

        <div class="card shadow mb-3 delivery-dates-panel card-panel" >
            <div class="card-header">
                <div class="sidebar-head delivery-date-title" data-type="pickup">Afhaaldatum</div>
            </div>
            <div class="card-body px-3 py-4">
                <div class="delivery-dates position-relative mb-2">
                    <div class="table-title border rounded px-3 py-4 mb-2 delivery-date" data-id="overnight.truck_vst"
                        data-date="2023-12-12" data-price="28">
                        <div class="row align-items-center justify-content-between">
                            <div class="col-4 date">
                                <p>Dinsdag 12 december</p>
                            </div>
                            <div class="col-auto price text-end pe-4">
                                <p>€ 28</p>
                            </div>
                        </div> <i class="icon-check fas fa-check"></i>
                    </div>
                    <div class="table-title border rounded px-3 py-4 mb-2 delivery-date" data-id="overnight.truck_vst"
                        data-date="2023-12-13" data-price="27.3">
                        <div class="row align-items-center justify-content-between">
                            <div class="col-4 date">
                                <p>Woensdag 13 december</p>
                            </div>
                            <div class="col-auto price text-end pe-4">
                                <p>€ 27.3</p>
                            </div>
                        </div> <i class="icon-check fas fa-check"></i>
                    </div>
                    <div class="table-title border rounded px-3 py-4 mb-2 delivery-date" data-id="overnight.truck_vst"
                        data-date="2023-12-14" data-price="26.6">
                        <div class="row align-items-center justify-content-between">
                            <div class="col-4 date">
                                <p>Donderdag 14 december</p>
                            </div>
                            <div class="col-auto price text-end pe-4">
                                <p>€ 26.6</p>
                            </div>
                        </div> <i class="icon-check fas fa-check"></i>
                    </div>
                    <div class="table-title border rounded px-3 py-4 mb-2 delivery-date" data-id="overnight.truck_vst"
                        data-date="2023-12-15" data-price="25.9">
                        <div class="row align-items-center justify-content-between">
                            <div class="col-4 date">
                                <p>Vrijdag 15 december</p>
                            </div>
                            <div class="col-auto price text-end pe-4">
                                <p>€ 25.9</p>
                            </div>
                        </div> <i class="icon-check fas fa-check"></i>
                    </div>
                    <div class="table-title border rounded px-3 py-4 mb-2 delivery-date" data-id="overnight.truck_vst"
                        data-date="2023-12-18" data-price="25.9">
                        <div class="row align-items-center justify-content-between">
                            <div class="col-4 date">
                                <p>Maandag 18 december</p>
                            </div>
                            <div class="col-auto price text-end pe-4">
                                <p>€ 25.9</p>
                            </div>
                        </div> <i class="icon-check fas fa-check"></i>
                    </div>
                    <div class="table-title border rounded px-3 py-4 mb-2 delivery-date" data-id="overnight.truck_vst"
                        data-date="2023-12-19" data-price="25.9">
                        <div class="row align-items-center justify-content-between">
                            <div class="col-4 date">
                                <p>Dinsdag 19 december</p>
                            </div>
                            <div class="col-auto price text-end pe-4">
                                <p>€ 25.9</p>
                            </div>
                        </div> <i class="icon-check fas fa-check"></i>
                    </div>
                    <div class="table-title border rounded px-3 py-4 mb-2 delivery-date" data-id="overnight.truck_vst"
                        data-date="2023-12-20" data-price="25.9">
                        <div class="row align-items-center justify-content-between">
                            <div class="col-4 date">
                                <p>Woensdag 20 december</p>
                            </div>
                            <div class="col-auto price text-end pe-4">
                                <p>€ 25.9</p>
                            </div>
                        </div> <i class="icon-check fas fa-check"></i>
                    </div>
                    <div class="table-title border rounded px-3 py-4 mb-2 delivery-date delivery-date-custom"
                        data-date="datepicker" data-id=""
                        data-dates="[{&quot;date&quot;:&quot;2023-12-21&quot;,&quot;date_due&quot;:&quot;2023-12-20&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:11,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;4&quot;},{&quot;date&quot;:&quot;2023-12-22&quot;,&quot;date_due&quot;:&quot;2023-12-21&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:12,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;5&quot;},{&quot;date&quot;:&quot;2023-12-25&quot;,&quot;date_due&quot;:&quot;2023-12-22&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:13,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;1&quot;},{&quot;date&quot;:&quot;2023-12-27&quot;,&quot;date_due&quot;:&quot;2023-12-25&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:14,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;3&quot;},{&quot;date&quot;:&quot;2023-12-28&quot;,&quot;date_due&quot;:&quot;2023-12-27&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:15,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;4&quot;},{&quot;date&quot;:&quot;2023-12-29&quot;,&quot;date_due&quot;:&quot;2023-12-28&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:16,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;5&quot;},{&quot;date&quot;:&quot;2024-01-02&quot;,&quot;date_due&quot;:&quot;2023-12-29&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:17,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;2&quot;},{&quot;date&quot;:&quot;2024-01-03&quot;,&quot;date_due&quot;:&quot;2024-01-02&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:18,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;3&quot;},{&quot;date&quot;:&quot;2024-01-04&quot;,&quot;date_due&quot;:&quot;2024-01-03&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:19,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;4&quot;},{&quot;date&quot;:&quot;2024-01-05&quot;,&quot;date_due&quot;:&quot;2024-01-04&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:20,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;5&quot;},{&quot;date&quot;:&quot;2024-01-08&quot;,&quot;date_due&quot;:&quot;2024-01-05&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:21,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;1&quot;},{&quot;date&quot;:&quot;2024-01-09&quot;,&quot;date_due&quot;:&quot;2024-01-08&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:22,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;2&quot;},{&quot;date&quot;:&quot;2024-01-10&quot;,&quot;date_due&quot;:&quot;2024-01-09&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:23,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;3&quot;},{&quot;date&quot;:&quot;2024-01-11&quot;,&quot;date_due&quot;:&quot;2024-01-10&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:24,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;4&quot;},{&quot;date&quot;:&quot;2024-01-12&quot;,&quot;date_due&quot;:&quot;2024-01-11&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:25,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;5&quot;},{&quot;date&quot;:&quot;2024-01-15&quot;,&quot;date_due&quot;:&quot;2024-01-12&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:26,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;1&quot;},{&quot;date&quot;:&quot;2024-01-16&quot;,&quot;date_due&quot;:&quot;2024-01-15&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:27,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;2&quot;},{&quot;date&quot;:&quot;2024-01-17&quot;,&quot;date_due&quot;:&quot;2024-01-16&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:28,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;3&quot;},{&quot;date&quot;:&quot;2024-01-18&quot;,&quot;date_due&quot;:&quot;2024-01-17&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:29,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;4&quot;},{&quot;date&quot;:&quot;2024-01-19&quot;,&quot;date_due&quot;:&quot;2024-01-18&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:30,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;5&quot;},{&quot;date&quot;:&quot;2024-01-22&quot;,&quot;date_due&quot;:&quot;2024-01-19&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:31,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;1&quot;},{&quot;date&quot;:&quot;2024-01-23&quot;,&quot;date_due&quot;:&quot;2024-01-22&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:32,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;2&quot;},{&quot;date&quot;:&quot;2024-01-24&quot;,&quot;date_due&quot;:&quot;2024-01-23&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:33,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;3&quot;},{&quot;date&quot;:&quot;2024-01-25&quot;,&quot;date_due&quot;:&quot;2024-01-24&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:34,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;4&quot;},{&quot;date&quot;:&quot;2024-01-26&quot;,&quot;date_due&quot;:&quot;2024-01-25&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:35,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;5&quot;},{&quot;date&quot;:&quot;2024-01-29&quot;,&quot;date_due&quot;:&quot;2024-01-26&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:36,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;1&quot;},{&quot;date&quot;:&quot;2024-01-30&quot;,&quot;date_due&quot;:&quot;2024-01-29&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:37,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;2&quot;},{&quot;date&quot;:&quot;2024-01-31&quot;,&quot;date_due&quot;:&quot;2024-01-30&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:38,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;3&quot;},{&quot;date&quot;:&quot;2024-02-01&quot;,&quot;date_due&quot;:&quot;2024-01-31&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:39,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;4&quot;},{&quot;date&quot;:&quot;2024-02-02&quot;,&quot;date_due&quot;:&quot;2024-02-01&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:40,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;5&quot;},{&quot;date&quot;:&quot;2024-02-05&quot;,&quot;date_due&quot;:&quot;2024-02-02&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:41,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;1&quot;},{&quot;date&quot;:&quot;2024-02-06&quot;,&quot;date_due&quot;:&quot;2024-02-05&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:42,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;2&quot;},{&quot;date&quot;:&quot;2024-02-07&quot;,&quot;date_due&quot;:&quot;2024-02-06&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:43,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;3&quot;},{&quot;date&quot;:&quot;2024-02-08&quot;,&quot;date_due&quot;:&quot;2024-02-07&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:44,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;4&quot;},{&quot;date&quot;:&quot;2024-02-09&quot;,&quot;date_due&quot;:&quot;2024-02-08&quot;,&quot;date_now&quot;:&quot;2023-12-05&quot;,&quot;days_delivery&quot;:1,&quot;days&quot;:45,&quot;carrier&quot;:&quot;overnight.truck_vst&quot;,&quot;price&quot;:25.9,&quot;price_original&quot;:28,&quot;price_percentage&quot;:-7.5,&quot;day&quot;:&quot;5&quot;}]"
                        data-date-picked="" data-months="3">
                        <div class="row">
                            <div class="col date">
                                <p class="no-date">Kies een datum!</p>
                                <p class="date-picked collapse"></p>
                            </div>
                            <div class="col-auto price text-end pe-4">
                                <p></p>
                            </div>
                        </div>
                        <div class="datepicker-container">
                            <div class="row">
                                <div class="col-12">
                                    <div class="datepicker"></div>
                                </div>
                            </div>
                        </div> <i class="icon-check fas fa-check"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="card card-panel rounded">

            <div class="card-header">
                <div class="title">
                    <h2>
                        Extra informatie
                    </h2>
                </div>
            </div>

            <div class="card-body">

                <div class="row mb-3">
                    
                    <div class="col-12 col-md-4"> 
                        <label for="extra_information" class="title">Opmerkingen</label> 
                    </div>
                    
                    <div class="col-12 col-md font-md"> 
                        <div class="row"> 
                            <div class="col-12"> 
                                <textarea rows="3" placeholder="Extra informatie voor deze bestelling" name="extra_information" class="form-control form-control-sm" maxlength="150"></textarea> 
                            </div> 
                        </div> 
                    </div> 

                </div>
                
                <div class="row mb-3"> 
                    
                    <div class="col-12 col-md-4"> 
                        <label for="extra_information_delivery" class="title">Opmerkingen bezorging</label> 
                    </div> 
                    
                    <div class="col-12 col-md font-md"> 
                        <div class="row"> 
                            <div class="col-12"> 
                                <textarea rows="3" placeholder="Extra bezorginformatie voor deze bestelling" name="extra_information_delivery" class="form-control form-control-sm" maxlength="150"></textarea> 
                            </div> 
                        </div> 
                    </div> 
                </div>

                <div class="row mb-3 align-items-center"> 

                    <div class="col-12 col-md-4"> 
                        <label class="title">Eigen pakbon</label> 
                    </div> 

                    <div class="col-12 col-md mt-2 mt-md-0 px-0"> 

                        <div class="container-fluid fileselector" data-class="upload-packing-slip"> 

                            <input type="hidden" class="cache_file" name="cache_upload_packing_slip" value=""> 

                            <div class="row"> 

                                <div class="col-12 col-md-auto btn-container"> 
                                    <div class="btn btn-tertiary btn-upload-packing-slip btn-sm-block btn-md-inline">
                                        Upload pakbon
                                    </div> 
                                </div>

                                <div class="col-10 col-md upload-packing-slip-link ps-3 px-0 px-md-3 py-1 font-md align-self-center table-item text-truncate d-none"></div>

                                <div class="col-2 col-md-auto align-self-center text-end">
                                    <i class="fas fa-trash ps-2 upload-packing-slip-delete cursor-pointer d-none font-xl"></i>
                                </div>

                                <input type="file" name="upload_packing_slip" id="file-upload-packing-slip" class="sr-only"> 
                            </div> 
                        </div> 
                    </div> 
                </div>

                <div class="row mb-3 align-items-center"> 

                    <div class="col-12 col-md-4"> 
                        <label class="title">Extra bestand #1</label> 
                    </div> 

                    <div class="col-12 col-md mt-2 mt-md-0 px-0"> 

                        <div class="container-fluid fileselector" data-class="upload-packing-slip"> 

                            <input type="hidden" class="cache_file" name="cache_upload_packing_slip" value=""> 

                            <div class="row"> 

                                <div class="col-12 col-md-auto btn-container"> 
                                    <div class="btn btn-tertiary btn-upload-packing-slip btn-sm-block btn-md-inline">
                                        Upload pakbon
                                    </div> 
                                </div>

                                <div class="col-10 col-md upload-packing-slip-link ps-3 px-0 px-md-3 py-1 font-md align-self-center table-item text-truncate d-none"></div>

                                <div class="col-2 col-md-auto align-self-center text-end">
                                    <i class="fas fa-trash ps-2 upload-packing-slip-delete cursor-pointer d-none font-xl"></i>
                                </div>

                                <input type="file" name="upload_packing_slip" id="file-upload-packing-slip" class="sr-only"> 
                            </div> 
                        </div> 
                    </div> 
                </div>
                
                <div class="row mb-3 align-items-center"> 

                    <div class="col-12 col-md-4"> 
                        <label class="title">Extra bestand #2</label> 
                    </div> 

                    <div class="col-12 col-md mt-2 mt-md-0 px-0"> 

                        <div class="container-fluid fileselector" data-class="upload-packing-slip"> 

                            <input type="hidden" class="cache_file" name="cache_upload_packing_slip" value=""> 

                            <div class="row"> 

                                <div class="col-12 col-md-auto btn-container"> 
                                    <div class="btn btn-tertiary btn-upload-packing-slip btn-sm-block btn-md-inline">
                                        Upload pakbon
                                    </div> 
                                </div>

                                <div class="col-10 col-md upload-packing-slip-link ps-3 px-0 px-md-3 py-1 font-md align-self-center table-item text-truncate d-none"></div>

                                <div class="col-2 col-md-auto align-self-center text-end">
                                    <i class="fas fa-trash ps-2 upload-packing-slip-delete cursor-pointer d-none font-xl"></i>
                                </div>

                                <input type="file" name="upload_packing_slip" id="file-upload-packing-slip" class="sr-only"> 
                            </div> 
                        </div> 
                    </div> 
                </div>

                <div class="row mb-3 align-items-center"> 

                    <div class="col-12 col-md-4"> 
                        <label class="title">Extra bestand #3</label> 
                    </div> 

                    <div class="col-12 col-md mt-2 mt-md-0 px-0"> 

                        <div class="container-fluid fileselector" data-class="upload-packing-slip"> 

                            <input type="hidden" class="cache_file" name="cache_upload_packing_slip" value=""> 

                            <div class="row"> 

                                <div class="col-12 col-md-auto btn-container"> 
                                    <div class="btn btn-tertiary btn-upload-packing-slip btn-sm-block btn-md-inline">
                                        Upload pakbon
                                    </div> 
                                </div>

                                <div class="col-10 col-md upload-packing-slip-link ps-3 px-0 px-md-3 py-1 font-md align-self-center table-item text-truncate d-none"></div>

                                <div class="col-2 col-md-auto align-self-center text-end">
                                    <i class="fas fa-trash ps-2 upload-packing-slip-delete cursor-pointer d-none font-xl"></i>
                                </div>

                                <input type="file" name="upload_packing_slip" id="file-upload-packing-slip" class="sr-only"> 
                            </div> 
                        </div> 
                    </div> 
                </div>
            </div>
        </div>
    </div>
    
    <aside class="page-aside page-aside-sticky col-12 col-lg-4">
        <div class="row">
            <div class="col-12">
                <div class="card card-panel">

                    <div class="card-header text-uppercase">
                        <div class="title">
                            <h2>Besteloverzicht</h2>
                        </div>
                    </div>

                    <div class="card-body">

                        <div class="row text">
                            <div class="col-3">
                                <div class="ratio ratio-1x1">
                                    <img src="{{URL::asset('/img/silicon-edge.jpg')}}" alt="checkout">
                                </div>
                            </div>
                            <div class="col ps-3">
                                <span class="title text-color">BlockOut Textile BlackBack Stretch 320 DS</span>
                                <br>
                                <span class="fst-italic">Rondom slimframe pees</span>
                                <br>
                                <span><u>Doek 1</u></span>
                                <br>
                                <span>200 x 200 cm, 1x</span>
                            </div>
                        </div>

                        

                        <hr class="my-2">

                        <div class="row text fw-bold">
                            <div class="col-8">Totaal</div>
                            <div class="col-4 text-end"> € 2.094,71</div>
                        </div>

                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="alert alert-warning mb-2">
                                    U hebt nog niet alle benodigde informatie verstrekt
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    
    </aside>

</div>

@endsection