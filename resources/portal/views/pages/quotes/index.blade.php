@extends($app.'.layouts.app')
@section('content')
<div class="row">
    <div class="page-top col-12">
        <x-portal.breadcrumb page="Quotes"/>
    </div>
    
    <div class="page-heading col-12">
        <div class="page-title">
            <h1>Quotes</h1>
        </div>
    </div>
    
    <div class="page-content col-12">
        <div class="row justify-content-end pb-4">
            <div class="col-12 col-lg-4">
                <div class="form-icon-container">
                    <input 
                        type="text" 
                        class="form-control font-md" 
                        name="dashboard-orders-search" 
                        id="dashboard-orders-search" 
                        placeholder="Search by reference or quotation number">
                    <label for="dashboard-orders-search" class="d-flex justify-content-center align-items-center">
                        <i class="fas fa-search"></i>
                    </label>
                </div>
            </div>

            <div class="col-12 pt-3 pt-lg-0 col-lg-4">
                <div class="text-end align-self-end align-self-lg-center order-4 order-lg-3">
                    <div class="container">
                        <div class="row mb-0">
                            <label class="col-auto align-self-center title fw-bold font-md" for="result-sorting">Sorting:</label>
        
                            <select class="col form-select result-sorting font-md" id="result-sorting">
                                <option value="id desc" selected>Quotation number Descending</option>
                                <option value="id asc">Quotation number Ascending</option>
                                <option value="date desc">Delivery date Descending</option>
                                <option value="date asc">Delivery date Ascending</option>
                                <option value="amount desc">Total amount Descending</option>
                                <option value="amount asc">Total amount Ascending</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card card-panel overview">
            <div class="card-header">
                <div class="row ">
                    <div class="col-12 col-lg-1 title text-truncate d-none d-lg-block">Quotation number</div>
                    <div class="col-6 col-lg   title text-truncate">Reference</div>
                    <div class="col-6 col-lg-2 title text-truncate text-end">Total amount</div>
                    <div class="col-12 col-lg-2 title text-truncate text-end d-none d-lg-block">Quotation date</div>
                    <div class="col-12 col-lg-2 title text-truncate text-end d-none d-lg-block">Delivery date</div>
                    <div class="col-12 col-lg-2 title text-truncate text-end d-none d-lg-block">Status</div>

                </div>
            </div>
            <div class="card-body p-0 ">
                @foreach ($data as $item)
                    <a href="offertes/specifiek" class="row text-decoration-none item mx-0 px-1 py-3 align-items-center">
                        <div class="col-2 col-lg-1 text order-0 text-decoration-underline d-none d-lg-block">{{ $item['id'] }}</div>
                        <div class="col-7 col-lg title order-1 text-decoration-underline">{{ $item['title'] }}</div>
                        <div class="col-5 col-lg-2 text  order-2 text-end">{{ $item['amount'] }}</div>
                        <div class="col-12 col-lg-2 text order-3 order-lg-2 text-end d-none d-lg-block">{{ $item['start_date'] }}</div>
                        <div class="col-12 col-lg-2 text order-4 order-lg-3 text-end d-none d-lg-block">{{ $item['end_date'] }}</div>
                        <div class="col-3 col-lg-2  text order-2 order-lg-4 text-end d-none d-lg-block">
                            <div class="badge badge-status fw-bold">{{ $item['status'] }}</div>
                        </div>
                    </a>
                @endforeach                
            </div>
        </div>
    </div>

</div>

@endsection