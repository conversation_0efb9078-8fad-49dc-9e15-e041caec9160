@extends($app . '.layouts.app')
@section('content')
    <div class="row">
        <div class="page-top col-12">
            <x-portal.breadcrumb page="Importer" />
        </div>

        <div class="page-heading col-12">
            <div class="page-title">
                <div class="row">
                    <div class="col-auto">
                        <h1 class="mb-0">Importer</h1>
                    </div>
                    <div class="col-auto ms-auto">
                        <button class="btn btn-primary">Nieuwe import</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-content col-12">
            <p>
                Op deze pagina kan u zelf simpel en efficient XML &amp; PDF orderlijsten importeren.&nbsp;<br>
                Wij raden u aan per product type een aparte 'sheet' of pagina te maken zodat u minder tijd kwijt bent aan
                het koppelen van de orderregels aan de juiste product configuraties.&nbsp;<br><br>
                <strong>TIP: </strong>
                Heeft u veel print-bestanden! U kan gelijktijdig alvast beginnen met het uploaden hiervan door linksonderin
                te klikken op de 'Hub Uploader' knop.
            </p>

            <div class="card card-panel">

                <div class="card-header px-0">

                    <div class="row mx-1">
                        <div class="col col-md-2 table-head text-truncate">Bestandnaam</div>
                        <div class="col-4 col-md-4 table-head d-none d-md-block">Worksheet</div>
                        <div class="col-2 table-head text-truncate text-end d-none d-md-block">Status</div>
                        <div class="col-2 col-lg-2 table-head text-truncate text-end d-none d-md-block">Geimporteerd</div>
                        <div class="col-2 table-head text-truncate text-end">Opties</div>
                    </div>
                </div>

                <div class="card-body py-0 ">

                    <div class="row">

                        <div class="col font-md d-flex align-items-center text  py-2">
                            656edddd73f99_camp-2-verdeellijst-backdrops-van-straaten-xlsx.url
                        </div>

                        <div class="col-2 col-lg-1 font-md text text-end py-2">
                            <button type="button" data-id="3" class="ml-2 btn btn-sm btn-outline-primary btn-import-delete" title="Import verwijderen">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>

                        <div class="col-12 px-2">
                            
                            <div class="row border-top d-flex align-items-center py-2">

                                <div class="col-3 col-md-2 font-md text-end text ">
                                    <i class="fas fa-level-up-alt fa-rotate-90"></i>
                                </div>

                                <div class="col-4 font-md table-item">
                                    <a href="/importer/worksheets">Backdrops</a>
                                </div>
                                <div class="col-2 font-md table-item text-end">-</div>
                                <div class="col-2 font-md table-item text-end">-</div>
                                <div class="col-2 me-auto font-md table-item text-end"></div>
                            </div>
                        </div>

                        <div class="col-12 px-2">
                            
                            <div class="row border-top d-flex align-items-center py-2">

                                <div class="col-3 col-md-2 font-md text-end text ">
                                    <i class="fas fa-level-up-alt fa-rotate-90"></i>
                                </div>

                                <div class="col-4 font-md table-item">
                                    <a href="/importer/worksheets">Import</a>
                                </div>
                                <div class="col-2 font-md table-item text-end">-</div>
                                <div class="col-2 font-md table-item text-end">-</div>
                                <div class="col-2 me-auto font-md table-item text-end"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
@endsection
