@extends($app . '.layouts.app')
@section('content')
    <div class="row">

        <div class="page-top col-12">
            <x-portal.breadcrumb page="Importer / Worksheets" />
        </div>

        <div class="page-heading col-12">
            <div class="page-title">
                <div class="row">
                    <h1 class="mb-0">Configuratie</h1>
                </div>
            </div>
        </div>

        <div class="col-12">
            <div class="block-checkout-steps mb-3 row">
                <div class="step d-flex justify-content-center align-items-center py-2 col-4 checked"> 
                    <span class="title">Koppelen</span> <i class="ms-2 fas fa-check check"></i> 
                </div>
                <div class="step d-flex justify-content-center align-items-center py-2 col-4 active">
                    <span class="title">Configureren</span>
                    <i class="fas fa-chevron-right chevron ms-2"></i>
                </div>
                <div class="step d-flex justify-content-center align-items-center py-2 col-4 ">
                    <span class="title">Importeren</span>
                    <i class="fas fa-chevron-right chevron ms-2"></i>
                </div>
            </div>
        </div>

        <div class="page-content col-12">

            <p>Selecteer hieronder de 'worksheet' om de gegevens te bekijken en importeren.</p>

            <div class="card card-panel">

                <div class="card-header">

                    <div class="title">

                        <h2>Kolommen koppelen</h2>
                    </div>
                </div>
                
                <div class="card-body">

                    <div class="row pb-3">

                        <div class="col-5">
                            <div class="text font-lg">Omschrijving</div>
                        </div>

                        <div class="col-7">
                            <div class="dropdown bootstrap-select form-control">
                                <select name="column_reference" class="form-control selectpicker" tabindex="null">

                                    <option value="0">Niet geselecteerd</option>
                                    <option value="42" selected>Breedte in cm</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row pb-3">

                        <div class="col-5">
                            <div class="text font-lg">Breedte</div>
                        </div>

                        <div class="col-4 col-md-5">
                            <div class="dropdown bootstrap-select form-control">
                                <select name="column_reference"
                                    class="form-control selectpicker" tabindex="null">
                                    <option value="0">Niet geselecteerd</option>
                                    <option value="42" selected>Breedte in cm</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-3 col-md-2">
                            <div class="dropdown bootstrap-select form-control">
                                <select name="column_width_unittype" class="form-control selectpicker"> 
                                    <option value="MM">MM</option><option value="CM" selected="">CM</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row pb-3">

                        <div class="col-5">
                            <div class="text font-lg">Hoogte</div>
                        </div>

                        <div class="col-4 col-md-5">
                            <div class="dropdown bootstrap-select form-control">
                                <select name="column_reference"
                                    class="form-control selectpicker" tabindex="null">
                                    <option value="0">Niet geselecteerd</option>
                                    <option value="42" selected>Breedte in cm</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-3 col-md-2">
                            <div class="dropdown bootstrap-select form-control">
                                <select name="column_width_unittype" class="form-control selectpicker"> 
                                    <option value="MM">MM</option><option value="CM" selected="">CM</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row pb-3">

                        <div class="col-5">
                            <div class="text font-lg">Aantal</div>
                        </div>

                        <div class="col-7">
                            <div class="dropdown bootstrap-select form-control">
                                <select name="column_reference"
                                    class="form-control selectpicker" tabindex="null">
                                    <option value="0">Niet geselecteerd</option>
                                    <option value="42" selected>Breedte in cm</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row pb-3">

                        <div class="col-5">
                            <div class="text font-lg">Bestanden</div>
                        </div>

                        <div class="col-7">
                            <div class="dropdown bootstrap-select form-control">
                                <select name="column_reference"
                                    class="form-control selectpicker" tabindex="null">
                                    <option value="0">Niet geselecteerd</option>
                                    <option value="42" selected>Breedte in cm</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row pb-3">

                        <div class="col-5">
                            <div class="text font-lg">Opmerkingen</div>
                        </div>

                        <div class="col-7">
                            <div class="dropdown bootstrap-select form-control">
                                <select name="column_reference"
                                    class="form-control selectpicker" tabindex="null">
                                    <option value="0">Niet geselecteerd</option>
                                    <option value="42" selected>Breedte in cm</option>
                                </select>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="row">

                <div class="col-6 text-start mt-3">
                    <a href="/importer/worksheets" name="script" value="back" class="btn btn-outline-primary btn-import-back">Terug</a>
                </div>
    
                <div class="col-6 text-end mt-3">
                    <a href="/importer/worksheets/importeren" name="script" value="next" class="btn btn-primary btn-import-next">Volgende stap</a>
                </div>

            </div>
        </div>


    </div>
@endsection
