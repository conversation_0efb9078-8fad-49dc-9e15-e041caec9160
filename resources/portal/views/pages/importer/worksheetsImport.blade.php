@extends($app . '.layouts.app')
@section('content')
    <div class="row">

        <div class="page-top col-12">
            <x-portal.breadcrumb page="Importer / Worksheets" />
        </div>

        <div class="page-heading col-12">
            <div class="page-title">
                <div class="row">
                    <h1 class="mb-0">Worksheets</h1>
                </div>
            </div>
        </div>

        <div class="col-12">
            
            <div class="block-checkout-steps mb-3 row">

                    <div class="step d-flex justify-content-center align-items-center py-2 col-3 col-4 checked">
                        <span class="title">Koppelen</span>
                        <i class="ms-2 fas fa-check check"></i>
                    </div>

                    <div class="step d-flex justify-content-center align-items-center py-2 col-4 checked">
                        <span class="title">Configureren</span>
                        <i class="ms-2 fas fa-check check"></i>
                    </div>

                    <div class="step d-flex justify-content-center align-items-center py-2 col-4 active">
                        <span class="title">Importeren</span>
                        <i class="fas fa-chevron-right chevron ms-2"></i>
                    </div>
            </div>
        </div>

        <div class="page-content col-12">

            <p>Selecteer hieronder de 'worksheet' om de gegevens te bekijken en importeren.</p>

            <div class="row mb-3">

                <div class="col text-end">
                    <button name="select_all" class="btn btn-sm btn-primary btn-select-all">Select all</button>
                    <button name="deselect_all" class="btn btn-sm btn-primary btn-deselect-all">De-select all</button>
                    <button name="inverse_selection" class="btn btn-sm btn-primary btn-inverse-selection ml-2">Inverse
                        selection</button>
                </div>

            </div>

            <div class="card card-panel">

                <div class="card-header">
                    <div class="row">
                        <div class="col">Omschrijving</div>
                        <div class="col-1 text-end px-0 d-none d-md-block">Aantal</div>
                        <div class="col-2 table-head text-end ps-0 pe-2 col-3 col-md-1 " title="Breedte x Hoogte">B x H</div>
                        <div class="col-4 table-head px-2 d-none d-md-block">Bestand</div>
                        <div class="col-2 table-head px-2 d-none d-md-block">Opmerkingen</div>
                    </div>
                </div>

                <div class="card-body p-0">

                    <div class="row py-2 row-warning " data-id="452">
                        <div class="col font-sm">
                            <div class="row ">
                                <div class="col-auto d-flex align-items-center">
                                    <div class="custom-control custom-switch custom-switch-overview d-flex align-items-center">
                                        <input type="checkbox"
                                            class="custom-control-input import-switch" name="row[452]" value="1"
                                            data-id="452" id="row[452]"><label class="custom-control-label"
                                            for="row[452]"></label></div>
                                </div>
                                <div class="col">1_1085MUNT_1-1_M-01_4800x2400</div>
                            </div>
                        </div>

                        <div class="col-2 table-item text-end font-sm pe-2 d-none d-md-block">1x</div>

                        <div class="col-3 col-md-1 table-item text-end font-sm ">480 x 240cm</div>

                        <div class="col-4 table-item font-sm px-2 d-none d-md-block">

                            <div class="row">

                                <div class="col text-truncate"><i class="fas fa-check text-danger pe-2"
                                        title="Het bijhorende printbestand is niet gevonden"></i><i
                                        class="fas fa-check text-danger pe-2"
                                        title="De afmetingen van het print bestand komen niet overeen met het gewenste eind formaat"></i>230314-we-men-city-rgb-v01
                                </div>

                            </div>
                        </div>

                        <div class="col-2 table-item font-sm text-truncate px-2 d-none d-md-block"></div>
                    </div>
                </div>
            </div>

            <div class="row">

                <div class="col-6 text-start mt-3">
                    <a href="/importer/worksheets/configureren" name="script" value="back"
                        class="btn btn-outline-primary btn-import-back">Terug</a>
                </div>

                <div class="col-6 text-end mt-3">
                    <a href="/importer" name="script" value="next"
                        class="btn btn-primary btn-import-next">Volgende stap</a>
                </div>

            </div>
        </div>

    </div>
@endsection
