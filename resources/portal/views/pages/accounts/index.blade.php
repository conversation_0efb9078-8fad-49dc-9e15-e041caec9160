@extends($app.'.layouts.app')
@section('content')
<form action="{{ route('account.store') }}" method="POST" enctype="multipart/form-data">

@csrf
<input type="hidden" name="id" value="{{ isset($user) ? $user->id : '' }}">
<input type="hidden" name="locale_current" value="{{ app()->getLocale() }}">

<div class="row">
    <div class="page-top col-12">
        <x-portal.breadcrumb page="Instellingen / Account"/>
    </div>
    
    <div class="page-heading col-12">
        <div class="page-title">
            <div class="row">
                <div class="col-auto">
                    <h1 class="mb-0">{{$user->getFullName()}}</h1>
                </div>
                <div class="col-auto ms-auto">
                    <button class="btn btn-tertiary">Opslaan</button>
                </div>
            </div>
        </div>
    </div>

    @if (!is_null(session('message')))
        <div class="block-message">
            <div class="alert alert-success mb-4" role="alert">
                {{session('message')}}
            </div>
        </div>
    @endif

    <aside class="page-aside d-none d-lg-block col-lg-3">
        <div class="row">
            <div class="col-6 col-md-12">
                @include($app.'.templates.menu_account')
            </div>
        </div>
    
    </aside>

    <div class="page-content col-12 col-lg-9">
        <div class="card card-panel rounded">
            <div class="card-header">
                <div class="row">
                    <div class="col-auto d-center">
                        Account
                    </div>
                </div>
                
            </div>

            <div class="card-body">

                <div class="row pb-3">

                    <div class="col-auto title">
                        Aanhef
                    </div>

                    <div class="col-9 text">
                        <div class="row">
                            <div class="col-auto">
                                <input class="radio address-gender address-gender-male" type="radio" id="address_1_gender_male" name="address_1_gender" value="male" data-value="Dhr." {{ ($user->gender == 'male') ? 'checked' : '' }}>
                                <label for="address_1_gender_male" class="label-text">Dhr.</label>
                            </div>
                            <div class="col-auto">
                                <input class="radio address-gender address-gender-female" type="radio" id="address_1_gender_female" name="address_1_gender" value="female" data-value="Mvr." {{ ($user->gender == 'female') ? 'checked' : '' }}>
                                <label for="address_1_gender_female" >Mvr.</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row pb-3">
                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="address_1_firstname" class="title label-required">Voornaam</label>
                            <input type="text" id="address_1_firstname" placeholder="Uw voornaam" name="address_1_firstname" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm firstname" value="{{$user->firstname}}" data-value="{{$user->firstname}}">
                        </div> 
                    </div> 
                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="address_1_lastname" class="title label-required">Achternaam</label> 
                            <input type="text" id="address_1_lastname" placeholder="Uw achternaam" name="address_1_lastname" autocomplete="family-name" class="form-control form-control-sm lastname" value="{{$user->lastname}}" data-value="{{$user->lastname}}"> 
                        </div> 
                    </div> 
                </div>

                <div class="row pb-3"> 
                    <div class="form-group col-12 col-md-6"> 
                        <label for="address_1_email" class="title">E-mailadres</label>
                        <input type="email" id="address_1_email" placeholder="<EMAIL>" name="address_1_email" autocomplete="email" class="form-control form-control-sm email mb-3 mb-md-0" value="{{$user->email}}" data-value="{{$user->email}}"> 
                    </div> 
                    <div class="form-group col-12 col-md-6" > 
                        <label for="address_1_phone" class="title">Telefoonnummer</label> 
                        <input type="text" id="address_1_phone" placeholder="Uw telefoonnummer" name="address_1_phone" autocomplete="tel" class="form-control form-control-sm phone" value="{{$user->phonenumber}}" data-value=""{{$user->phonenumber}}">
                    </div> 
                </div>
            </div>
        </div>
    </div>
    

</div>

</form>
@endsection