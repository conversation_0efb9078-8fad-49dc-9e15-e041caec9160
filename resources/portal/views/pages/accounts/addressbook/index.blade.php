@extends($app.'.layouts.app')
@section('content')
<div class="row">
    <div class="page-top col-12">
        <x-portal.breadcrumb page="Instellingen / Adresboek"/>
    </div>
    
    <div class="page-heading col-12">
        <div class="page-title">
            <div class="row d-center">
                <div class="col-auto"><h1 class="mb-0">Adresboek</h1></div>
                <div class="col-auto ms-auto"><a href="/account/adres/bewerken" class="btn btn-tertiary">Nieuw adres</a></div>
            </div>
        </div>
    </div>

    <aside class="page-aside d-none d-lg-block col-lg-3">
        <div class="row">
            <div class="col-6 col-md-12">
                @include($app.'.templates.menu_account')
            </div>
        </div>
    
    </aside>

    <div class="page-content col-12 col-lg-9">
    
        <div class="card card-panel overview">
            <div class="card-header">
                <div class="row ">
                    <div class="col-4 title text-uppercase text-truncate">Naam</div>
                    <div class="col col-md-4 title text-uppercase text-truncate">Adres</div>
                    <div class="col-auto title d-none d-md-block text-uppercase text-truncate">Contact</div>
                    <div class="col-auto col-md title text-uppercase text-truncate text-end">Acties</div>
    
                </div>
            </div>
            <div class="card-body p-0 ">
                @foreach ($data as $item)
                    <div class="row text-decoration-none item mx-0 px-1 py-3">

                        <div class="col-4 text text-truncate">

                            <div class="d-block">
                                {{$item['firstname']}} {{$item['lastname']}}
                            </div>

                            <div class="d-block">
                                {{$item['company']}}
                            </div>
                        </div>

                        <div class="col col-md-4 text text-truncate">
                            
                            <div class="d-block">
                                {{$item['street']}} {{$item['housenumber']}}
                            </div>

                            <div class="d-block">
                                {{$item['zipcode']}} {{$item['city']}}
                            </div>

                            <div class="d-block">
                                {{$item['country']}}
                            </div>
                        </div>

                        <div class="col-auto  d-none d-md-block text text-truncate">
                            
                            <a href="tel:{{$item['phonenumber']}}"class="contact-link d-block">
                                {{$item['phonenumber']}}
                            </a>

                            <a href="mailto:{{$item['email']}}" class="contact-link d-block" >
                                {{$item['email']}}
                            </a>

                        </div>
                        
                        <div class="col-auto col-md text text-end controls">

                            <a href="/account/adres/bewerken" class="text-decoration-none" >
                                <i class="fa-regular fa-pen-to-square"></i>
                            </a>

                            <a href="/account/adres/bewerken" class="text-decoration-none" >
                                <i class="fa-solid fa-trash"></i>
                            </a>
                        </div>
                    </div>
                @endforeach                
            </div>
        </div>
    </div>
    

</div>

@endsection