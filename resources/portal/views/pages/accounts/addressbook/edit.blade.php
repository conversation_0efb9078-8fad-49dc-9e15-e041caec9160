@extends($app.'.layouts.app')
@section('content')
<div class="row">
    <div class="page-top col-12">
            <x-portal.breadcrumb page="Instellingen / Adresboek / Toevoegen"/>
    </div>
    
    <div class="page-heading col-12">
        <div class="page-title">
            <div class="row d-center">
                <div class="col-auto"><h1 class="mb-0">Adresboek</h1></div>
                <div class="col-auto ms-auto"><a href="/account/contactpersonen/bewerken" class="btn btn-tertiary">Opslaan</a></div>
            </div>
        </div>
    </div>

    <aside class="page-aside d-none d-lg-block col-lg-3">
        <div class="row">
            <div class="col-6 col-md-12">
                @include($app.'.templates.menu_account')
            </div>
        </div>
    
    </aside>

    <div class="page-content col-12 col-lg-9">
        <div class="card card-panel rounded">
            <div class="card-header">
                <div class="row">
                    <div class="col-auto d-center">
                        Adres toevoegen
                    </div>
                </div>
                
            </div>

            <div class="card-body">

                <div class="row pb-3">
                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="company_name" class="title label-required">Type adres</label>
                            <select class="col form-select form-select-sm lang">
                                <option value="id desc">Bezorgadres</option>
                                <option value="id desc">Facuutradres</option>
                                <option value="id desc">Algemeen adres</option>
                            </select>
                        </div> 
                    </div>
                </div>

                <div class="row pb-3">
                    
                    <div class="col-12"> 
                        <div class="form-group"> 
                            <div class="row">
                                <div class="col-12">

                                    <label for="address_1_gender" class="title">Aanhef</label>

                                </div>
                                <div class="col-12">

                                    <input class="radio address-gender address-gender-female" type="radio" id="address_1_gender_female" name="address_1_gender" value="female" data-value="Mvr.">
                                    <label for="address_1_gender_female" >Mevr.</label>

                                    <input class="radio address-gender address-gender-male ms-2" type="radio" id="address_1_gender_male" name="address_1_gender" value="male" data-value="Dhr." checked="">
                                    <label for="address_1_gender_male" class="label-text">Dhr.</label>

                                    <input class="radio address-gender address-gender-male ms-2" type="radio" id="address_1_gender_male" name="address_1_gender" value="male" data-value="Dhr." checked="">
                                    <label for="address_1_gender_male" class="label-text">T.a.v.</label>
                                </div>
                            </div>

                        </div> 
                    </div> 

                </div>

                <div class="row pb-3">
                    
                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="id" class="title ">Bedrijfsnaam</label>
                            <input type="text" id="id" placeholder="Kamans" name="id" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm id">
                        </div> 
                    </div> 
                </div>
                
                <div class="row pb-3">
                    
                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="id" class="title label-required">Voornaam</label>
                            <input type="text" id="id" placeholder="Kamans" name="id" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm id">
                        </div> 
                    </div> 
                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="id" class="title label-required">Achternaam</label>
                            <input type="text" id="id" placeholder="Kamans" name="id" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm id">
                        </div> 
                    </div> 
                </div>

                
                <div class="row pb-3">
                    
                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="id" class="title">E-mailadres</label>
                            <input type="text" id="id" placeholder="Kamans" name="id" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm id">
                        </div> 
                    </div> 

                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="id" class="title">Telefoonnummer</label>
                            <input type="text" id="id" placeholder="Kamans" name="id" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm id">
                        </div> 
                    </div> 
                </div>

                <div class="row pb-3 mt-5">
                    
                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="id" class="title label-required">Land</label>
                            <input type="text" id="id" placeholder="Kamans" name="id" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm id">
                        </div> 
                    </div> 
                </div>
                <div class="row pb-3">
                    
                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="id" class="title">Postcode</label>
                            <input type="text" id="id" placeholder="Kamans" name="id" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm id">
                        </div> 
                    </div> 

                    <div class="col-12 col-md-3"> 
                        <div class="form-group"> 
                            <label for="id" class="title label-required">Huisnummer</label>
                            <input type="text" id="id" placeholder="Kamans" name="id" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm id">
                        </div> 
                    </div> 
                    <div class="col-12 col-md-3"> 
                        <div class="form-group"> 
                            <label for="id" class="title label-required">Toevoeging</label>
                            <input type="text" id="id" placeholder="Kamans" name="id" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm id">
                        </div> 
                    </div> 
                </div>

                <div class="row pb-3">
                    
                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="id" class="title label-required">Straat</label>
                            <input type="text" readonly id="id" placeholder="Kamans" name="id" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm id">
                        </div> 
                    </div> 

                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="id" class="title label-required">Plaats</label>
                            <input type="text" readonly id="id" placeholder="Kamans" name="id" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm id">
                        </div> 
                    </div> 
                </div>
            </div>
        </div>
    </div>
    

</div>

@endsection