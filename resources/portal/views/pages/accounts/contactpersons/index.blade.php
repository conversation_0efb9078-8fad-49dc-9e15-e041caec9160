@extends($app.'.layouts.app')
@section('content')
<div class="row">
    <div class="page-top col-12">
        <x-portal.breadcrumb page="Instellingen / Contactpersonen"/>
    </div>
    
    <div class="page-heading col-12">
        <div class="page-title">
            <div class="row d-center">
                <div class="col-auto"><h1 class="mb-0">Contactpersonen</h1></div>
                <div class="col-auto ms-auto"><a href="/account/contactpersonen/bewerken" class="btn btn-tertiary">Nieuw contactpersoon</a></div>
            </div>
        </div>
    </div>

    <aside class="page-aside d-none d-lg-block col-lg-3">
        <div class="row">
            <div class="col-6 col-md-12">
                @include($app.'.templates.menu_account')
            </div>
        </div>
    
    </aside>

    <div class="page-content col-12 col-lg-9">
    
        <div class="card card-panel overview">
            <div class="card-header">
                <div class="row ">
                    <div class="col-5 title text-uppercase text-truncate">Naam</div>
                    <div class="col title text-uppercase text-truncate">Functie</div>
                    <div class="col title text-uppercase text-truncate text-end">Acties</div>
    
                </div>
            </div>
            <div class="card-body p-0 ">
                @foreach ($data as $item)
                    <a href="contactpersonen/bewerken" class="row text-decoration-none item mx-0 px-1 py-3 align-items-center">
                        <div class="col-5 title fw-normal text-decoration-underline">{{ $item['name'] }}</div>
                        <div class="col text ">{{ $item['function'] }}</div>
                        <div class="col text text-end controls">
                            <i class="fa-regular fa-pen-to-square"></i>
                            <i class="fa-solid fa-trash"></i>
                        </div>
                    </a>
                @endforeach                
            </div>
        </div>
    </div>
    

</div>

@endsection