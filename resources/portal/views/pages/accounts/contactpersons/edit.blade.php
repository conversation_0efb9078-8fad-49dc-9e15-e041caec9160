@extends($app.'.layouts.app')
@section('content')
<div class="row">
    <div class="page-top col-12">
            <x-portal.breadcrumb page="Instellingen / Contactpersonen / Toevoegen"/>
    </div>
    
    <div class="page-heading col-12">
        <div class="page-title">
            <div class="row d-center">
                <div class="col-auto"><h1 class="mb-0">Contactpersoon</h1></div>
                <div class="col-auto ms-auto"><a href="/account/contactpersonen/bewerken" class="btn btn-tertiary">Opslaan</a></div>
            </div>
        </div>
    </div>

    <aside class="page-aside d-none d-lg-block col-lg-3">
        <div class="row">
            <div class="col-6 col-md-12">
                @include($app.'.templates.menu_account')
            </div>
        </div>
    
    </aside>

    <div class="page-content col-12 col-lg-9">
        <div class="card card-panel rounded">
            <div class="card-header">
                <div class="row">
                    <div class="col-auto d-center">
                        Contactpersonen toevoegen
                    </div>
                </div>
                
            </div>

            <div class="card-body">


                <div class="row pb-3">

                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="company_name " class="title label-required">Rol</label>
                            <select class="col form-select form-select-sm lang">
                                <option value="id desc" selected>Beheerder</option>
                                <option value="id desc" selected>Medewerker</option>
                            </select>
                        </div> 
                    </div> 

                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="company_name " class="title label-required">Functie</label>
                            <select class="col form-select form-select-sm lang">
                                <option value="id desc" selected>Developer</option>
                                <option value="id desc" selected>Manager</option>
                                <option value="id desc" selected>Secetaresse</option>
                            </select>
                        </div> 
                    </div> 
                </div>
                <div class="row pb-3">
                    
                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="company_name" class="title label-required">Voornaam</label>
                            <input type="text" id="company_name" placeholder="Freek" name="company_name" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm name">
                        </div> 
                    </div> 
                    
                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="id" class="title label-required">Achternaam</label>
                            <input type="text" id="id" placeholder="Kamans" name="id" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm id">
                        </div> 
                    </div> 

                </div>
                <div class="row pb-3">
                    
                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="company_name" class="title">E-mailadres</label>
                            <input type="text" id="company_name" placeholder="<EMAIL>" name="company_name" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm name" >
                        </div> 
                    </div> 
                    
                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="id" class="title">Telefoonnummer</label>
                            <input type="text" id="id" placeholder="060612345678" name="id" autocomplete="given-name" class="mb-3 mb-md-0 form-control form-control-sm id">
                        </div> 
                    </div> 

                </div>
                <div class="row pb-3">
                    
                    <div class="col-12 col-md-6"> 
                        <div class="form-group"> 
                            <label for="id" class="title">Taal</label>
                            <select class="col form-select form-select-sm lang">
                                <option value="id desc" selected>Nederlands</option>
                            </select>
                        </div> 
                    </div> 

                </div>
            </div>
        </div>
    </div>
    

</div>

@endsection