@extends($app.'.layouts.login')
@section('content')


<div class="row justify-content-center mt-4" >
    <div class="col col-lg-5">
        <div class="card card-front">

            <div class="card-header">
                <h1 class="title">Klantselectie</h1>
            </div>
            
            <div class="card-body px-4">
                <div class="row row-cols-2 row-cols-xl-3 g-5">
                    
                    @foreach ($companies as $company)
                        <div class="col">

                            @php
                                $isActive = isset($currentCompanyId) && $currentCompanyId == $company->id;
                            @endphp

                            <{{ $isActive ? 'div' : 'a' }} class="border rounded row company-block h-100 pt-3{{ $isActive ? ' active' : '' }}" {{ $isActive ? '' : "href=" . route('login.companies', $company->slugName) }}>

                                <i class="icon-check fas fa-check px-0"></i>

                                <div class="col-12 pt-2">
                                    <div class="ratio ratio-2x1 ">
                                        <img src="{{ URL::asset('/img/logo.png') }}" class="rounded border" alt="logo">
                                    </div>
                                </div>

                                <div class="col-12 py-2 mt-auto">
                                    <div class="d-flex justify-content-center align-items-center company-name">
                                        {{ $company->name }}
                                    </div>
                                </div>

                            </{{ $isActive ? 'div' : 'a' }}>
                        </div>
                    
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endsection