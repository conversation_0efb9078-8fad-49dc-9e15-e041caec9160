@extends($app.'.layouts.login')
@section('content')

    <div class="row justify-content-center mt-4 mt-lg-5">
        <div class="col col-lg-6">

            @if ($message)
                <div class="alert alert-danger">
                    {{ $message }}
                </div>
            @else

            <form action="{{ route('auth.activation.activate', $token) }}" method="POST" enctype="multipart/form-data">
                @csrf

                <div class="card card-front">
                    <div class="card-header">
                        <h1 class="title">Account activeren</h1>
                    </div>

                    <div class="card-body">
                        <x-admin.alert/>

                        <div class="text">
                            Maak je account compleet door een wachtwoord aan te maken.
                            Het nieuwe wachtwoord moet uit minimaal 8 tekens bestaan,
                            waarvan minimaal 1 hoofdletter en 1 cijfer.
                        </div>

                        <input type="hidden" name="hash" value="{{ $token }}">

                        <div class="form-group">
                            <label for="password">Nieuw wachtwoord</label>
                            <div class="input-group ">
                                <input 
                                    type="password" 
                                    class="form-control border-end-0"
                                    id="password" 
                                    placeholder="Nieuw wachtwoord" 
                                    name="password" 
                                    value="" 
                                >
                                <span class="input-group-text">
                                    <a class="toggle-password">
                                        <i class="fa fa-eye-slash" aria-hidden="true"></i>
                                    </a>
                                </span>
                            </div>
                            <span class="password-checker"></span>
                        </div>

                        <div class="password-group form-group mb-2">
                            <label for="password">Herhaal wachtwoord</label>
                            <div class="password-checker-container">
                                <div class="input-group">
                                    <input 
                                        type="password" 
                                        class="form-control border-end-0"
                                        id="password_confirmation" 
                                        placeholder="Herhaal wachtwoord" 
                                        name="password_confirmation" 
                                        value="" 
                                    >
                                    <span class="input-group-text">
                                        <a class="toggle-password">
                                            <i class="fa fa-eye-slash" aria-hidden="true"></i>
                                        </a>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-tertiary" name="reset_password">Verzenden</button>
                        <input type="hidden" name="form" value="send" />
                    </div>
                </div>
            </form>
            @endif
        </div>
    </div>
@endsection
