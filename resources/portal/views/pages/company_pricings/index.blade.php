@extends($app.'.layouts.app')
@section('content')

@php
    use App\Models\Product;
    use App\Classes\Webshop;
@endphp

<div class="row">
    <div class="page-top col-12">
        <x-portal.breadcrumb page="Prijsafspraken"/>
    </div>
    <div class="page-content col-12">
        <h2 class="pt-3">{{ Temp::translate('page.company_pricings.heading.title_company_pricings') }}</h2>

        <div class="card card-dashboard">
            <div class="card-body pb-3 pt-3 px-4">
                @if($pricings->count() < 1)
                    {{ Temp::translate('page.company_pricings.text.no_pricings')}}
                @else 
                    @foreach ($pricings as $pricing)
                        <div>
                            <div>
                                @php
                                
                                $confection = $pricing->confection;
                                $substrate = $pricing->substrate;

                                $type = 
                                    ($pricing['product_substrate_id'] == 0 && $pricing['product_convection_id'] != 0) ? 'confection_no_substrate' 
                                    : 
                                    (($pricing['product_convection_id'] == 0 && $pricing['product_substrate_id'] != 0) ? 'substrate_no_confection' 
                                    : 
                                    (($pricing['product_convection_id'] != 0 && $pricing['product_substrate_id'] != 0) ? 'confection_with_substrate' 
                                    : '')
                                );
                                

                                $params = [
                                    'confection' => '<b>'.($confection->{'title_'.App::getLocale()} ?? '').'</b>',
                                    'substrate' => '<b>'.($substrate->{'title_'.App::getLocale()} ?? '').'</b>'
                                ];
                                        
                                @endphp

                                {!! Temp::translate('page.company_pricings.text.'.$type, $params) !!}
                            </div>
                        
                            <div>
                                @php

                                $unit = 
                                    (!is_null($pricing->unit) && !empty($pricing->unit->value)) 
                                     ? $pricing->unit->value[0] . '<sup>' . $pricing->unit->value[1] . '</sup>' 
                                     : ''
                                ;

                                $params = [
                                    'height' => '<b>'.($pricing['amount_start'] / 10).'</b>',
                                    'width' => '<b>'.($pricing['amount_stop'] / 10).'</b>',
                                    'amount_start' => '<b>'.$pricing['amount_start'].'</b>',
                                    'amount_end' => '<b>'.$pricing['amount_stop'].'</b>',
                                    'unit' => '<abbr><b>'.$unit.'</b></abbr>'
                                ];

                                @endphp

                                {!! Temp::translate('page.company_pricings.text.discount_' . $pricing->conditioning->value, $params) !!}
                            </div>

                            <div>
                                @php
                                $backValue = (is_null($pricing['value_back'])) ? '' : '_with_back_value';
                                $percentageSubstract = '';
                                
                                $countries = config('modules.products.pricing.countries');
                                $selectedCountry = collect($countries)->where('id', session()->get('company')['country_id'])->first();

                                $amount = $pricing['value'];
                                $amountBack = $pricing['value_back'];

                                if ($pricing['discount']->value == 'percentage') {

                                    if ($pricing['direction']->value == '-') {

                                        $percentageSubstract = "_substract_percentage";

                                        $amount = intval($pricing['value']) . '%';
                                        $amountBack = intval($pricing['value_back']) . '%';
                                        
                                    } 
                                    else {
                                        if ($pricing['type']->value == 'substrate_confection' || ($pricing['product_convection_id'] != 0 && $pricing['product_substrate_id'] != 0)) {

                                            $params = [
                                                'country' => $selectedCountry['code'],
                                                'sector_id' => session()->get('company')['sector_id'],
                                                'groupcode' => session()->get('company')['groupcode'],
                                            ];

                                            $substratePrice = Product::getPricing(['product_id' => $pricing['product_substrate_id']] + $params)[0]['price'];
                                            $confectionPrice = Product::getPricing(['product_id' => $pricing['product_convection_id']] + $params)[0]['price'];

                                            $price = $substratePrice + $confectionPrice;
                                        }

                                        if ($pricing['type']->value == 'substrate' || $pricing['type']->value == 'confection') {

                                            $productId = ($pricing['type']->value == 'substrate') ? $pricing['product_substrate_id'] : $pricing['product_convection_id'];
                                            


                                            $price = Product::getPricing([
                                                'product_id' => $productId,
                                                'country' => $selectedCountry['code'],
                                                'sector_id' => session()->get('company')['sector_id'],
                                                'groupcode' => session()->get('company')['groupcode'],
                                            ])[0]['price'];
                                        }

                                        $amount = Webshop::getLocalePrice($price * (($pricing['value'] / 100) + 1));
                                        $amountBack = Webshop::getLocalePrice($price * (($pricing['value_back'] / 100) + 1));
                                    }

                                } else {

                                    $amount = Webshop::getLocalePrice($pricing['value']);
                                    $amountBack = Webshop::getLocalePrice($pricing['value_back']);
                                }

                                $params = [
                                    'amount' => '<b>'.$amount.'</b>',
                                    'amount_back' => '<b>'.$amountBack.'</b>'
                                ];
                                
                                @endphp

                                {!! Temp::translate('page.company_pricings.text.amount'.$percentageSubstract.$backValue, $params) !!}

                            </div>

                            @if (!$loop->last)
                                <hr>
                            @endif
                            
                        </div>
                    @endforeach
                @endif

            </div>
        </div>

        <h2 class="pt-3">{{ Temp::translate('page.company_pricings.heading.title_company_transport_pricings')}}</h2>

        <div class="card card-dashboard">
            <div class="card-body pb-3 pt-3 px-4">
                @if($shipments->count() < 1)
                    {{ Temp::translate('page.company_pricings.text.no_transport_pricings')}}
                @else 
                    @foreach ($shipments as $shipment)
                        <div>
                            <div>
                                @php
                                $amount = [
                                    1 => 'one',
                                    2 => 'some',
                                    3 => 'many',
                                ];

                                $methods = explode(',', $shipment['methods']);
                                
                                foreach ($methods as $key => $method) {
                                    
                                    $methods[$key] = Temp::translate('global.shipment.text.'.str_replace('.','_',strtolower($method)));

                                    if ($method == 'overnight.1200') {
                                        $methods[$key] = Temp::translate('global.shipment.text.overnight_time');
                                    }
                                }
                                
                                $amount = $amount[min(count($methods), count($amount))];

                                $params = [
                                    'shipment1' => '<b>'.$methods[0].'</b>',
                                    'shipment2' => '<b>'.($methods[1] ?? '').'</b>',
                                    'shipments' => '<b>'.(implode(', ' ,$methods)).'</b>'
                                ];
                                @endphp
                                
                                {!! Temp::translate('page.company_pricings.text.transport_'.$amount , $params) !!}
                            </div>
                            <div>
                                @php
                                $days = [];

                                $shipment['days'] = ($shipment['days'] == '1,2,3,4,5') ? 'business_days' : $shipment['days'];

                                foreach (explode(',', $shipment['days']) as $dayId ) {

                                    if ($dayId == 'all') {
                                        $days[] = Temp::translate('page.company_pricings.text.all_days');
                                    }
                                    else if ($dayId == 'business_days') {
                                        $days[] = Temp::translate('page.company_pricings.text.all_working_days');
                                    }
                                    else {
                                        $days[] = Temp::translate('page.company_pricings.text.day_'.$dayId);
                                    }
                                }
                                
                                $amount = [
                                    1 => 'one',
                                    2 => 'some',
                                    3 => 'many',
                                ];
                                
                                $amount = ($shipment['days'] == 'business_days' || $shipment['days'] == 'all' ) ? $amount[3] : $amount[min(count($days), count($amount))];
                                
                                $params = [
                                    'day1' => '<b>'.$days[0].'</b>',
                                    'day2' => '<b>'.($days[1] ?? '').'</b>',
                                    'days' => '<b>'.implode(', ' , $days).'</b>'
                                ];

                                echo Temp::translate('page.company_pricings.text.transport_days_'.$amount , $params)
                                @endphp
                            </div>
                            <div>
                                <div>
                                    @php
                                    
                                    $params = [
                                        'amount_start' => '<b>'.App\Classes\Webshop::getLocalePrice($shipment['amount_start']).'</b>',
                                        'amount_end' => '<b>'.App\Classes\Webshop::getLocalePrice($shipment['amount_stop']).'</b>',
                                    ];

                                    echo Temp::translate('page.company_pricings.text.discount_condition_shipment_'.$shipment['conditioning']->value, $params);

                                    @endphp
                                </div>
                            </div>
                            <div>
                                @php

                                $params = [
                                    'amount' => '<b>' . (($shipment['discount']->value == 'fixed') ? App\Classes\Webshop::getLocalePrice($shipment['value']) :intval( $shipment['value']).'%') . '</b>'
                                ];
                                
                                if($shipment['direction'] == '+'){
                                    //todo add up extra ask anne
                                }

                                echo Temp::translate('page.company_pricings.text.amount_shipment_'.$shipment['discount']->value, $params);
                                @endphp
                                
                            </div>
                        </div>

                        @if (!$loop->last)
                            <hr>
                        @endif

                    @endforeach
                @endif
            </div>
        </div>
    </div>
</div>

@endsection