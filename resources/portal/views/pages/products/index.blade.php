@extends($app.'.layouts.app')
@section('content')

<div class="row gx-5 data-container">

    <div class="page-top col-12">
        <x-portal.breadcrumb page="Products"/>
    </div>
    
    <div class="page-heading col-12">
        <div class="page-title">
            <h1>Products</h1>
        </div>
    </div>
    
    <aside class="page-aside col-12 col-lg-3 ">
        <div class="filters-overlay">

            <div class="filters-header">
                <div class="title">Filter</div>
                <div class="close">
                    <i class="fa-solid fa-xmark"></i>
                </div>
            </div>

            <nav class="filters">
                <div class="sort-mobile border-bottom">
                    <div class="title">Sorteren op</div>
                    <div class="d-flex ">
                        <div class="sort-pill rounded-pill">Naam A tot Z</div>
                        <div class="sort-pill rounded-pill">Naam Z tot A</div>
                    </div>
                </div>

                <ul>
                    @foreach ($filters as $filter)
    
                    @php
                        $open = '';
                        $static = '';

                        $dropDownIcon = '
                            <div>
                                <i class="fa-solid fa-chevron-down"></i>
                            </div>';
                            
                        $collapsed = "collapsed collapse";
    
                        if (isset($filter['is_open']) && $filter['is_open']) {
                            $open = ' filter-open';
                            $collapsed = '';
                        }
    
                        if (isset($filter['is_static']) && $filter['is_static']) {
                            $static = ' filter-static';
                            $dropDownIcon = '';
                        }
    
                    @endphp
                    
                    <li 
                        class="filter{{$open}}{{$static}}" 
                        data-id="filter-{{$filter['id']}}" 
                        data-type="{{$filter['type']}}">
                        
                        <div class="title d-flex justify-content-between" >
                            <p>{{ $filter['title'] }}</p>
                            {!! $dropDownIcon !!}
                        </div>
            
                        <ul class="items pt-2 {{$collapsed}}">
                            @foreach ($filter['values'] as $value)
                                <li class="d-flex d-horizontal py-1">
                                    <input type="checkbox">
                                    <p class="m-0 ms-2 d-flex align-items-center">{{ $value['title'] }}</p>
                                    <span class=" quantity border rounded-pill ms-auto px-1 d-center">{{$value['quantity']}}</span>
                                </li>
                            @endforeach
                        </ul>
                    </li>
                    @endforeach
                </ul>
            </nav>

            <div class="border-top d-none d-lg-block">
                <a href="/" class="btn btn-link p-0 pt-3"> Wis alle filters</a>
            </div>

            <div class="filters-footer">

                <div class="d-flex justify-content-end">
                    <a href="/" class="btn btn-link filters-reset"> Wis alle filters</a>
                </div>
                
                <button class="btn btn-primary filters-submit">15 resultaten tonen </button>
            </div>
        </div>
        
        <div class="w-100 filters-mobile-toggle d-lg-none  d-flex flex-column p-3">
            <button class="btn btn-primary ">Filter</button>
        </div>
    </aside>

    <div class="page-content col-12 col-lg-9">

        <div class="pb-3">
            Van Straaten offers a wide range of various products that can be custom-made with a print of your choice.
            Click on one of the products below and completely customize the material according to your preferences.
        </div>

        <div class="row justify-content-end sort mb-0 mb-md-4 sort">

            <div class="col-auto d-center">
                <label for="sort">Sort by</label>
            </div>

            <div class="col-3">
                <select class="col form-select result-sorting font-md" id="result-sorting">
                    <option value="sort asc" {{implode(' ' , $sort) == 'sort asc' ? 'selected' : ''}}>Relevance High to low</option>
                    <option value="systemname asc" {{implode(' ' , $sort) == 'systemname asc' ? 'selected' : ''}}>Name A to Z</option>
                    <option value="systemname desc" {{implode(' ' , $sort) == 'systemname desc' ? 'selected' : ''}}>Name Z to A</option>
                </select>
            </div>
        </div>

        <div class="row-cols-2344445">

            @foreach ($data as $item) 
             
                <div class="col">
                    @include($app.'.templates.product' , [
                        'item' => $item
                    ])
                </div>

            @endforeach
        </div>
    </div>
</div>

@endsection