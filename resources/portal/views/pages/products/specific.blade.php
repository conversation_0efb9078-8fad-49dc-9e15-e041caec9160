@extends($app.'.layouts.app')
@section('content')

<div class="row">

    <div class="page-top col-12">
        <x-portal.breadcrumb page="Product specifiek"/>
    </div>

    <div class="col-12">


        <div class="w-100 wrapper">

            <div class="wrapper-left">
                <div class="wrapper-left-top">
                    <div class="arrow-left">
                        <i class="fas fa-chevron-left"></i>
                    </div>
    
                    <div class="arrow-right">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                    
                    <a href="{{URL::asset('/img/silicon-edge.jpg')}}" class="image-link" data-fancybox="gallery">
                        <div class="ratio ratio-3x2 ratio-cover image-container mt-2">
                            <img class="main-image rounded border" src="{{URL::asset('/img/silicon-edge.jpg')}}" />
                        </div>
                    </a>

                </div>

                <div class="mt-3 d-flex thumb-images owl-carousel owl-theme ">
                    {{-- @if ($product['thumb_images'])
                    
                        @foreach ($product['thumb_images'] as $thumbImage)

                            <div class="link {{ $loop->first ? 'selected' : '' }}" data-image-url="{{$thumbImage}}">
                                <div class="ratio ratio-3x2 mt-2 image-container ">
                                    <img class="rounded " src="{{$thumbImage}}" />
                                </div>
                            </div>

                        @endforeach
                        
                    @endif --}}
                    <div class="link selected" data-image-url="{{URL::asset('/img/silicon-edge.jpg')}}">
                        <div class="ratio ratio-3x2 mt-2 image-container ">
                            <img class="rounded " src="{{URL::asset('/img/silicon-edge.jpg')}}" />
                        </div>
                    </div>
                    <div class="link" data-image-url="{{URL::asset('/img/silicon-edge.jpg')}}">
                        <div class="ratio ratio-3x2 mt-2 image-container ">
                            <img class="rounded " src="{{URL::asset('/img/silicon-edge.jpg')}}" />
                        </div>
                    </div>
                    <div class="link" data-image-url="{{URL::asset('/img/silicon-edge.jpg')}}">
                        <div class="ratio ratio-3x2 mt-2 image-container ">
                            <img class="rounded " src="{{URL::asset('/img/silicon-edge.jpg')}}" />
                        </div>
                    </div>
                    <div class="link" data-image-url="{{URL::asset('/img/silicon-edge.jpg')}}">
                        <div class="ratio ratio-3x2 mt-2 image-container ">
                            <img class="rounded " src="{{URL::asset('/img/silicon-edge.jpg')}}" />
                        </div>
                    </div>
                    <div class="link" data-image-url="{{URL::asset('/img/silicon-edge.jpg')}}">
                        <div class="ratio ratio-3x2 mt-2 image-container ">
                            <img class="rounded " src="{{URL::asset('/img/silicon-edge.jpg')}}" />
                        </div>
                    </div>

                </div>
            </div>

            <div class="wrapper-right-top"> 
                <div class="page-title mb-3">
                    <h1>{{$product->{'title_'.App::getLocale()} }}</h1>
                </div>
            </div>


            <div class="wrapper-right-bottom">

                <div class="content-editor my-3">
                    {{$product->{'content_intro_'.App::getLocale()} }}
                </div>
                
                @if ($usps)
                    @foreach ($usps as $usp)
                        <div class="d-flex usp">
                            <div class="icon"><i class="fas fa-check"></i></div>
                            <div class="py-1">{{$usp->{'title_'.App::getLocale()} }}</div>
                        </div>
                    @endforeach
                @endif
                
                <div class="col-12 my-4 my-lg-4 border-bottom"></div>

                <div class="add-to-cart row d-flex justify-content-between">
                    <div class="col-12">
                        <div class="btn btn-tertiary w-100">
                            Product samenstellen
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="col-12 d-none d-lg-block  my-4">
        <div class="border-top"></div>
    </div>
    
    <div class="col-12 py-2">
        
        <h2>Zelf samenstellen</h2>

        <div class="content-editor">
            <div class=" block-producttree producttree" data-id=""> 
                <div class="question-wrapper accordion d-flex flex-column">
                    <div class="question mb-2 template-dimensions type-dimensions" data-template="dimensions" data-name="dimensions" data-type="dimensions" data-uniqid="jshyjlkdi5" data-id="1"> 
                        <div class="card"> 
                            <div class="card-header"> 
                                <a data-toggle="collapse" href="#collapse-jshyjlkdi5" aria-expanded="false" aria-controls="collapse-jshyjlkdi5" id="heading-jshyjlkdi5" class="d-flex align-items-center justify-content-between question-header collapsed"> 
                                    <div class="accordion-head d-inline-block"> 
                                        <span class="question-title">Formaat en aantal</span> 
                                        <span class="question-suffix ml-1 accordion-head text-secondary"></span> 
                                    </div> <img class="pencil" src="/assets/dist/img/edit-light-grey.svg" alt="Edit"> 
                                </a> 
                            </div> 
                            <div id="collapse-jshyjlkdi5" class="question-content collapse" style=""> 
                                <div class="card-body question-content-container"> 
                                    <div> 
                                        <p class="accordion-text content">Geef hier uw gewenste formaat en aantal in</p> 
                                        <div class="card-default card-dimensions-quantity"> 
                                            <div class="row"> 
                                                <div class="col-12">  
                                                </div> 
                                                <div class="col-12 alert-validation collapse"> 
                                                    <div class="alert alert-danger d-md-inline-flex rounded py-2 mb-4"> 
                                                        <div class="alert-text p-1">
                                                            <span class="text"></span>
                                                        </div> 
                                                    </div> 
                                                </div> 
                                            </div> 
                                            <div class="row px-2 mb-n3 input-container d-flex align-items-center"> 
                                                <div class="form-group col px-2 order-12 col d-flex justify-content-end align-items-end"> 
                                                    <button class="btn btn-primary btn-sm-block producttree-next">Ga verder</button> 
                                                </div> 
                                                <div class="inputcolum-container inputcolumn-width form-group col-12 col-md-3 col-md-2 px-2"> 
                                                    <label for="inputcolumn-8xxbgh1u0i">Breedte</label> 
                                                    <div class="input-group"> 
                                                        <input type="text" name="width" class="form-control inputcolumn width-field price-normalize-dimensions" data-field="dimension" id="inputcolumn-8xxbgh1u0i">
                                                         <div class="input-group-append suffix"> 
                                                            <span class="input-group-text">cm</span> 
                                                        </div> 
                                                    </div> 
                                                </div>
                                                <div class="inputcolum-container inputcolumn-height form-group col-12 col-md-3 col-md-2 px-2"> 
                                                    <label for="inputcolumn-xq1xhm47peg">Hoogte</label> 
                                                    <div class="input-group"> 
                                                        <input type="text" name="height" class="form-control inputcolumn height-field price-normalize-dimensions" data-field="dimension" id="inputcolumn-xq1xhm47peg"> 
                                                        <div class="input-group-append suffix"> 
                                                            <span class="input-group-text">cm</span> 
                                                        </div> 
                                                    </div> 
                                                </div>
                                                <div class="inputcolum-container inputcolumn-amount form-group col-12 col-md-3 col-md-2 px-2"> 
                                                    <label for="inputcolumn-hs9w8scox78">Aantal</label> 
                                                    <div class="input-group"> 
                                                        <input type="text" name="amount" class="form-control inputcolumn quantity-field numbers-only number-normalize" data-field="amount" id="inputcolumn-hs9w8scox78">  
                                                    </div> 
                                                </div>
                                            </div> 
                                        </div> 
                                    </div>
                                </div> 
                            </div> 
                        </div> 
                    </div>
                </div> 
            </div>
        </div>

    </div>

    <div class="col-12 d-none d-lg-block  my-4">
        <div class="border-top"></div>
    </div>
    

    <div class="col-12 py-2">
        
        <h2>Omschrijving</h2>

        <div class="content-editor"> 
            {{$product->{'content_'.App::getLocale()} }}
        </div>

    </div>


    <div class="col-12 d-lg-block  my-4">
        <div class="border-top"></div>
    </div>

    <div class="col-12 ">

        <h2>Specificaties</h2>
        <div class="specifications rounded mt-4">

            @php 
                $count = 0; 
            @endphp
        
            <div class="row">

                @foreach ($properties as $property)
                
                    @php
                        $propertyValue = $propertiesValues->where('property_id', $property->id)->first();
                    @endphp
                
                    @if ($count % 2 == 0)
                        @if ($count > 0)
                            </div>
                            <div class="row">
                        @endif
                    @endif
                
                    <div class="col-12 col-md-6 specs-col">
                        <div class="row align-items-center">
                            <div class="col-6 title">{{ $property->{'name_'.App::getLocale()} }}</div>
                            <div class="col-6 text">{{ $propertyValue->{'name_'.App::getLocale()} }}</div>
                        </div>
                    </div>
                
                    @php
                        $count++;
                    @endphp

                @endforeach
            
            
            </div>
    </div>

</div>

@endsection