$(function() {
    
    $('.owl-carousel').owlCarousel({
        margin: 10,
        nav: true,
        lazyLoad:false,
        responsive:{
            0:{
                items:4,
            },
            768:{
                items:6
            },
            992:{
                items:5
            },
        }
    });

    $(".owl-carousel .link").on('click', function() {
        $(".link").removeClass('selected');
        $(this).addClass('selected');
        $(".main-image").attr("src", $(this).data('image-url'));
        $(".image-link").attr("href", $(this).data('image-url'));
    });

    $(".arrow-left").on("click", function() {

        let cur = $(".wrapper-left .link.selected").parent(),
        prev = cur.prev();
        
        $('.wrapper-left .link.selected').removeClass('selected');

        if (prev.length == 0) {
            prev = $(".wrapper-left .link").first().parent();
        }

        prev.find('.link').addClass('selected');

        $(".wrapper-left .main-image").attr("src", $('.wrapper-left .link.selected').data("image-url"));
        $(".wrapper-left .image-link").attr("href", $('.wrapper-left .link.selected').data("image-url"));
    });

    $(document).on('click', '.filter .title', function() {
        setFilter($(this).closest('.filter').data('id')); 
    })

    $(".arrow-right").on("click", function() {

        let cur = $(".wrapper-left .link.selected").parent(),
            next = cur.next();
        
        $('.wrapper-left .link.selected').removeClass('selected');

        if (next.length == 0) {
            next = $(".wrapper-left .link").first().parent();
        }

        next.find('.link').addClass('selected');

        $(".wrapper-left .main-image").attr("src", $('.wrapper-left .link.selected').data("image-url"));
        $(".wrapper-left .image-link").attr("href", $('.wrapper-left .link.selected').data("image-url"));
    
    });
});

let addressDelivery = null,
    addressInvoice = null,
    deliveryType = null,
    deliveryTime = null,
    deliveryDate = null,
    deliveryId = null,
    deliveryPrice = null,
    isLoading = false,
    t, checkoutXhr;

function setSetting(settingName, settingValue)
{
    //doe xhr voor wegschrijven setting

}

function setPropertyValue(type, id, state = null) 
{
    $(this).closest('.property').toggleClass('property-open');

    //xhr wegschrijven setting
}   

function setFilter(propertyId) 
{   
    let element = $('.filter[data-id='+propertyId+']');

    if (element.hasClass('filter-static')) {
        return;
    }
    
    element.toggleClass('filter-open');

    if (!element.hasClass('filter-open')) {
        element.find('.items').slideUp();
    } else {
        element.find('.items').slideDown();
    }

}   