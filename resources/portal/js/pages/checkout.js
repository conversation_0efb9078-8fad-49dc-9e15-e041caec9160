
$(document).on('click', '.delivery-time', function() {

    if (!$(this).hasClass('selected')) {
        $('.delivery-times .delivery-time').removeClass('selected');
        $(this).addClass('selected');
    } else {
        $('.delivery-times .delivery-time').removeClass('selected');
    }
    
});

    
$(document).on('click', '.delivery-type', function() {
       
    if (!$(this).hasClass('selected')) {
        $('.delivery-types .delivery-type').removeClass('selected');
        $(this).addClass('selected');
    } else {
        $('.delivery-types .delivery-type').removeClass('selected');
    }
});

$(document).on('click', '.delivery-date:not(.delivery-date-s)', function(e) {
    
    if (e.target.nodeName == 'INPUT') {
        return;
    }

    let sDate = false;
    
    $('.sds')
        .attr('checked', false)
        .prop('checked', false);
    $('.delivery-date').removeClass('selected');
    $('.delivery-date').removeClass('selected-date');
    
    $(this).addClass('selected');

    if ($(this).data('date') == 'datepicker') {
        console.log('hoi')
        let dates = [];

        $(this).data('dates').forEach(function(d) {
            dates.push(d.date);
     
            if (deliveryDate != null &&
                deliveryDate == d.date) {
                sDate = d;
            }
        });

        let dateDefault = dates[0].split('-');

        let args = {
            showWeek: true,
            firstDay: 1,
            changeMonth: false,
            changeYear: false,
            hideIfNoPrevNext: true,
            dateFormat: "yy-mm-dd",
            numberOfMonths: [ $(this).data('months'), 1 ],
        };

        args.defaultDate = new Date(dateDefault[0], parseInt(dateDefault[1]) - 1, parseInt(dateDefault[2]));
        if (sDate !== false) {
            let dd = sDate.date.split('-');
            args.setDate = new Date(dd[0], parseInt(dd[1]) - 1, parseInt(dd[2]));
            args.defaultDate = new Date(dd[0], parseInt(dd[1]) - 1, parseInt(dd[2]));
        }

        $(this).find('.datepicker-container .datepicker').datepicker(args);
        $(this).find('.datepicker-container').slideDown();

    } else {
        
        $('.delivery-dates .delivery-date-custom .datepicker-container').slideUp();
        $('.delivery-dates .delivery-date-custom').removeClass('selected-date');
        $('.delivery-dates .delivery-date-custom').find('.no-date').show();
        $('.delivery-dates .delivery-date-custom').find('.date-picked').hide();
        $('.delivery-dates .delivery-date-custom').find('.price p').html('');
    }
});



$('.btn-edit').on('click', function() {

    $(this).closest('.card-panel').find('.checkout-data').slideUp();
    $(this).closest('.card-panel').find('.checkout-data-edit').slideDown();

    let self = $(this);
    setTimeout(function() {
        self.closest('.card-panel').find('.btn-edit').hide();

        if (self.find('.checkout-data').data('edited') == '1') {
            self.closest('.card-panel').find('.btn-reset').show();
        }
    }, 250);
});

$('.btn-cancel').on('click', function() {

    let panel = $(this).closest('.card-panel');

    panel.find('.checkout-data').slideDown();
    panel.find('.checkout-data-edit').slideUp();

    setTimeout(function() {

        if (panel.find('.checkout-data').data('edited') == '1') {
            panel.find('.btn-reset').show();
        }

        panel.find('.btn-edit').show();
    }, 250);
});