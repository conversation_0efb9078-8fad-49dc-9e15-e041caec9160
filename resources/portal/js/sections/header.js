
$(function() {

    // $('.selectpicker').selectpicker();

    function redirect(value) {
        window.location = 'zoeken' +'?term=' + value;
    }

    $('.search-input input').on('keyup click', function (e) {
        e.stopPropagation();

        let value = $(this).val();
        if (e.which == 13) {
            redirect(value)
        } else {
            
            let noWhitespaceValue = value.replace(/\s+/g, '');
            let noWhitespaceCount = noWhitespaceValue.length;
            if (noWhitespaceCount >= 3) {
                
                if($('.search-gradient').length == 0) {
                    $('body').append('<div class="overlay-gradient search-gradient"></div>');
                }
                $('.search-content').removeClass('d-none');
                $('.btn-show-results').attr('href', '/zoeken' + '?term=' + $('.search-input input').val());
            } else {
                $('.search-gradient').remove();
                $(".search-loader").hide();
                $('.search-content').addClass('d-none');
                return;
            }
        }
    });

    $('.search-button').on('click', function () {
        if ($('.search input').val().length != 0) {
            redirect($('.search input').val())
        }
    });

    let searchbarTimer;

    $('.search input, .search-content').on('mouseenter', function() {
        if (typeof searchbarTimer != 'undefined') {
            clearTimeout(searchbarTimer);
        }
    });
    
    $('.search search-input, .search-content').on('mouseleave', function (e) {
    
        let self = $(this);
    
        searchbarTimer = setTimeout(function() {
            $('.search-content').addClass('d-none');

            $('.search-gradient').remove();
        }, 1500);
    });

    $(document).on('click', '.search-gradient', function () {
        $('.search-content').addClass('d-none');
        clearTimeout(searchbarTimer);
    });

    $('.search-bar input').on('click', function(){
        $('.search-bar').addClass('search-hover');

        if ($(".search-gradient").length == 0) {
            $('body').append('<div class="overlay-gradient search-gradient"></div>');
        }
    })

    $(document).on('click', '.search-gradient', function () {
        $('.search-content').addClass('d-none');
        if ($(window).width() >= 767.9) {
            $('.search-bar').removeClass('search-hover');
        }
        $('.search-gradient').remove();
        clearTimeout(searchbarTimer);
    });

    $(".toggle-password").on('click', function() {

        let passwordField = $("#password");
        let icon = $(this).find("i");

        if (passwordField.attr("type") === "password") {
            passwordField.attr("type", "text");
            icon
                .removeClass("fa-eye-slash")
                .addClass("fa-eye");
        } else {
            passwordField.attr("type", "password");
            icon
                .removeClass("fa-eye")
                .addClass("fa-eye-slash");
        }
    });

    $(".open-mobile-menu").on('click', function() {
        $(document.body).addClass('menu-open');
    });

    $(".menu-mobile .top > .header .close").on('click', function() {
        $(document.body).removeClass('menu-open');
    });
    
    $(".filters-mobile-toggle").on('click', function() {
        $(document.body).addClass('filter-open');
    });

    $(".filters-overlay .filters-header .close").on('click', function() {
        $(document.body).removeClass('filter-open');
    });

    $(".menu-mobile .top > .header .close").on('click', function() {
        $(document.body).removeClass('menu-open');
    });

    // $(".menu-mobile .body .close").on('click', function() {
    //     $('.header-item-childs').removeClass('active');
    //     $(document.body).removeClass('menu-open');
    // });

    $(".menu-mobile .body .icon-box").on('click', function() {
        $(this).closest('.header-item-childs').removeClass('active');
    });

    $(".menu-mobile .body .close").on('click', function() {
        $('.header-item-childs').removeClass('active');
        $(document.body).removeClass('menu-open');
    });

    $('.menu-items .header-item-childs span').on('click', function() {
        $(this).closest('.header-item-childs').toggleClass('active');
    });

});