import '../sass/app.scss'; 
import 'bootstrap-select/dist/css/bootstrap-select.min.css';

import '@fortawesome/fontawesome-free';
//import '@fortawesome/fontawesome-free/css/all.min.css';

import jQuery from 'jquery';

window.$ = window.jQuery = jQuery;

import * as bootstrap from 'bootstrap';
window.bootstrap = bootstrap;


// import 'owl.carousel/dist/assets/owl.carousel.min.css';
import '@fancyapps/fancybox/dist/jquery.fancybox.min.css';
import './sections/header.js';




$(function() {

    // $('.selectpicker').selectpicker();

});


let ordersXhr;
let ordersTimer = null;

function refreshDashboard(urlSegments) {

    if (ordersXhr && ordersXhr.readyState != 4) { 
        ordersXhr.abort();
    }

    setLoading('.overview');
    console.log(urlSegments)
    ordersXhr = $.get(getUriDashboard()+urlSegments.join('')+"&pageonly=1", function (data) {
        let html = $('<div>').append(data);
        $('.data-container').replaceWith(html.find('.data-container'));

        setLoading('.data-container', 'inline', 'remove');

        $("html, body").animate({ scrollTop: 0 }, "slow");

    });
}

function getUriDashboard() {
    var uri = window.location.href,
        char = '?';

    if (uri.indexOf('?') != -1) {
        char = '&';
    }

    return window.location.href + "/" + char;
}


$(function() {

    $(document).on('change', '#result-limiter', function () {

        var urlSegments = [];
        urlSegments.push('&limiter=' + parseInt($(this).val()));
        if ($('.form-icon-container input').length > 0) {
            urlSegments.push('&search=' + encodeURI($('.form-icon-container input').val()));
        }
        refreshDashboard(urlSegments);
    });

    $(document).on('click', '.pagination li', function (e) {

        var urlSegments = [];
        urlSegments.push("page=" + $(this).data('page'));
        if ($('.form-icon-container input').length > 0) {
            urlSegments.push('&search=' + encodeURI($('.form-icon-container input').val()));
        }
        refreshDashboard(urlSegments);
    });

    
    $(document).on('change', '#result-sorting', function () {

        var urlSegments = [];
        urlSegments.push('&sort=' + $(this).val().replace(' ', '.'));
        if ($('.form-icon-container input').length > 0) {
            urlSegments.push('&search=' + encodeURI($('.form-icon-container input').val()));
        }
        refreshDashboard(urlSegments);
    });

});


function setLoading(selector, state = 'add', type = 'inline', text = false) {

    if (typeof isLoading != 'undefined') {
        if (isLoading) {
            return;
        }
    }

    type = type == 'inline'
        ? ' loading-inline'
        : '';

    text = text === false 
        ? 'Loading...'
        : text;

    $(selector).find('.loading').remove();

    if (state == 'add') {
        $(selector).append( `
            <div class="loading`+type+`">
                <div class="w-100 text-center">
                    <i class="fa fa-cog fa-spin fa-3x fa-fw"></i>
                </div>
                <div class="w-100 text-center">
                    <span>`+text+`</span>
                </div>
            </div>`);
    }
}
