@use 'sass:math';


*[data-bs-toggle=collapse][aria-expanded=true] .icon-collapse {
    display: none;
}
*[data-bs-toggle=collapse][aria-expanded=false] .icon-collapsed {
    display: none;
}



h1 .badge {
    position: absolute;
    top: -10px;
    right: -10px;
}


// .form-control {

//     &.form-control-as-text {
//         border: none !important;
//         box-shadow: none !important;
//         padding: 4px;
//     }
// }

// .input-group {
    
//     &.input-group-as-text:not(:hover) {
//         border: none !important;
//         box-shadow: none !important;
//         padding: 4px;
//         background-color: transparent;

//         .input-group-text {
//             border: none !important;
//             box-shadow: none !important;
//             background-color: transparent !important;
//         }

//         input.form-control {
//             border: none !important;
//             box-shadow: none !important;
//             padding: 4px;
//         }
//     }
// }







.module-document-uploader {


    .uploader-container {

        .file-uploader {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: yellow;
        }

    }

    .uploader {

        display: flex;
        width: 100%;
        height: 150px;
        margin-top: 20px;
        margin-bottom: 10px;

        .dropzone {

            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            background: #e7e7e7;
            border: 3px dotted #7c7d7e;
            cursor: pointer;
            
            &.dragover {
                border: 3px dotted #e87f00;
            }

            i {
                font-size: 50px;
                color: #7c7d7e; 
            }
            
            label {
                margin-top: 10px;
                font-size: 15px;
            }
        }

        .upload-progress {

            display: none;

        }

        input {
            display: none;
        }


    }
}



.progress-box {

    position: relative;
    width: 110px;
    height: 110px;
    display: flex;
    align-items: center;
    justify-items: center;

    &.progress-box-sm {
        width: 70px;
        height:70px;
    }

    .percentage {
        position: absolute;
        font-size: 22px;
        top: 21px;
        font-weight: bold;
        width: 100%;
        text-align: center;
    }

    .status-text {
        position: absolute;
        font-size: 15px;
        top: calc(50% - 15px);
        font-weight: bold;
        width: 100%;
        text-align: center;
    }

    .speed {
        position: absolute;
        font-size: 11px;
        top: 56px;
        font-weight: 600;
        width: 100%;
        text-align: center;
    }

    &.state-uploading {

        .status-text {
            display: none;
        }

        .ring-circle {
            transform: rotate(270deg);
            transform-origin: center;
        }
    }

    &.state-processing {
        
        .percentage, 
        .speed {
            display: none;
        }

        .ring-circle {
            stroke-dasharray: 40, 360 !important;
            stroke-dashoffset: 220 !important;
            transform-origin: center;
            transform: rotate(0deg);
            animation: rotation 2s infinite cubic-bezier(0.8, 0.1, 0.34, 0.87);
        }
    }

    
    
}





.row-wrapper {

    //min-height: 30px;

    .row {

        display: flex;
        align-items: center;

        label {
   
        }
    }
}



.flag {
    height: 16px;
    width: auto;
}






.container.login {
    max-width: 700px;
    margin-top: 350px;

    .logo {
        position: absolute;
        top: -250px;
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
    }
}

.ratio {

    &.ratio-cover {
        img {
            object-fit: cover;
        }
	}

	&.ratio-contain {
        img {
            object-fit: contain;
        }
	}

}
.ui-datepicker {  background: white; }

.input-group-text {
    height: 100%
}

.icon-box {

    i {
        height: 1rem;
        width: 1rem;
    }
}

.gender {
    
    font-size: 16px;

    .male { 
        color: #347d9f;
    }
    
    .female { 
        color: #dc47e2;
    }

}

.sector-switch {
    margin-top: 7px;
}

.login-title {
    font-size: 24px;
}

.order-icons {
    
    .order-icon {
        font-size: 30px;
        margin-left: 8px;
    }

    .flag-icon {
        width : 40px;
        margin-left: 8px;
        vertical-align: text-bottom !important;
    }
}

.tag-search-results {

    @extend 
        .border,
        .bg-light;

    z-index:998;
    position: absolute;
    width: auto;
    max-width: 600px;
    padding: 10px;
    min-width: 200px;
    max-height: 300px;
    overflow-y: auto;

    ul {
        margin-bottom: 0px !important;
    }

    ul li {
        @extend .border-bottom;
        cursor: pointer;
    }

    ul li:hover {
        font-weight: bold;
    }   
    
    ul li:last-child {
        border-bottom:none !important;
    }
}

.tags-container,
.keywords-container {

    @extend 
        .pt-2;

    .tag {
        @extend 
            .me-2,
            .my-1,
            .col-auto,
            .border,
            .rounded-pill;

        cursor: pointer;
        font-family: Poppins;

        &:hover {
            background-color: #f8f8f8;

            > div > div:last-child {
                color: #ff8d8d;
            }
        }        

        > div > div:first-child {
            @extend 
                .col-auto;


        }

        > div > div:last-child {
            @extend 
                .px-2,
                .rounded-pill,
                .bg-dark,
                .col-auto;
            
            color: white;
        }
    }


}

.text-color {
    color: #777 !important;
}

.font-sm {
	font-size: 0.5rem;
}

.ratioa {

	position: relative;
    width: 200px;

	
	&.ratioa-2x1 {
		
		&:before {
			display: block;
			content: "";
			padding-top: 50%;
		}
	}

	.inner {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
	}
} 


.dot{
	
	border-radius: 4px;
    top: 2px;
    position: relative;
    height:15px;
    width:15px;

    &.big {
        width: 20px;
        height: 20px;
        top: 4px;
    }
}

.green {
    background:green; 
}

.red {
    background:red;
}

.yellow {
    background: #FFB300;
}

.modal {
    .modal-head,
    .modal-title {
        font-size: 16px;
        line-height: 25px;
        color: #444444;
    }

    .close {
		float: right;
		font-size: 1.5rem;
		font-weight: 700;
		line-height: 1;
		color: #000;
		text-shadow: 0 1px 0 #fff;
		opacity: .5;
		padding: .5rem 1rem ;
		margin: -1rem -1rem 0px auto;
		background-color: transparent;
		border: 0;
    }
}



.link-objects{
    
    .link-object {
        margin-bottom: 0 !important;
    }

    .template {
        @extend .d-none;
    }

}

.disabled {
    color: rgb(211, 110, 110) !important;
    background-color: #f9f9f9;
}


.sorting-handle {
    cursor: grab;
}

.filter-option {
    font-size: 0.8rem;
}

.btn-icon {
    padding: 0.4rem !important;
    
    div {
        height: 14px  !important;
        width: 14px  !important;
        display: flex  !important;
    
        i {
            height: 14px;
            width: 14px;
            font-size: 14px;
        }
    }
}

.row-substrate, .row-accessory {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    
    &:hover {
        background-color: #f9f9f9;
    }
}

$widths: 5,10,15,20, 30,35,40,45, 55,60,65,70, 80,85,90,95;

@each $width in $widths {
    .w-#{$width} {
        width: #{$width}#{"%"} !important;
    }
}

.input-money {
    text-align: right !important;
}


.row {
    font-size: 0.8333333333rem;
}


.card {

    .controls {

        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        flex-wrap: nowrap;
        padding-top: 0px;
        padding-bottom: 0px;
        margin-top: -0.25rem !important;
        margin-bottom: -0.25rem !important;        

        .btn {
            display: flex;
            width: 24px;
            height: 24px;

            padding:0px !important;
            margin:0px !important;

            justify-content: center;
            align-items: center;

            &:not(:first-child) {
                margin-left: 5px !important;
            }

            i {
                padding:0px !important;
                margin:0px !important;

                padding-inline-end: 0px !important;
                padding-inline-start: 0px !important;
                padding-left: 0px !important;
                padding-right: 0px !important;
                line-height: initial !important;
            }
        }
    }
}

.btn {

    &.btn-control {
        cursor: pointer;
    }

    &.btn-control-disabled {
        opacity: 0.2;
        cursor: not-allowed;
        pointer-events: none;
    }
}