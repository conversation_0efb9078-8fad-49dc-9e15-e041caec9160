.card {

    margin-bottom: 1.5rem;

    .card-header {

        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        position: relative;

        background-color: var(--falcon-tertiary-bg);
        border-bottom: var(--falcon-border-width) var(--falcon-border-style) var(--falcon-border-color) !important;
        height: 50px;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        padding-left: 1rem !important;
        padding-right: 1rem !important;

        .card-title {
            font-size: 1.3rem;
            margin-bottom: 0px;
            margin-top: 0px;

            .nav-tabs {
                margin-bottom: -11px;
                border-bottom: 1px solid var(--falcon-border-color);
            }
        }

        .card-controls {
            position: relative;
        }

        .card-locales {

            margin-bottom: -12px;

            .nav-tabs {
                border-bottom: 1px solid var(--falcon-border-color);
            }
        }
    }

    .card-body {
        
        .overview-header {
            font-weight: bold;

            .controls {
                text-align: right;
            }
        }
    }
}


.card-overview {

    .card-body {
        
        font-size: 13px;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        padding-left: 1rem !important;
        padding-right: 1rem !important;

        .row-item {
            padding-bottom: 0.5rem;
            padding-top: 0.5rem;

            &.row-item-disabled {
                background-color: #ffececd4 !important;
            }
        }
        
        .overview-body {

            .row-item {
                height: auto !important;
                border-top: 1px solid #ddd;
                align-items: center;
                
                &:hover {
                    background-color: var(--falcon-tertiary-bg);
                }
                    
                .row-item-childs {
                    margin-top: 0.5rem !important;
                    margin-bottom: -0.5rem !important;
                    padding-bottom: 0px !important;
                    padding-top: 0px !important;
                    
                    .row-item {
                        border-bottom: 0px !important;
                        border-left: 0px !important;
                        border-right: 0px !important;
                    }
                }
            
                .controls {

                    text-align: right;

                    .btn-control {
                        width: 22px;
                        height: 22px;
                 
                        &:last-child {
                            //margin-right: -5px !important;
                        }
                        &:first-child {
                            margin-left: 0px !important;
                        }
                    }
                }

            }
        }
    }
}



.sortable {

    position: relative;
    cursor: pointer;
    text-decoration: none !important;
    padding-right: 12px;


    &::after,
    &::before {
        font-family: 'Font Awesome 6 Duotone'; //'Font Awesome 6 Free';
        font-weight: 900;
        font-size: 0.95em;
        display: inline-block;
        position: absolute;
        right: 0px;
        color: #bcd4f4;
        content: '\f0dc';
    }

    &.sortable-asc {
        &::after {
            color: #2c7be5 !important;
            content: '\f0de' !important; /* Ascending icon (fa-sort-down) */
        }
        &::before {
            content: '\f0dd' !important; /* Ascending icon (fa-sort-up) */
        }
    }

    &.sortable-desc {
        &::after {
            color: #2c7be5 !important;
            content: '\f0dd' !important; /* Ascending icon (fa-sort-up) */
        }
        &::before {
            content: '\f0de' !important; /* Ascending icon (fa-sort-down) */
        }
    }

    &:hover {
        &::before {
            color: #de9c22 !important;
        }
    }
}





.row-item {

    &.is-finished {
        background: linear-gradient(91deg, rgb(61 231 49 / 7%) 0%, rgb(43 185 0 / 16%) 65%, rgb(181 255 176 / 15%) 100%) !important;
    }

    &.is-tolate {
        background: linear-gradient(90deg, rgb(231 49 74 / 7%) 0%, rgb(201 15 15 / 16%) 65%, rgb(255 176 187 / 15%) 100%) !important;
    }

    &.is-onhold {
        background: linear-gradient(90deg, rgb(207 207 207 / 7%) 0%, rgb(139 139 139 / 16%) 65%, rgb(221 221 221 / 15%) 100%) !important;
    }

    &.is-reproduction {
        background: linear-gradient(90deg, rgb(110 187 255 / 7%) 0%, rgb(0 123 185 / 16%) 65%, rgb(53 147 185 / 15%) 100%) !important;
    }

    &.is-reproduction.is-finished {
        background: linear-gradient(91deg, rgb(61 231 49 / 18%) 0%, rgb(43 185 0 / 38%) 65%, rgb(181 255 176 / 48%) 100%) !important;
    }

    .order-icon {
        font-size: 0.75em;
        margin-left:5px;
    }

}