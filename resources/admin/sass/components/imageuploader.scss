.image-uploader {

    .media-container{
        display: flex;
        flex-wrap: wrap;
        gap: 10px; /* Spacing between items */
    }


    &[data-items-per-row="1"] { .image-block { width: calc(100% - 10px); }}
    &[data-items-per-row="2"] { .image-block { width: calc(50% - 10px); }}
    &[data-items-per-row="3"] { .image-block { width: calc(33.3333% - 10px); }}
    &[data-items-per-row="4"] { .image-block { width: calc(25% - 10px); }}
    &[data-items-per-row="5"] { .image-block { width: calc(20% - 10px); }}
    &[data-items-per-row="6"] { .image-block { width: calc(16.6667% - 10px); }}
    &[data-items-per-row="7"] { .image-block { width: calc(14.2857% - 10px); }}
    &[data-items-per-row="8"] { .image-block { width: calc(12.5% - 10px); }}
    &[data-items-per-row="9"] { .image-block { width: calc(11.1111% - 10px); }}
    &[data-items-per-row="10"] { .image-block { width: calc(10% - 10px); }}

    

    .image-block {
        min-width: 100px; /* Optional: Prevent too small sizes */
        aspect-ratio: 3/2; /* Optional: Maintain square shape */
     
        display: flex;
        align-items: center;
        justify-content: center;
       // padding: 10px;

        .image-block-content {
            background: #f9fafd;
            border-radius: 10px;
            padding: 15px;
            position: relative;

            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            text-align: center;            
        }

        &:hover {
            .image-block-content {
                background: #f7f8fd;

                .controls {
                    display: block !important;
                }
            }
        }

        &.image-block-media {

            .image-block-content {
                border: 2px solid #e9eff6;

                .controls {
                    display: none;
                    position: absolute;
                    top: 0;
                    right: 0;
                    padding: 5px;
    
                    .btn-control {
                        width: 24px;
                        height: 24px;
                        color: #9aa6b5;
                        font-size: 18px;
                        display: flex;
                        justify-content: center;
                        align-items: center;

                        &:hover {
                            color: gray;
                        }
                    }
                }
            }
        }

        &.image-block-uploader {
            cursor: pointer;

            .image-block-content {
                border: 3px dotted #9aa6b5;
            }

            &.dragover {
                .image-block-content {
                    border: 3px dotted #d79a00;
                }
            }
        }
    }

    


}