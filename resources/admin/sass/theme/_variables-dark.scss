// Dark color mode variables
//
// Custom variables for the `[data-bs-theme="dark"]` theme. Use this as a starting point for your own custom color modes by creating a new theme-specific file like `_variables-dark.scss` and adding the variables you need.

//
// Global colors
//

// scss-docs-start sass-dark-mode-vars
// scss-docs-start dark-gray-colors-map
$dark-grays: (
  '100': $gray-1100,
  '200': $gray-1000,
  '300': $gray-900,
  '400': $gray-800,
  '500': $gray-700,
  '600': $gray-600,
  '700': $gray-500,
  '800': $gray-400,
  '900': $gray-300,
  '1000': $gray-200,
  '1100': $gray-100,
) !default;
// scss-docs-end dark-gray-colors-map

// scss-docs-start theme-dark-colors-map
$theme-dark-colors: (
  'primary': $primary,
  'secondary': $secondary,
  'success': $success,
  'info': $info,
  'warning': $warning,
  'danger': $danger,
  'light': $light,
  'dark': rgba($gray-1000, 0.25), //TODO
) !default;
// scss-docs-end theme-dark-colors-map

// scss-docs-start table-dark-variats-map
$table-variants-dark: (
  'primary': shift-color($primary, -$table-bg-scale),
  'secondary': shift-color($secondary, -$table-bg-scale),
  'success': shift-color($success, -$table-bg-scale),
  'info': shift-color($info, -$table-bg-scale),
  'warning': shift-color($warning, -$table-bg-scale),
  'danger': shift-color($danger, -$table-bg-scale),
  'light': shift-color($light, -$table-bg-scale),
  'dark': shift-color($dark, -$table-bg-scale),
) !default;
// scss-docs-end table-dark-variats-map

// scss-docs-start theme-bg-subtle-dark-variables
$primary-bg-subtle-dark: shade-color($primary, 65%) !default;
$secondary-bg-subtle-dark: shade-color($secondary, 65%) !default;
$success-bg-subtle-dark: shade-color($success, 70%) !default;
$info-bg-subtle-dark: shade-color($info, 70%) !default;
$warning-bg-subtle-dark: shade-color($warning, 65%) !default;
$danger-bg-subtle-dark: shade-color($danger, 60%) !default;
$light-bg-subtle-dark: $light-bg-subtle !default;
$dark-bg-subtle-dark: $dark-bg-subtle !default;
// scss-docs-end theme-bg-subtle-dark-variables

// scss-docs-start theme-text-dark-variables
$primary-text-emphasis-dark: tint-color($primary, 60%) !default;
$secondary-text-emphasis-dark: tint-color($secondary, 60%) !default;
$success-text-emphasis-dark: tint-color($success, 45%) !default;
$info-text-emphasis-dark: tint-color($info, 50%) !default;
$warning-text-emphasis-dark: tint-color($warning, 55%) !default;
$danger-text-emphasis-dark: tint-color($danger, 55%) !default;
$light-text-emphasis-dark: $light-text-emphasis !default;
$dark-text-emphasis-dark: $dark-text-emphasis !default;
// scss-docs-end theme-text-dark-variables

// scss-docs-start theme-border-subtle-variableses
$primary-border-subtle-dark: shade-color($primary, 50%) !default;
$secondary-border-subtle-dark: shade-color($secondary, 50%) !default;
$success-border-subtle-dark: shade-color($success, 50%) !default;
$info-border-subtle-dark: shade-color($info, 50%) !default;
$warning-border-subtle-dark: shade-color($warning, 50%) !default;
$danger-border-subtle-dark: shade-color($danger, 50%) !default;
$light-border-subtle-dark: $gray-700 !default;
// scss-docs-end theme-border-subtle-variables

$body-bg-dark: $gray-1100 !default;
$body-color-dark: $gray-500 !default;

$body-secondary-color-dark: $gray-300 !default;
$body-secondary-bg-dark: $gray-900 !default;

$body-tertiary-color-dark: $gray-600 !default;
$body-tertiary-bg-dark: tint-color($gray-1100, 4.6%) !default;

$body-emphasis-color-dark: $white !default;
$body-emphasis-bg-dark: $card-bg-dark !default;

$body-quaternary-bg-dark: $dark !default;

$border-color-dark: rgba($white, 0.05) !default;
$border-color-translucent-dark: rgba($black, 0.175) !default;

$link-color-dark: $primary !default;
$link-hover-color-dark: shift-color($link-color-dark, $link-shade-percentage) !default;

$code-color-dark: $pink !default;

$headings-color-dark: var(--#{$prefix}secondary-color) !default;

//
// Navbar Vertical
//
// scss-docs-start navbar-vertical-default-dark-variables
$navbar-vertical-default-link-color-dark: $gray-500 !default;
$navbar-vertical-default-link-hover-color-dark: $gray-200 !default;
$navbar-vertical-default-link-active-color-dark: $primary !default;
$navbar-vertical-default-link-disable-color-dark: $gray-800 !default;
$navbar-vertical-default-hr-color-dark: rgba($white, 0.08) !default;
$navbar-vertical-default-scrollbar-color-dark: rgba($gray-600, 0.3) !default;
// scss-docs-end navbar-vertical-default-dark-variables

// scss-docs-start navbar-vertical-card-dark-variables
$navbar-vertical-card-hr-color-dark: rgba($white, 0.08) !default;
$navbar-vertical-card-bg-color-dark: $card-bg-dark !default;
// scss-docs-end navbar-vertical-card-dark-variables

//
// Accordion
//
$accordion-button-icon-dark: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$gray-700}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>") !default;
$accordion-button-active-icon-dark: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$gray-700}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>") !default;

//
// Forms
//
$form-invalid-border-color-dark: $danger !default;
$form-valid-border-color-dark: $success !default;
$form-switch-color-dark: $gray-500 !default;
$form-select-indicator-color-dark: $gray-800 !default;
$form-select-indicator-dark: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color-dark}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/></svg>") !default;