@import "../../../node_modules/bootstrap/scss/_functions";
@import "theme/functions";


@import 'theme/colors';
@import "theme/variables";
@import "theme/variables-dark";
@import "../../../node_modules/bootstrap/scss/variables";
@import "../../../node_modules/bootstrap/scss/variables-dark";
@import "user-variables";

@import 'theme/maps';
@import "../../../node_modules/bootstrap/scss/maps";

@import "../../../node_modules/bootstrap/scss/mixins";
@import "theme/mixins";

@import 'theme/_utilities';
@import "../../../node_modules/bootstrap/scss/utilities";


$all-colors: map-merge-multiple($blues, $indigos, $purples, $pinks, $reds, $oranges, $yellows, $greens, $teals, $cyans);

$utilities: map-merge(
  $utilities,
  (
    "color": map-merge(
      map-get($utilities, "color"),
      (
        values: map-merge(
          map-get(map-get($utilities, "color"), "values"),
          (
            $all-colors
          ),
        ),
      ),
    ),

    // "background-color": map-merge(
    //   map-get($utilities, "background-color"),
    //   (
    //     values: map-merge(
    //       map-get(map-get($utilities, "background-color"), "values"),
    //       (
    //         $all-colors
    //       ),
    //     ),
    //   ),
    // ),
  )
);

@import "../../../node_modules/bootstrap/scss/root";
@import 'theme/root';


// Layout & components
@import "../../../node_modules/bootstrap/scss/reboot";
@import "../../../node_modules/bootstrap/scss/type";
@import "../../../node_modules/bootstrap/scss/images";
@import "../../../node_modules/bootstrap/scss/containers";
@import "../../../node_modules/bootstrap/scss/grid";
@import "../../../node_modules/bootstrap/scss/tables";
@import "../../../node_modules/bootstrap/scss/forms";
@import "../../../node_modules/bootstrap/scss/buttons";
@import "../../../node_modules/bootstrap/scss/transitions";
@import "../../../node_modules/bootstrap/scss/dropdown";
@import "../../../node_modules/bootstrap/scss/button-group";
@import "../../../node_modules/bootstrap/scss/nav";
@import "../../../node_modules/bootstrap/scss/navbar";
@import "../../../node_modules/bootstrap/scss/card";
@import "../../../node_modules/bootstrap/scss/accordion";
@import "../../../node_modules/bootstrap/scss/breadcrumb";
@import "../../../node_modules/bootstrap/scss/pagination";
@import "../../../node_modules/bootstrap/scss/badge";
@import "../../../node_modules/bootstrap/scss/alert";
@import "../../../node_modules/bootstrap/scss/progress";
@import "../../../node_modules/bootstrap/scss/list-group";
@import "../../../node_modules/bootstrap/scss/close";

@import "../../../node_modules/bootstrap/scss/modal";
@import "../../../node_modules/bootstrap/scss/tooltip";
@import "../../../node_modules/bootstrap/scss/popover";
@import "../../../node_modules/bootstrap/scss/carousel";
//@import "node_modules/bootstrap/scss/spinners";
@import "../../../node_modules/bootstrap/scss/offcanvas";
@import "../../../node_modules/bootstrap/scss/placeholders";
@import "../../../node_modules/bootstrap/scss/helpers";
@import "../../../node_modules/bootstrap/scss/utilities/api";

@import "theme/theme";

@import "sections/left.menu";
@import "sections/content";

@import "components/breadcrumbs";
@import "components/form";
@import "components/nav.languages";
@import "components/cards";
@import "components/badges";
@import "components/page";
@import "components/imageuploader";
@import "components/buttons";
@import "components/filters";

@import "custom";