.content {
    
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding-bottom: 0px;
    
    .content-header {

        display: flex;
        flex-direction: column;

        .content-header-top,
        .content-header-bottom {
            display: flex;
            justify-content: space-between;
            padding-left: 4em;
            padding-right: 4em;
        }

        .content-header-top {
            align-items: center;
            height: 50px !important;
            background-color: #f9fafd;

            .controls {
                display: flex;
                flex-direction: row;

                .btn {
                    margin-left: 0.5rem;
                }
                .dropdown {
                    button {
                        margin-left: 0.5rem;
                    }
                }
                
            }
        }

        .content-header-bottom {

            display: flex;
            align-items: flex-end;
            flex-direction: row;
            height: 60px !important;

            .title-wrapper {
                display: flex;
                align-items: center;
                align-content: center;
                margin-bottom: 0px !important;

                h1 {
                    margin-bottom: 0px !important;
                }

                .title-elements {
                    padding-left: 1rem;

                    .badge {
                        position: relative;
                        top: -5px;
                    }
                }
            }


            .header-flags {

                display: flex;
                height: 100%;
                align-items: center;
            
                .flag {
                    width: 30px;
                    font-size: 26px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    
                    &.flag-is-locked {
                        color: #ebba00;
                        background: -webkit-gradient(linear, left top, left bottom, from(#ffd020), to(#cca308));
                        background-clip: text;
                    }      
                }
            }
            

           
        }
    }

    .content-body {
        height: calc(100vh - 110px);
        overflow-y: auto !important;
        padding-top: 0.5rem !important;
        padding-right: 4rem !important;
        padding-left: 4rem !important;
        padding-bottom: 3em !important;

        .title {
            margin-bottom: 1.3rem !important;
            margin-top: 0.7rem !important;
        }

    }
}