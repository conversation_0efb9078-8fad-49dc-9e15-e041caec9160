.left-menu {

    background-color: #f9fafd;
    
    .top-left {
        
        display: inline;

    }

    .nav-link {

        &.nav-link-has-childs {
            padding-bottom: 0.2rem !important;
        }
    }

    .nav-link-child {
        font-size: small;
        padding: 0.1rem 1rem !important;

        &:last-child {
            padding-bottom: 0.25rem !important;
        }
    }

    .nav-link-icon {
        width: 15px !important;
        height: 15px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
    }

    .active {
        color: #e0417f !important;
    }

    .menu {
        overflow-y: auto;
        overflow-x: hidden;
    }
}



@include media-breakpoint-up(sm) {

    .left-menu {
        width: 150px !important;
        max-width: 150px !important;
    }

    .content {
        margin-left: 150px !important;
    }
}

@include media-breakpoint-up(md) {

    .left-menu {
        width: 208px !important;
        max-width: 208px !important;
    }

    .content {
        margin-left: 208px !important;
    }
}