$aspect-ratios: (
    "1x1": 100%,
    
    "2x1": 50%,
    "1x2": 200%,

    "2x3": calc(3 / 2 * 100%),
    "3x2": calc(2 / 3 * 100%),
    
    "4x3": calc(3 / 4 * 100%),
    "3x4": calc(4 / 3 * 100%),
    
    "16x9": calc(9 / 16 * 100%),

    "21x9": calc(9 / 21 * 100%)
) !default;

$enable-negative-margins: true;
$enable-cssgrid: true;


$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px,
  xxxl: 1700px
);

$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1320px,
  xxxl: 1600px
);


input.form-control.form-control-sm {
    font-size: 0.8rem;
  min-height: 29px;
}