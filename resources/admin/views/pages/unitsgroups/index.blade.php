@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button button="module" :route="route('admin.units.index')" label="Units" />
    <x-admin.button button="module" :route="route('admin.unitstypes.index')" label="Types" />
    <x-admin.button button="create" />
@endsection

@section('breadcrumbs')
    <li>Unit Groups</li>
@endsection

@section('filters', true)

@section('content')

    <x-admin.panel type="overview" title="Overview">

        <div class="overview">

            <div class="overview-header">
                <div class="row row-item">
                    <div class="col"><x-admin.link.sortable label="System name" attribute="systemname" /></div>
                    <div class="col-2 text-end"><x-admin.link.sortable label="Units" attribute="units_count" /></div>
                    <div class="col-1 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">

                @foreach ($data as $row)
                    <div class="row row-item">
                        <div class="col fw-bold"><a href="{{ route('admin.unitsgroups.edit', $row->id) }}">{{$row->systemlabel}}</a></div>
                        <div class="col-2 text-end"><a href="{{ route('admin.unitsgroups.edit', $row->id) }}">{{$row->units_count}}</a></div>
                        <div class="col-1 controls">
                            <x-admin.button group="overview" button="edit" :parameters="$row->id" />
                            <x-admin.button group="overview" button="destroy" :parameters="$row->id" data-name="{{$row->systemlabel}}" :disabled="$row->is_locked" />
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="overview-empty alert alert-info mt-3">There are no units groups yet.</div>
        </div>

    </x-admin.panel>

@endsection