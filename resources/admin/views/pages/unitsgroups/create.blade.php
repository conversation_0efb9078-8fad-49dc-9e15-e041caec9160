@extends('admin::layouts.app')

@section('title', true)

@section('flags')
    <x-admin::icon icon="lock" class="flag flag-is-locked" :if="$data->exists && $data->is_locked" />
@endsection

@section('controls')
    <x-admin.button button="back" />
    <x-admin.button button="destroy" data-name="{{$data->systemlabel}}" :if="$data->exists && !$data->is_locked" />
    <x-admin.button button="store" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.unitsgroups.index')}}">Unit Groups</a></li>
    <li>{{ !$data->exists ? 'create' : $data->systemlabel }}</li>
@endsection

@section('form')
    <x-admin.form
        :action="route('admin.unitsgroups.store')"
        method="POST"
        :bind="$data"
    />
    <x-admin.form.input type="hidden" name="id" value="{{ $data->id }}" />
@endsection

@section('content')

    <x-admin.page.row>
        <x-admin.page.col col="6">

            <x-admin.panel title="General">
                <x-admin.form.row>
                    <x-admin.form.label for="systemname" label="System name" required="true" />
                    <x-admin.form.input name="systemname" required="true" />
                </x-admin.form.row>
                
                <x-admin.form.row>
                    <x-admin.form.label for="value" label="Value" required="true" />
                    <x-admin.form.input name="value" /> 
                </x-admin.form.row>

            </x-admin.panel>

        </x-admin.page.col>
        <x-admin.page.col col="6">

            <x-admin.panel type="locales" title="Language specific">

                @foreach (config('locales') as $locale => $localeData)
        
                    <x-admin.form.row :locale="$locale">
                        <x-admin.form.label label="Name" for="name" />
                        <x-admin.form.input name="name" />
                    </x-admin.form.row>
        
                @endforeach
        
            </x-admin.panel>

        </x-admin.page.col>


    </x-admin.page.row>

    
    <x-admin.panel type="overview" title="Units">

        @slot('controls')
            <x-admin.button 
                type="button" 
                class="btn-unit-add"
                icon="plus"
                label="Add unit"
            />
        @endslot

        <div class="overview overview-units">

            <div class="overview-header">
                <div class="row row-item">
                    <div class="col">Systemname</div>
                    <div class="col-2">Type</div>
                    <div class="col-1">Value</div>
                    <div class="col-1 text-end">Is base unit</div>
                    <div class="col-1 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">

                @template('unit-row', false)
                    <div 
                        class="row row-item row-unit" 
                        data-unit-group-unit-id="[[id]]"
                        data-unit-id="[[unit_id]]"
                        x-data="{
                            is_locked: [[is_locked]],
                            unit_base_unit: [[unit_base_unit]]
                        }"
                    >
                        <input type="hidden" name="units[[[id]]][id]" value="[[id]]" />
                        <input type="hidden" name="units[[[id]]][unit_id]" value="[[unit_id]]" />

                        <div class="col fw-bold">[[unit_systemname]]</div>
                        <div class="col-2">[[unit_type_systemname]]</div>
                        <div class="col-1">[[unit_value]]</div>
                        <div class="col-1 text-end">
                            <x-admin::icon icon="check" x-show="unit_base_unit" class="icon-base-unit" />
                        </div>
                        <div class="col-1 controls">
                            <x-admin.button 
                                group="overview" 
                                button="destroy" 
                                data-id="[[id]]"
                                data-name="[[unit_systemname]]" 
                                data-confirm="btn-delete-unit-confirm" 
                                x-bind:class="is_locked ? ' btn-control-disabled' : ''"
                                x-bind:disabled="is_locked"
                            />
                        </div>
                    </div>
                @endtemplate

                @foreach ((old('units', null) ?? $data?->units?->toArray()) as $groupUnit)

                    @php
                        $unit = $units->firstWhere('id', $groupUnit['unit_id']);
                        $unitType = $unitTypes->firstWhere('id', $unit['unit_type_id'] ?? null);
                    @endphp

                    @usetemplate('unit-row', [
                        'id'                    => $groupUnit['id'],
                        'unit_id'               => $groupUnit['unit_id'],

                        'unit_systemname'       => $unit->systemname,
                        'unit_type_id'          => $unit->unit_type_id,
                        'unit_type_systemname'  => $unitType->systemname ?? '-',
                        'unit_value'            => $unit->value,
                        'is_locked'             => $groupUnit['is_locked'] ?? false,   
                        'unit_base_unit'        => $groupUnit['unit_id'] == $unitType?->unit_base_id
                    ])
                @endforeach

            </div>

            <div class="overview-empty alert alert-info mt-3">There are no units yet.</div>
        </div>
    
        

    </x-admin.panel> 

    @template('unit-modal', false)
        <x-admin.form.row>
            <x-admin.form.label for="unit" label="Unit" required="true" />
            <x-admin.form.select name="unit" :bind="null" :options="$units" required="true" />
        </x-admin.form.row>
    @endtemplate

@endsection 