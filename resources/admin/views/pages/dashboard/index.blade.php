@extends('admin::layouts.app')

@section('title', 'Welkom '.auth()->user()->fullname)

@section('controls')
@endsection

@section('breadcrumbs')
    <li>Dashboard</li>
@endsection

@section('content')

    <div class="row">

        @if( $notifications?->count() > 0)
            <div class="ms-auto col-4">
                <div class="card card-overview">
                    <div class="card-header bg-body-tertiary">
                        <div class="card-title col-auto mb-0">Notifications</div>
                    </div>
                    <div class="card-body">
                        <div class="overview-body">
                            @foreach ($notifications as $item)
                            
                                @if ($loop->first)

                                    <div class="row row-item border border-0 align-items-center">

                                @else
                                
                                    <div class="row row-item align-items-center">

                                @endif

                                        <div class="col">

                                            <div class="notification-title text-uppercase">
                                                
                                                @foreach ($item->labels as $label)
                                                    <span class="badge text-dark bg-{{$label->style}}">{{$label->{'title_'.App::getLocale()} }}</span>
                                                @endforeach

                                                    {{ $item->{'title_'.App::getLocale()} }}
                                            </div>

                                            <div class="text-color">
                                                {{ $item->created_at->diffForHumans()}}
                                            </div>
                                        
                                        </div>
                                            
                                        <div class="col-auto">
                                            <i class="fa-regular fa-bell fa-shake"></i>
                                            <i class="fa-regular fa-envelope"></i>
                                        </div>
                                </div>
                            @endforeach

                        </div>
                    </div>
                </div>
            </div>
        @endif

    </div>

@endsection

{{--         
<div class="col-8">
    
    <div class="row">

        <div class="col">

            <div class="card card-overview">

                <div class="card-body">

                    <div class="overview-header">

                        <div class="row row-item">
                            @foreach ($countries as $country)
                                
                                <div class="col text-center mx-2">
                                    <x-admin.flag :lang="$country"/>

                                    <div class="row row-item justify-content-center">

                                        @foreach (config('modules.products.pricing.sectors', [])  as $sector)

                                            @empty($sector['exception'])

                                                <div class="col border text-center">{{$sector['systemname']}}
                                                
                                                    <div class="row row-item">
                                                            
                                                        @foreach (config('modules.products.pricing.groups', [])  as $group)
                                                            <div class="col border text-center">{{$group['systemname']}}</div>
                                                        @endforeach
            
                                                    </div>
                                                </div>
                                            @endempty

                                        @endforeach
                                        


                                    </div>

                                </div>

                            @endforeach
                        </div>
                    </div>

                    <div class="overview-body"></div>
                </div>
            </div>
        </div>

        <div class="col-auto">

            <div class="card card-overview">
                <div class="card-body">
                    <div class="overview-header">

                        <div class="row row-item">
                            <div class="col">
                                
                                <div class="row row-item">
                                    <div class="col">

                                        <div class="row row-item">
                                            <div class="col">
                                                <div class="row row-item">
                                                    @foreach (config('modules.products.pricing.sectors', [])  as $sector)
                            
                                                        @isset($sector['exception'])
                
                                                            <div class="col text-center">{{$sector['systemname']}}</div>
                                                        @endisset
                
                                                    @endforeach
                                                
                                                </div>
                                            </div>
                                        </div>
                                        
                                    </div>
                                </div>

                            </div>

                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>

</div> 
--}}