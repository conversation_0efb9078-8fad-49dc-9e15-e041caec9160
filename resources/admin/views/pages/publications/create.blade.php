@extends('admin::layouts.app')
@section('content')

<form action="{{ route('admin.'.App::get('module').'.store') }}" method="POST" enctype="multipart/form-data">
<input type="hidden" name="id" value="{{ isset($data) ? $data->id : '' }}">
<input type="hidden" name="locale_current" value="{{ app()->getLocale() }}">
@csrf

<nav class="content-header">

    <div class="breadcrumbs">
        <ol>
            <li><a href="{{route('admin.'.App::get('module').'.index')}}">Publications</a></li>
            <li>{{ empty($data->id) ? 'Add' : $data->systemname }}</li>
        </ol>
    </div>

    <div class="controls">

        <x-admin.button type="back" />

        <x-admin.button type="save" />
    </div>
</nav>

<div class="content-body">

    <h1 class="title">{{ empty($data->id) ? 'Create Publications' : 'Edit ' . $data->systemname }}</h1>
    
    <x-admin.alert/>

    <div class="card card-form">
        <div class="card-header">
            <div>
                <h2>General information</h2>
            </div>
        </div>

        <div class="card-body">
            
            <x-admin.form.row.systemname :any="$data" />

            <x-admin.form.row.status :any="$data" column="status"/>
            <x-admin.form.row.toggle :any="$data" column="is_highlighted" />

            <div class="row-block">

                <label class="label label-required" for="published_at">Publication date</label>
                
                <div class="input">
                    <div class="input-group"> 
                        
                        <input type="text" class="form-control datepicker" autocomplete="off" name="published_at" id="published_at" value="{{$data->toArray()['published_at']  ?? ''}}"> 

                        <div class="input-group-append"> 
                            <span class="input-group-text" id="basic-addon2"><i class="fa fa-calendar"></i></span> 
                        </div> 
                </div>
                </div>
            </div>

            <div class="row-block">
                <label class="label label-required" for="expired_at">Expiration date</label>
                <div class="input">

                    <div class="input-group"> 

                        <input type="text" class="form-control datepicker" autocomplete="off" name="expired_at" id="expired_at" value="{{$data->toArray()['expired_at'] ?? ''}}"> 

                        <div class="input-group-append"> 
                            <span class="input-group-text" id="basic-addon2"><i class="fa fa-calendar"></i></span> 
                        </div> 

                    </div>
                </div>
            </div>
            
            <div class="row-block">
                <label class="label label-required" for="type_id">Type</label>
                <div class="input">
                    <select name="type_id" id="type_id" class="form-control">

                        <option value="">Nothing selected</option>
                        
                        @foreach ($publicationTypes as $publicationType)
                            <option value="{{ $publicationType->id }}" {{ old('type_id', $data->type_id) ==  $publicationType->id ? "selected" : "" }}>{{ $publicationType->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            
            <div class="row-block">
                <label class="label" for="author">Author</label>
                <div class="input">
                    <input type="text" name="author" id="author" class="form-control" value="{{ old('author', $data) }}">
                </div>
            </div>

            
            <div class="row-block">
                <label class="label" for="publisher">publisher</label>
                <div class="input">
                    <input type="text" name="publisher" id="publisher" class="form-control" value="{{ old('publisher', $data) }}">
                </div>
            </div>

            
            <div class="row-block">
                <label class="label" for="author_job_description">author_job_description</label>
                <div class="input">
                    <input type="text" name="author_job_description" id="author_job_description" class="form-control" value="{{ old('author_job_description', $data) }}">
                </div>
            </div>
    
        </div>
    </div>

    <div class="card card-form">
        
    
        <div class="card-header">
            <h2>Language specific</h2>
            <div class="controls">
                <x-admin.locales/>
            </div>
        </div>
    
        <div class="card-body">
            <div class="tab-content">
                @foreach (config('languages') as $lang)

                    <div class="tab-pane fade pt-3 {{($lang['code'] == App::getLocale() ? 'show active' : '') }} " id="lang-{{$lang['code']}}" role="tabpanel">

                        <x-admin.form.row.status :any="$data" :column="'status_'.$lang['code']"/>

                        <x-admin.form.row.toggle :any="$data" column="is_indexable_{{$lang['code']}}" />

                        <x-admin.form.row.langtext :any="$data" :$lang column="title" :required="1" />

                        <x-admin.form.row.langtext :any="$data" :$lang column="title_tab" />

                        {{-- <x-admin.form.row.meta :any="$data" :$lang /> --}}

                        <x-admin.form.row.texteditor :any="$data" :$lang column="content" />

                    </div>
                @endforeach
            </div>
    
        </div>
    </div>
    
    <x-admin.widget.attachments :module="App::get('module')" :any="$data" />
</div>


</form>
@endsection