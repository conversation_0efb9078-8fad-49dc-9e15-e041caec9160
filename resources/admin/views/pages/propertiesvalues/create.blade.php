@extends('admin::layouts.app')

@section('title', true)

@section('flags')
    <x-admin::icon icon="lock" class="flag flag-is-locked" :if="$data->exists && $data->is_locked" />
@endsection

@section('controls')
    <x-admin.button button="back" />
    <x-admin.button button="destroy" data-name="{{$data->systemlabel}}" :if="$data->exists && !$data->is_locked" />
    <x-admin.button button="store" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.properties.index')}}">Properties</a></li>
    <li><a href="{{route('admin.properties.edit', $property->id)}}">{{$property->systemname}}</a></li>
    <li><a href="{{route('admin.propertiesvalues.index', $property->id)}}">Values</a></li>
    <li>{{ !$data->exists ? 'Create' : $data->systemlabel }}</li>
@endsection

@section('form')

    <x-admin.form
        :action="route('admin.propertiesvalues.store', $property->id)"
        method="POST"
        :bind="$data"
        x-data="{
            system_context: '{{ $data->system_context }}',
            system_context_unit_id: '{{ $data->system_context_unit_id }}',
            system_context_unit_compositional_id: '{{ $data->system_context_unit_compositional_id }}',
            system_context_unit_compositional_unit_id: '{{ $data->system_context_unit_compositional_unit_id }}',
            is_global: '{{ $data->is_global }}'
        }"
    />
    
    <x-admin.form.input type="hidden" name="id" value="{{ $data->id }}" />
    <x-admin.form.input type="hidden" name="property_id" value="{{ $property->id }}" />

@endsection

@section('content')

    <x-admin.page.row>
        <x-admin.page.col col="6">

            <x-admin.panel title="General information">

                <x-admin.form.row>
                    <x-admin.form.label for="systemname" label="System name" required="true" />
                    <x-admin.form.input name="systemname" required="true" />
                </x-admin.form.row>
            
                @if ($property->system_context === \App\Enums\SystemContext::UNIT)
                    <x-admin.form.row>
                        <x-admin.form.label for="system_context" label="Context" required="true" />
                        <x-admin.form.input
                            name="value"
                            label="{{ $property->system_context_value }}"
                            required="true"
                            suffix="{{ $property->system_context_value }}"
                        />
                    </x-admin.form.row>
                @endif

            </x-admin.panel>

        </x-admin.page.col>
        <x-admin.page.col col="6">

            <x-admin.panel type="locales" title="Language specific">

                @foreach (config('locales') as $locale => $localeData)

                    <x-admin.form.row :locale="$locale">
                        <x-admin.form.label label="Name" for="name" />
                        <x-admin.form.input name="name" />
                    </x-admin.form.row>

                    <x-admin.form.row :locale="$locale">
                        <x-admin.form.label label="Content" for="content" />
                        <x-admin.form.textarea textEditor="default" name="content" />
                    </x-admin.form.row>

                @endforeach

            </x-admin.panel>

        </x-admin.page.col>
    </x-admin.page.row>

@endsection 