@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button button="back" :route="route('admin.properties.edit', $property->id)" />
    <x-admin.button button="create" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.properties.index')}}">Properties</a></li>
    <li><a href="{{route('admin.properties.edit', $property->id)}}">{{ $property->systemname }}<a></li>
    <li>Values</li>
@endsection

@section('content')

    <x-admin.panel type="overview" title="Properties values">

        <div class="overview">

            <div class="overview-header">

                <div class="row row-item">

                    <div class="col"><x-admin.link.sortable label="Systemname" attribute="systemname" /></div>
                    
                    {{-- @if ($property->unit_type != \App\Enums\Property\UnitType::TEXT) 
                        <div class="col-3 text-end">Value</div>
                    @endif
                    @if ($property->system_usage == \App\Enums\PropertySystemUsage::PRODUCTS) 
                        <div class="col-1 text-end">Products</div>
                    @endif --}}
                    <div class="col-1 text-end">Actions</div>
                    
                </div>
            </div>

            <div class="overview-body">
                @foreach ($data as $row)

                    <div class="row row-item">

                        <div class="col fw-bold">
                            <a href="{{ route('admin.propertiesvalues.edit', [$row->property_id, $row->id]) }}">
                                {{$row->systemname}}
                            </a>
                        </div>

                        {{-- @if ($property->unit_type != \App\Enums\Property\UnitType::TEXT) 
                            <div class="col-3 text-end">{{$row->value}}</div>
                        @endif

                        @if ($property->system_usage == \App\Enums\PropertySystemUsage::PRODUCTS) 
                            <div class="col-1 text-end">
                                {{$row->products->count()}}
                            </div>
                        @endif  --}}

                        <div class="col-1 controls">
                            <x-admin.button group="overview" button="edit" :parameters="[$row->property_id, $row->id]" />
                            <x-admin.button group="overview" button="destroy" :parameters="[$row->property_id, $row->id]" data-name="{{$row->systemname}}" :disabled="$row->is_locked" />
                        </div>

                        
                    </div>
                @endforeach
            </div>

            <div class="overview-empty alert alert-info mt-3">There are no property values yet.</div>

        </div>
 
    </x-admin.panel>

@endsection