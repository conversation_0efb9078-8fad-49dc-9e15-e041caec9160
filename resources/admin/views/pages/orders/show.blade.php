@extends('admin::layouts.app')
@section('content')

<nav class="content-header">
    
    <div class="breadcrumbs">
        <ol>
            <li><a href="{{ route('admin.'.App::get('module').'.index')}}">Orders</a></li>
            <li>{{$data->reference}}</li>
        </ol>
    </div>

    <div class="controls">
        {{-- <a class="btn btn-sm btn-outline-primary " href="{{ route('admin.'.App::get('module').'.index') }}">
            Back
        </a>
        @if (!$data->is_verified)
        <a class="btn btn-sm btn-outline-primary" href="{{ route(App::get('module').'.activation', $data->id) }}">
            Activation
        </a>
        @else
            <a class="btn btn-sm btn-outline-primary" href="{{ route(App::get('module').'.passwordForget', $data->id) }}">
                Forgot password
            </a>
        @endif --}}

        <a class="btn btn-sm btn-warning" href="">
            Modify
        </a>

        <button type="button" class="btn btn-info btn-order-copy btn-sm">Copy</button>

        <a 
            type="button" 
            href="" 
            class="btn btn-info btn-sync btn-sm text-white">Sync
        </a>
        
        <div class="btn-group btn-group-sm"> 
            <a type="button" href="?pdf_confirmation=confirmation&amp;type=view" target="_blank" class="btn btn-primary text-white">Order Bevestiging</a> 
            <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown"></button> 
            <div class="dropdown-menu dropdown-menu-right"> 
                <a class="dropdown-item btn-confirmation-sent" data-name="confirmation" data-type="confirmation" data-id="3979" data-order-number="999672" data-message="" data-has-confirmation="0" data-sent-version-default="1" data-is-sent="0" href="#">Mail to customer</a> 
                <a class="dropdown-item" href="?pdf_confirmation=confirmation&amp;type=view" target="_blank">View</a> 
                <a class="dropdown-item" href="?pdf_confirmation=confirmation&amp;type=download" download="">Download</a> 
                <a class="dropdown-item" href="?pdf_confirmation=confirmation&amp;type=view&amp;renew=1" target="_blank">Renew (force)</a> 
                <a class="dropdown-item" href="?pdf_confirmation=confirmation&amp;type=html" target="_blank">View as HTML</a> 
            </div> 
        </div>

        <div class="btn-group btn-group-sm"> 
            <a type="button" href="?pdf_preview=1&amp;type=view" target="_blank" class="btn btn-primary text-white">Previewbon</a> 
            <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown"></button> 
            <div class="dropdown-menu dropdown-menu-right"> 
                <a class="dropdown-item btn-preview-sent" data-name="preview" data-type="preview" data-id="3979" href="#">Mail to customer</a> 
                <a class="dropdown-item" href="?pdf_preview=1&amp;type=view" target="_blank">View</a> 
                <a class="dropdown-item" href="?pdf_preview=1&amp;type=download" download="">Download</a> 
                <a class="dropdown-item" href="?pdf_preview=1&amp;type=view&amp;renew=1" target="_blank">Renew (force)</a> 
                <a class="dropdown-item" href="?pdf_preview=1&amp;type=html" target="_blank">View as HTML</a> 
            </div> 
        </div>

        <div class="btn-group btn-group-sm"> 
            <a type="button" href="?pdf_packingslip=1&amp;type=view" target="_blank" class="btn btn-primary text-white">Pakbon</a> 
            <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false"></button> 
            <div class="dropdown-menu dropdown-menu-right" style=""> 
                <a class="dropdown-item" href="?pdf_packingslip=1&amp;type=view" target="_blank">View</a> 
                <a class="dropdown-item" href="?pdf_packingslip=1&amp;type=download" download="">Download</a> 
                <a class="dropdown-item" href="?pdf_packingslip=1&amp;type=view&amp;renew=1" target="_blank">Renew (force)</a> 
                <a class="dropdown-item" href="?pdf_packingslip=1&amp;type=html" target="_blank">View as HTML</a> 
            </div> 
        </div>

        <a type="button" href="/admin/orders" class="btn btn-outline-dark btn-sm">Terug</a>
    </div>
</nav>

<div class="content-body">
    <div class="row align-items-center">
        
        <div class="col-auto">
            <h1 class="pt-3">
                Order: {{$data->id}} 
                {{$data->reference}}
            </h1>
        </div>
        <div class="col-auto text-start">
            <span class="badge badge-0">Bestanden geüpload</span>
        </div>

        <div class="col order-icons text-end">
            @foreach ([
                    [
                        'field' => 'is_customer', 
                        'icon' => '<i class="order-icon fas fa-user-circle text-info" title="Is customer order"></i>'
                    ],
                    [
                        'field' => 'is_locked', 
                        'icon' => '<i class="order-icon fas fa-lock text-danger" title="is locked"></i>'
                    ],
                    [
                        'field' => 'is_standbuilder', 
                        'icon' => '<i class="order-icon fas fa-user-tag text-info" title="is standbuilder"></i>'
                    ],
                    [
                        'field' => 'is_urgent', 
                        'icon' => '<i class="order-icon fas fa-shipping-fast text-danger" title="Is urgent"></i>'
                    ],
                    [
                        'field' => 'is_foreign', 
                        'icon' => '<i class="order-icon fas fa-globe text-primary" title="is foreign"></i>'
                    ],
                    [
                        'field' => 'is_reproduction', 
                        'icon' => '<i class="order-icon fas fa-retweet text-danger" title="is reproduction for:"></i>'
                    ],
                    [
                        'field' => 'is_correction', 
                        'icon' => '<i class="order-icon fas fa-money-bill-alt text-success" title="is correction"></i>'
                    ],
                ] as $item)

                    {!! $data->{$item['field']} ? $item['icon'] : '' !!}

                @endforeach

        </div>
    </div>
    
    <x-admin.alert/>

    <div class="row">
        <div class="col-3">

            <div class="card mt-5">
                <div class="card-header bg-body-tertiary border-bottom">
                    <div class="card-title col-auto mb-0">General information</div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-5">QOS id:</div>
                        <div class="col-7">{{$data->qos_id}}</div>
                    </div>
                    <div class="row">
                        <div class="col-5">Ordernumber:</div>
                        <div class="col-7">{{$data->order_number}}</div>
                    </div>
                    <div class="row">
                        <div class="col-5">Deadline at:</div>
                        <div class="col-7">{{ \Carbon\Carbon::parse($data->deadline_at)->format('d-m-Y \a\t H:i') }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-5">Delivery at:</div>
                        <div class="col-7">{{ \Carbon\Carbon::parse($data->delivery_at)->format('d-m-Y \a\t H:i') }}</div>
                    </div>
                    <div class="row">
                        <div class="col-5">Created at:</div>
                        <div class="col-7">{{ \Carbon\Carbon::parse($data->created_at)->format('d-m-Y \a\t H:i') }}</div>
                    </div>
                    <div class="row">
                        <div class="col-5">Synced at:</div>
                        <div class="col-7">{{ \Carbon\Carbon::parse($data->synced_at)->format('d-m-Y \a\t H:i') }}</div>
                    </div>
                    <div class="row">
                        <div class="col-5">Comments:</div>
                        <div class="col-7">{{$data->comment}}</div>
                    </div>
                </div>
            </div>

        </div>
        <div class="col-3">
            
            <div class="card mt-5">
                <div class="card-header bg-body-tertiary border-bottom">
                    <div class="card-title col-auto mb-0">Account information</div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-5">Customer:</div>
                        <div class="col-7"><a href="">TestCo BV</a>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-5">User:</div>
                        <div class="col-7"> <a href="">Harrald Vissers</a> </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-5">Creator:</div>
                        <div class="col-7"> <a href="">Anne Willems</a>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-5">Sales manager:</div>
                        <div class="col-7"> <a href="">Marc Hagens</a> </div>
                    </div>
                    <div class="row">
                        <div class="col-5">Office manager :</div>
                        <div class="col-7"> <a href="">Marc Hagens</a> </div>
                    </div>
                </div>
            </div>
        </div>
        

        @foreach ($data->addresses as $address)
            @php
                        
                $salutation = '';

                if (isset($address->pivot->gender)) {
                    $salutation = $address->pivot->firstname == 'Financiële' 
                        ? 't.a.v.'
                        : ($address->pivot->gender == 'male' 
                            ? 'Dhr.' 
                            : 'Mevr.'
                    );
                }

            @endphp

            <div class="col-3">
                
                <div class="card mt-5">
                    <div class="card-header bg-body-tertiary border-bottom">
                        <div class="card-title col-auto mb-0">{{$address->pivot->type}}</div>
                    </div>
                    <div class="card-body">
                        <div>{{$address->pivot->company}}</div>
                        <div>{{ $salutation }} {{$address->pivot->firstname}}{{' '.$address->pivot->lastname ?? ' '}}</div>
                        <div>{{$address->getAddress()}}</div>
                        <div>{{$address->getPostalcodeAndStreet()}}</div>
                        <div>{{$address->country->{'name_'.App::getLocale()} }}</div>
                        <div>
                            <a href="mailto:{{$address->pivot->email}}">
                                {{$address->pivot->email}}
                            </a>
                        </div>
                        <div>
                            <a href="mailto:{{$address->pivot->email}}">
                                {{$address->pivot->phone}}
                            </a>
                        </div>
                        <div class="row">
                            <div class="col-auto">Distance</div>
                            <div class="col">1KM</div>
                        </div>
                    </div>
                </div>

            </div>

        @endforeach
    </div>

    <div>
                
        <div class="card card-overview mt-5">
            <div class="card-header bg-body-tertiary border-bottom">
                <div class="card-title col-auto mb-0">Items</div>
            </div>
            <div class="card-body">
                <div class="overview-header row">
                    <div class="col">
                        <div class="row">
                            <div class="col col-3">
                                <div class="row">
                                    <div class="col col-6 item-code">Code</div>
                                    <div class="col col-6 item-type">Type</div>
                                </div>
                            </div>
                            <div class="col item-name text-truncate">Description</div>
                        </div>
                    </div>
                    <div class="col col-2 text-end">
                        <div class="row">
                            <div class="col col-2 text-end px-1">Qty.</div>
                            <div class="col col-5 text-end">Width</div>
                            <div class="col col-5 text-end">Height</div>
                        </div>
                    </div>
                    <div class="col col-1 text-end">Price</div>
                    <div class="col col-1 text-end">Price total</div>
                    <div class="col col-1 text-end">Status</div>
                    <div class="col col-1 text-end">Options</div>
                </div>
                
                <div class="overview-body">
                    <div  class="row-item row row-sm row-item-parent align-items-center" data-id="15547" data-cart-id="657c669030fc3"
                        data-parent-id="0">
                        <div class="col">
                            <div class="row">
                                <div class="col col-3">
                                    <div class="row">
                                        <div class="col col-6 item-code">S0130</div>
                                        <div class="col col-6 item-type">fabrics</div>
                                    </div>
                                </div>
                                <div class="col item-name text-truncate">BlockOut Textile BlackBack 320 DS - Voorzien van een open
                                    zoom<br><i>dsfsdf</i></div>
                            </div>
                        </div>
                        <div class="col col-2">
                            <div class="row">
                                <div class="col col-2 text-end">1</div>
                                <div class="col col-5 text-end">111</div>
                                <div class="col col-5 text-end">222cm</div>
                            </div>
                        </div>
                        <div class="col col-1 text-end">€ 78,34</div>
                        <div class="col col-1 text-end">€ 78,34</div>
                        <div class="col col-1 text-end"><span class="badge badge-0">Files uploaded</span></div>
                        <div class="col col-1 text-end text-primary"><span type="button" class="btn-item-xml-download me-2"
                                data-files="[&quot;L21lZGlhL29yZGVycy8zOTk0L2RhdGEvOTk5Njg3XzAwMV83MDQxODkueG1s&quot;]"><i
                                    class="fas fa-file-code"></i></span><span type="button" class="btn-item-download me-2"
                                data-files="[&quot;L21lZGlhL29yZGVycy8zOTk0L2RhdGEvMDAxLzY1N2M2NjkwMzQxOTBfdXBsb2FkX3ZhbnN0cmFhdGVuLWxvZ28td2Vic2l0ZS1leHRyYS1rbGVpbi5wZGY=&quot;]"><i
                                    class="fas fa-download"></i></span><span type="button" class="btn-item-view" style=""><i
                                    class="fas fa-eye"></i></span></div>
                        <div class="col-12 details collapse collapsed">
                            <div class="row p-3 row-item-inline">
                                <div class="col-12">
                                    <div class="row m-1">
                                        <div class="col-12 col-md-7">
                                            <div class="row">
                                                <div class="col-12">
                                                    <table class="table table-bordered table-light table-hover table-thead-rotate">
                                                        <thead>
                                                            <tr>
                                                                <th>Item</th>
                                                                <th class="rotate" style="min-width: 50px;">
                                                                    <div><span>dtp</span></div>
                                                                </th>
                                                                <th class="rotate" style="min-width: 50px;">
                                                                    <div><span>rip</span></div>
                                                                </th>
                                                                <th class="rotate" style="min-width: 50px;">
                                                                    <div><span>printing</span></div>
                                                                </th>
                                                                <th class="rotate" style="min-width: 50px;">
                                                                    <div><span>cutting</span></div>
                                                                </th>
                                                                <th class="rotate" style="min-width: 50px;">
                                                                    <div><span>confection</span></div>
                                                                </th>
                                                                <th class="rotate" style="min-width: 50px;">
                                                                    <div><span>assembly</span></div>
                                                                </th>
                                                                <th class="rotate" style="min-width: 50px;">
                                                                    <div><span>packaging</span></div>
                                                                </th>
                                                                <th class="rotate" style="min-width: 50px;">
                                                                    <div><span>shipping</span></div>
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody class="table-hover">
                                                            <tr>
                                                                <td class="font-weight-bold text-wrap">BlockOut Textile BlackBack 320 DS -
                                                                    Voorzien van een open zoom</td>
                                                                <td class=""><i class="fas fa-circle text-success"></i></td>
                                                                <td class=""><i class="fas fa-circle text-success"></i></td>
                                                                <td class=""><i class="fas fa-circle text-success"></i></td>
                                                                <td class=""><i class="fas fa-circle text-danger"></i></td>
                                                                <td class=""><i class="fas fa-circle text-success"></i></td>
                                                                <td class=""><i class="fas fa-circle text-danger"></i></td>
                                                                <td class=""><i class="fas fa-circle text-success"></i></td>
                                                                <td class=""><i class="fas fa-circle text-success"></i></td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <div class="col-12"></div>
                                                <div class="col-12">
                                                    <div class="row row-cols-3 my-n3">
                                                        <div class="col py-3">
                                                            <div class="ratio ratio-contain ratio-1x1">
                                                                <div class="inner"><img class="border rounded p-1"
                                                                        src="/media/orders/3994/data/001/657c669034190_preview_vanstraaten-logo-website-extra-klein.jpg">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12 col-md-5">
                                            <div class="row">
                                                <div class="col-12"></div>
                                                <div class="col-12">
                                                    <table class="table table-sm table-bordered table-light">
                                                        <thead>
                                                            <tr>
                                                                <th>Question</th>
                                                                <th>Answer</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <th class="answer-name">Size and quantity</th>
                                                                <td class="answers-values">
                                                                    <div class="row">
                                                                        <div class="col-12"><span
                                                                                class="answer-name w-50">width:</span><span
                                                                                class="answer-value w-50">111cm</span></div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-12"><span
                                                                                class="answer-name w-50">height:</span><span
                                                                                class="answer-value w-50">222cm</span></div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-12"><span
                                                                                class="answer-name w-50">amount:</span><span
                                                                                class="answer-value w-50">1</span></div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th class="answer-name">Material</th>
                                                                <td class="answers-values">
                                                                    <div class="row">
                                                                        <div class="col-12"><span class="answer-value">BlockOut Textile
                                                                                BlackBack 320 - DS 320 cm - 260 grs</span></div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th class="answer-name">Type of finishing</th>
                                                                <td class="answers-values">
                                                                    <div class="row">
                                                                        <div class="col-12"><span class="answer-value">Open zoom</span>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th class="answer-name">Tunnel size</th>
                                                                <td class="answers-values">
                                                                    <div class="row">
                                                                        <div class="col-12"><span class="answer-value">25mm</span></div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th class="answer-name">Confection</th>
                                                                <td class="answers-values">
                                                                    <div class="row">
                                                                        <div class="col-12"><span class="answer-value">Voorzien van een open
                                                                                zoom</span></div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th class="answer-name">Printed or unprinted</th>
                                                                <td class="answers-values"></td>
                                                            </tr>
                                                            <tr>
                                                                <th class="answer-name">Packaging</th>
                                                                <td class="answers-values">
                                                                    <div class="row">
                                                                        <div class="col-12"><span class="answer-value">roll</span></div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th class="answer-name">How many on one roll?</th>
                                                                <td class="answers-values"></td>
                                                            </tr>
                                                            <tr>
                                                                <th class="answer-name">Accessoires</th>
                                                                <td class="answers-values"></td>
                                                            </tr>
                                                            <tr>
                                                                <th class="answer-name">Comments</th>
                                                                <td class="answers-values"></td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <div class="col-12"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>

    <div class="row">

        <div class="col-9">

            <div class="card card-overview mt-5">

                <div class="card-header bg-body-tertiary border-bottom">
                    <div class="card-title col-auto mb-0">Packaging</div>
                </div>

                <div class="card-body">

                    <div class="overview-header">


                        <div class="row row-item">
                            <div class="col col-3">Type</div> 
                            <div class="col col-3">Subtype</div> 
                            <div class="col col-2">Quantity</div> 
                            <div class="col col-2">Weight</div> 
                            <div class="col col-2 text-end">Options</div> 

                        </div>
                    </div>
                    
                    <div class="overview-body">
                        <div class="row-item row" data-id="24654"> 
                            <div class="col col-3">roll</div> 
                            <div class="col col-3">-</div> 
                            <div class="col col-2">1</div> 
                            <div class="col col-2">-</div> 
                            <div class="col col-2 text-end"></div> 
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="col-3">
            <div class="card card-overview mt-5">

                <div class="card-header bg-body-tertiary border-bottom">
                    <div class="card-title col-auto mb-0">Totals</div>
                </div>

                <div class="card-body">
                    
                    <div class="overview-body">
                        <div class="row ">
                            <div class="col">Total items (excl):</div>
                            <div class="col-auto text-right">€ 78,34</div>
                        </div>
                        <div class="row ">
                            <div class="col">Shippingcosts (excl):</div>
                            <div class="col-auto text-right">€ 25,90</div>
                        </div>
                        <div class="row ">
                            <div class="col">Express charges (excl):</div>
                            <div class="col-auto text-right">€ 0,00</div>
                        </div>
                        <div class="row ">
                            <div class="col">Energy charges (excl):</div>
                            <div class="col-auto text-right">€ 0,42</div>
                        </div>
                        <div class="row ">
                            <div class="col">Tax:</div>
                            <div class="col-auto text-right">€ 21,98</div>
                        </div>
                        <div class="row border-top">
                            <div class="col">Total (excl):</div>
                            <div class="col-auto text-right">€ 104,66</div>
                        </div>
                        <div class="row border-top">
                            <div class="col">Total (incl):</div>
                            <div class="col-auto text-right">€ 126,64</div>
                        </div>
                        <div class="row border-top text-danger font-weight-bold">
                            <div class="col">Total verrekend (incl):</div>
                            <div class="col-auto text-right">€ 0,00</div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div>
                
        <div class="card card-overview mt-5">
            <div class="card-header bg-body-tertiary border-bottom">
                <div class="card-title col-auto mb-0">Files</div>
            </div>
            <div class="card-body">
                @foreach($data->files as $file )

                    <div class="row row-item">

                        <div class="col col-2">{{$file->type}}</div>
                        <div class="col">{{$file->path}} {{$file->filename_original}}</div>
                        <div class="col col-1">{{Temp::humanFileSize($file->size)}}</div>
                        <div class="col col-2"><i class="fas fa-download"></i></div>
                        
                    </div>
                @endforeach
            </div>
        </div>

    </div>
</div>
@endsection