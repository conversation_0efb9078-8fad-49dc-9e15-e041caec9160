@extends('admin::layouts.app')
@section('content')

<nav class="content-header">
    <div class="breadcrumbs">
        <ol>
            <li>Orders</li>
        </ol>
    </div>

    <div class="controls">
        
    </div>
</nav>

<div class="content-body">

    


    <h1 class="title">Overview</h1>
    
    <x-admin.alert/>

    <div class="card card-overview">

        <div class="card-header">
            <div>
                <h2>Orders</h2>
            </div>
        </div>

        <div class="card-body ">

            <div class="overview-header">

                <div class="row row-item">

                    <div class="col-2 pe-1">

                        <div class="row">

                            <div class="col-3 text-end">Id</div>
                            <div class="col-4 text-end">QOS id</div>
                            <div class="col-5 text-end text-truncate">OrderNr</div>
                        </div>
                    </div>

                    <div class="col col-2 px-1">Company</div>

                    <div class="col-3 px-1">
                        <div class="row">
                            <div class="col px-1">Reference</div>
                            <div class="col col-2 text-end px-1">Total <small>(excl)</small></div>
                            <div class="col col-2 text-end px-1">Total <small>(incl)</small></div>
                        </div>
                    </div>

                    <div class="col col-2 text-end">
                        <div class="row">
                            <div class="col-7 text-end px-1">Due date</div>
                            <div class="col-5 text-end px-1">Delivery date</div>
                        </div>
                    </div>

                    <div class="col col-1 text-end">Status</div>

                    <div class="col col-2">
                        <div class="row">
                            <div class="col text-end">Date created</div>
                            <div class="col-7 text-end">Actions</div>
                        </div>
                    </div>

                </div>
            </div>

            <div class="overview-body">

                @foreach ($data as $row)

                @php
                    $btnDelete = '&nbsp';
                    $btnSync = '&nbsp;';
                    $btnModify = '&nbsp;';
                    $btnPDFProduction = '&nbsp;';
                    $btnPDFPackingSlip = '&nbsp;';

                    $btnView = '
                        <a href="'. route('admin.'.App::get('module').'.show', $row->id).'">
                            <i class="fas fa-eye"></i>
                        </a>
                    ';


                    if ($row->is_locked == 0) {

                        if (is_null($row->order_number)) {

                            $btnDelete = '
                                <a 
                                    type="button" 
                                    class="btn-delete" 
                                    name="delete" 
                                    href="#" 
                                    data-href="'.route('admin.'.App::get('module').'.destroy', $row->id).'."
                                    data-name="'.$row->systemname.'"
                                    data-text="
                                    ">

                                    <i class="fas fa-trash"></i>
                                </a>
                            ';
                        }

                        $btnModify = '
                            <a 
                                type="button" 
                                class="btn-modify" 
                                href="#"
                                >
                                <i class="fas fa-edit"></i>
                            </a>
                        ';
                        
                        if ($row->status_high < 110 ) {
                            $btnSync = '
                                <a 
                                    type="button" 
                                    class="btn-view" 
                                    name="Sync QOS & files"
                                    title="Sync QOS & files"
                                    href="">
                                    <i class="fas fa-sync"></i>
                                </a>';
                        }

                        
                    }

                    if (!is_null($row->order_number)) {

                        $btnPDFProduction = '
                            <a 
                                type="button" 
                                class="btn-view" 
                                name="View production PDF"
                                title="View production PDF"
                                target="_blank"
                                href="">
                                <i class="fas fa-clipboard-list"></i>
                        </a>';

                        $btnPDFPackingSlip = '
                            <a 
                                type="button" 
                                class="btn-view" 
                                name="View packing-slip PDF"
                                title="View packing-slip PDF"
                                target="_blank"
                                href="">
                                <i class="fas fa-clipboard-check"></i>
                            </a>';
                    }

                    $customerOrder = is_null($row['admin_id']) || $row['is_customer'] == 1
                        ? ' fw-bold'
                        : '';

                    $isError = strtotime($row->deadline_at) > strtotime($row->delivery_at)
                        ? ' style="background-color: #ffc107 !important;"' 
                        : '';
                    
                    $rowStatuses = [];

                    if ($row->is_finished == 1) {
                        $rowStatuses[] = 'is-finished';
                    }

                    if ($row->is_onhold == 1) {
                        $rowStatuses[] = 'is-onhold';
                    }

                    if ($row->is_tolate == 1) {
                        $rowStatuses[] = 'is-tolate';
                    }

                    if ($row->is_reproduction == 1) {
                        $rowStatuses[] = 'is-reproduction';
                    }
                    
                    
                @endphp

                <div class="row row-item {{implode(' ' , $rowStatuses)}} {{$customerOrder}}" {!! $isError !!}>

                    <div class="col-2 pe-1">
                        <div class="row">
                            <div class="col-3 text-end">{{$row->id}}</div>
                            <div class="col-4 text-end">
                                <a href="{{route('admin.'.App::get('module').'.show', $row->id)}}">
                                    {{$row->qos_id}}
                                </a>
                            </div>
                            <div class="col-5 text-end text-truncate">{{$row->order_number}}</div>
                        </div>
                    </div>

                    <div class="col col-2 px-1">

                        <a href="{{route($app.'.companies.edit', $row->company->id)}}">
                            {{$row->company->name ?? ""}}
                        </a>

                        @foreach ([
                        [
                            'field' => 'is_customer', 
                            'icon' => '<i class="order-icon fas fa-user-circle text-info" title="Is customer order"></i>'
                        ],
                        [
                            'field' => 'is_locked', 
                            'icon' => '<i class="order-icon fas fa-lock text-danger" title="is locked"></i>'
                        ],
                        [
                            'field' => 'is_standbuilder', 
                            'icon' => '<i class="order-icon fas fa-user-tag text-info" title="is standbuilder"></i>'
                        ],
                        [
                            'field' => 'is_urgent', 
                            'icon' => '<i class="order-icon fas fa-shipping-fast text-danger" title="Is urgent"></i>'
                        ],
                        [
                            'field' => 'is_foreign', 
                            'icon' => '<i class="order-icon fas fa-globe text-primary" title="is foreign"></i>'
                        ],
                        [
                            'field' => 'is_reproduction', 
                            'icon' => '<i class="order-icon fas fa-retweet text-danger" title="is reproduction for:"></i>'
                        ],
                        [
                            'field' => 'is_correction', 
                            'icon' => '<i class="order-icon fas fa-money-bill-alt text-success" title="is correction"></i>'
                        ],
                    ] as $item)

                        {!! $row->{$item['field']} ? $item['icon'] : '' !!}

                    @endforeach

                    </div>

                    <div class="col-3 px-1">
                        <div class="row">
                            <div class="col px-1">
                                <a href="{{route('admin.'.App::get('module').'.show', $row->id)}}">
                                    {{$row->reference}}
                                </a>
                            </div>
                            <div class="col col-2 text-end px-1">{{ Number::currency($row->total_excl, in: 'EUR', locale: App::getLocale())}} </div>
                            <div class="col col-2 text-end px-1">{{Number::currency($row->total_incl, in: 'EUR', locale: App::getLocale())}}</div>
                        </div>
                    </div>

                    <div class="col col-2 text-end">
                        <div class="row">
                            <div class="col-7 text-end px-1">{{ date('d-m-Y', strtotime($row->deadline_at))}}</div>
                            <div class="col-5 text-end px-1">{{date('d-m-Y', strtotime($row->delivery_at))}}</div>
                        </div>
                    </div>

                    <div class="col col-1 text-end">
                        <span class="badge badge-0 font-sm">Bestanden geüpload</span>
                    </div>

                    <div class="col col-2 col-controls">

                        <div class="row row-controls">

                            <div class="col text-end">{{ date('d-m-Y', strtotime($row->created_at)) }}</div>

                            <div class="col-7">
                                
                                <div class="row">

                                    <div class="col px-1 text-right">{!! $btnPDFProduction !!}</div>
                                    <div class="col px-1 text-right">{!! $btnPDFPackingSlip !!}</div>
                                    <div class="col px-1 text-right">{!! $btnView !!}</div>
                                    <div class="col px-1 text-right">{!! $btnSync !!}</div>
                                    <div class="col px-1 text-right">{!! $btnModify !!}</div>
                                    <div class="col px-1 text-right">{!! $btnDelete !!}</div>

                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                
                @endforeach
            </div>

        </div>
        
    </div>
</div>
@endsection