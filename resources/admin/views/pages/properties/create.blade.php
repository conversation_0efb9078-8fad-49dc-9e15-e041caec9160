@extends('admin::layouts.app')

@section('title', true)

@section('flags')
    <x-admin::icon icon="lock" class="flag flag-is-locked" :if="$data->exists && $data->is_locked" />
@endsection

@section('controls')
    <x-admin.button button="back" />
    <x-admin.button button="module" :route="route('admin.propertiesvalues.index', $data->id ?? 0)" label="Values" :if="$data->exists" />
    <x-admin.button button="destroy" data-name="{{$data->systemlabel}}" :if="$data->exists && !$data->is_locked" />
    <x-admin.button button="store" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.properties.index')}}">Properties</a></li>
    <li>{{ !$data->exists ? 'Create' : $data->systemlabel }}</li>
@endsection

@section('form')
    <x-admin.form
        :action="route('admin.properties.store')"
        method="POST"
        :bind="$data"
        x-data="{
            system_context: '{{ old('system_context', $data->system_context) }}',
            system_context_module: '{{ old('system_context_module', $data->system_context_module) }}',
            system_context_unit_id: '{{ old('system_context_unit_id', $data->system_context_unit_id) }}',
            system_context_unit_compositional_id: '{{ old('system_context_unit_compositional_id', $data->system_context_unit_compositional_id) }}',
            system_context_unit_compositional_unit_id: '{{ old('system_context_unit_compositional_unit_id', $data->system_context_unit_compositional_unit_id) }}',
            is_global: '{{ old('is_global', $data->is_global) }}'
        }"
    />
    <x-admin.form.input type="hidden" name="id" value="{{ $data->id }}" />
@endsection 

@section('content')

    <x-admin.page.row>
        <x-admin.page.col col="6">
           
            <x-admin.panel title="General information">

                <x-admin.form.row>
                    <x-admin.form.label for="systemname" label="System name"  />
                    <x-admin.form.input name="systemname"  />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="system_context" label="Context"  />
                    <x-admin.form.select 
                        name="system_context" 
                        :options="\App\Enums\SystemContext::class"
                        
                        x-model="system_context"
                    />
                </x-admin.form.row>

                <x-admin.form.row x-show="system_context == 'module'">

                    <x-admin.form.label for="system_context_module" label="Module"  />
                    <x-admin.form.select 
                        name="system_context_module" 
                        x-model="system_context_module"
                        :options="collect([
                            [   'id' => 'products',     'systemname' => 'Products'      ],
                            [   'id' => 'categories',   'systemname' => 'Categories'    ],
                        ])"
                    />
                </x-admin.form.row>
                    
                <x-admin.form.row x-show="system_context == 'unit'">

                    <x-admin.form.label for="system_context_unit_id" label="Unit"  />
                    <x-admin.form.select 
                        name="system_context_unit_id" 
                        :options="$units"
                        x-model="system_context_unit_id"
                    />

                    <x-admin.form.select 
                        name="system_context_unit_compositional_id" 
                        :options="$unitCompositionals"
                        x-model="system_context_unit_compositional_id"
                    />

                    <x-admin.form.select
                        name="system_context_unit_compositional_unit_id"
                        :options="$units"
                    />

                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="sorting" label="Sorting"  />
                    <x-admin.form.select 
                        name="sorting" 
                        :options="\App\Enums\Property\Sorting::class"
                        :default="\App\Enums\Property\Sorting::DEFAULT->value"
                    />
                </x-admin.form.row>

                <x-admin.form.row x-show="system_context == 'module'">
                    <x-admin.form.label for="is_filter" label="Is filter" />
                    <x-admin.form.toggle name="is_filter" />
                </x-admin.form.row>

                <x-admin.form.row x-show="system_context == 'module'">
                    <x-admin.form.label for="is_sortable" label="Is sortable" />
                    <x-admin.form.toggle name="is_sortable" />
                </x-admin.form.row>

                <x-admin.form.row x-show="system_context == 'module'">
                    <x-admin.form.label for="is_multiple" label="Multiple values" />
                    <x-admin.form.toggle name="is_multiple" />
                </x-admin.form.row>
                
                <x-admin.form.row x-show="system_context == 'module'">
                    <x-admin.form.label for="is_global" label="Globally available" />
                    <x-admin.form.toggle name="is_global" x-model="is_global" />
                </x-admin.form.row>

                <x-admin.form.row x-show="system_context == 'module' && system_context_module == 'products' && is_global == 0">
                    <x-admin.form.label for="categories" label="Categories" />
                    <x-admin.form.select 
                        name="categories" 
                        :options="$categories"
                        multiple="true"
                    />
                </x-admin.form.row>

            </x-admin.panel>
        
        </x-admin.page.col>
        <x-admin.page.col col="6">

            <x-admin.panel type="locales" title="Language specific">

                @foreach (config('locales') as $locale => $localeData)

                    <x-admin.form.row :locale="$locale">
                        <x-admin.form.label label="Name" for="name" />
                        <x-admin.form.input name="name" />
                    </x-admin.form.row>

                    <x-admin.form.row :locale="$locale">
                        <x-admin.form.label label="Content" for="content" />
                        <x-admin.form.textarea textEditor="default" name="content" />
                    </x-admin.form.row>

                @endforeach

            </x-admin.panel>

        </x-admin.page.col>
    </x-admin.page.row>

@endsection 