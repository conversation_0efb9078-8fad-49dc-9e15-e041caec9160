@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button button="store" />
@endsection

@section('breadcrumbs')
    <li>Config</li>
@endsection

@section('form')
    <x-admin.form
        :action="route('admin.config.store')"
        :bind="$data"
        method="POST"
    />
@endsection

@section('content')

    <x-admin.panel type="locales">

        @slot('title')
            <ul class="nav nav-tabs" role="tablist">
                @foreach ($groupSortOrder as $group)

                    @if(!array_key_exists($group, $data))
                        @continue
                    @endif

                    <li class="nav-item" role="presentation">
                        <button 
                            class="nav-link{{ $group == 'module' ? ' active' : '' }}" 
                            id="group-{{ $group }}-tab" 
                            data-bs-toggle="tab" 
                            data-bs-target="#group-{{ $group }}-content" 
                            type="button" 
                            role="tab">{{ ucwords($group) }}</button>
                    </li>

                @endforeach
            </ul>
        @endslot

        {{-- @slot('controls')
            <div class="search me-5">
                <x-admin.form.input name="search" placeholder="Search" />
            </div>
        @endslot --}}

        <div class="tab-content">
            @foreach ($data as $group => $groupItems)
                <div 
                    id="group-{{ $group }}-content"
                    class="tab-pane fade{{ $group == 'module' ? ' show active' : '' }}"  
                    role="tabpanel" tabindex="{{ $loop->index }}">

                    <div class="row">

                        <div class="col-2">

                            <ul class="list-group list-group-flush">

                                @foreach ( $groupItems as $namespace => $item)
                                    <li 
                                        class="list-group-item{{ $loop->first ? ' active' : '' }}" 
                                        data-bs-toggle="tab" 
                                        data-bs-target="#group-{{ $group }}-content-{{ $namespace }}" 
                                    >{{ $namespace }}</li>
                                @endforeach

                            </ul>

                        </div>
                        <div class="col-10">

                            <div class="namespace-content">

                                @foreach ($groupItems as $namespace => $items)
                                    <div 
                                        id="group-{{ $group }}-content-{{ $namespace }}"
                                        class="tab-pane{{ $loop->first ? ' active' : ' fade' }}"  
                                        role="tabpanel">
                                        <x-admin.module.config.collection :list="$items" />
                                    </div>
                                @endforeach

                            </div>
                        
                        </div>
                    </div>

                </div>
            @endforeach
        </div>
        
    </x-admin.panel>

@endsection