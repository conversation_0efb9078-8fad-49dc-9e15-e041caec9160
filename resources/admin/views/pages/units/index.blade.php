@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button button="module" :route="route('admin.unitsgroups.index')" label="Groups" />
    <x-admin.button button="module" :route="route('admin.unitstypes.index')" label="Types" />
    <x-admin.button button="create" />
@endsection

@section('breadcrumbs')
    <li>Units</li>
@endsection

@section('filters', true)

@section('content')

    <x-admin.panel type="overview" title="Overview">

        <div class="overview">

            <div class="overview-header">
                <div class="row row-item">
                    <div class="col"><x-admin.link.sortable label="Systemname" attribute="systemname" /></div>
                    <div class="col-2"><x-admin.link.sortable label="Type" attribute="type.systemname" /></div>
                    <div class="col-2 text-end">Value</div>
                    <div class="col-2 text-end">Is base unit</div>
                    <div class="col-2 text-end"><x-admin.link.sortable label="Groups" attribute="groups_count" /></div>
                    <div class="col-1 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">

                @foreach ($data as $row)
                    <div class="row row-item">
                        <div class="col fw-bold"><a href="{{ route('admin.units.edit', $row->id) }}">{{$row->systemname}}</a></div>
                        <div class="col-2"><a href="{{ route('admin.unitstypes.edit', $row->type->id) }}">{{$row->type->systemname}}</a></div>
                        <div class="col-2 text-end">{{$row->value}}</div>
                        <div class="col-2 text-end"><x-admin::icon icon="check" :if="$row->id === $row->type->unit_base_id" /></div>
                        <div class="col-2 text-end"><a href="{{ route('admin.unitsgroups.filter', ['unit_group_id_filter' => $row->groups->pluck('id')->toArray() ]) }}">{{ $row->groups_count }}</a></div>
                        <div class="col-1 controls">
                            <x-admin.button group="overview" button="edit" :parameters="$row->id" />
                            <x-admin.button group="overview" button="destroy" :parameters="$row->id" data-name="{{$row->systemlabel}}" :disabled="$row->is_locked" />
                        </div>
                    </div>
                @endforeach

            </div>
        
            <div class="overview-empty alert alert-info mt-3">There are no units yet.</div>
            
        </div>

    </x-admin.panel>

@endsection