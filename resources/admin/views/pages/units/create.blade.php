@extends('admin::layouts.app')

@section('title', true)

@section('flags')
    <x-admin::icon icon="lock" class="flag flag-is-locked" :if="$data->exists && $data->is_locked" />
@endsection

@section('controls')
    <x-admin.button button="back" />
    <x-admin.button button="destroy" data-name="{{$data->systemlabel}}" :if="$data->exists && !$data->is_locked" />
    <x-admin.button button="store" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.units.index')}}">Units</a></li>
    <li>{{ !$data->exists ? 'Create' : $data->systemlabel }}</li>
@endsection

@section('form')

    <x-admin.form
        :action="route('admin.units.store')"
        method="POST"
        :bind="$data"
        x-data="{ 
            mode: '{{ $unitTypes?->where('id', old('unit_type_id', $data?->unit_type_id))->first()?->mode }}'
        }"
    />
    <x-admin.form.input type="hidden" name="id" value="{{ $data->id }}" />

@endsection

@section('content')


    <x-admin.page.row>
        <x-admin.page.col col="6">

            <x-admin.panel title="General">
        
                <x-admin.form.row>
                    <x-admin.form.label for="systemname" label="Systemname" required="true" />
                    <x-admin.form.input name="systemname" required="true" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="unit_type_id" label="Type" required="true" />
                    <x-admin.form.select name="unit_type_id" :options="$unitTypes" required="true" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="value" label="Value" required="true" />
                    <x-admin.form.input name="value" type="float" required="true" />
                </x-admin.form.row>

                <x-admin.form.row 
                    class="conversion-to-base" 
                    x-bind:style="mode === '{{ \App\Enums\UnitTypeMode::CONVERSABLE }}' ? '' : 'display: none;'"
                >
                    <x-admin.form.label 
                        for="conversion_to_base" 
                        label="Conversion to base unit" 
                        x-bind:required="mode === '{{ \App\Enums\UnitTypeMode::CONVERSABLE }}'" 
                    />
                    <x-admin.form.input 
                        name="conversion_to_base" 
                        x-bind:required="mode === '{{ \App\Enums\UnitTypeMode::CONVERSABLE }}'" 
                    />
                </x-admin.form.row>

            </x-admin.panel>

        </x-admin.page.col>
        <x-admin.page.col col="6">

            <x-admin.panel type="locales" title="Language specific">

                @foreach (config('locales') as $locale => $localeData)

                    <x-admin.form.row :locale="$locale">
                        <x-admin.form.label label="Description" for="name" />
                        <x-admin.form.input name="name" />
                    </x-admin.form.row>

                    <x-admin.form.row :locale="$locale">
                        <x-admin.form.label label="Abbreviation" for="abbreviation" />
                        <x-admin.form.input name="abbreviation" />
                    </x-admin.form.row>

                @endforeach

            </x-admin.panel>

        </x-admin.page.col>
    </x-admin.page.row>

    <x-admin.panel 
        type="overview" 
        class="panel-compositionals" 
        title="Compositional attributes"
        x-bind:style="mode === '{{ \App\Enums\UnitTypeMode::COMPOSITIONAL }}' ? '' : 'display: none;'"
    >
    
        @slot('controls')
            <x-admin.button 
                type="button" 
                class="btn-attribute-add"
                icon="plus"
                label="Add attribute"
            />
        @endslot

        <div class="overview overview-attributes">

            <div class="overview-header">
                <div class="row row-item">
                    <div class="col">Systemname</div>
                    <div class="col-4">Attribute</div>
                    <div class="col-1 controls">Controls</div>
                </div>
            </div>

            <div class="overview-body">
                
                @template('attribute-row', false)
                    <div 
                        class="row row-item row-attribute"
                        x-data="{
                            id: '[[id]]',
                            systemname: '[[systemname]]',
                            attribute: '[[attribute]]',
                            is_locked: '[[is_locked]]',
                        }"
                    >
                        <x-admin.form.input 
                            type="hidden" 
                            class="id"
                            name="compositionals[[[id]]][id]" 
                            x-model="id"
                        />

                        <div class="col">
                            <x-admin.form.input 
                                name="compositionals[[[id]]][systemname]"
                                x-model="systemname"
                            />
                        </div>

                        <div class="col-4">
                            <x-admin.form.select 
                                :options="\App\Enums\UnitCompositionalAttribute::class" 
                                name="compositionals[[[id]]][attribute]" 
                                x-model="attribute"
                                x-bind:readonly="is_locked == 1"
                            />
                        </div>
                        <div class="col-1 controls">

                            <x-admin.button 
                                group="overview" 
                                button="destroy" 
                                class="btn-attribute-delete"
                                data-id="[[id]]"
                                data-name="[[systemname]]" 
                                data-confirm="btn-attribute-delete-confirm" 
                                x-bind:class="is_locked == 1 ? ' btn-control-disabled' : ''"
                                x-bind:disabled="is_locked == 1"
                            />

                        </div>
                    </div>
                @endtemplate

                @foreach ((old('compositionals', null) ?? $data?->compositionals?->toArray()) as $compositional)   
                
                    @usetemplate('attribute-row', [
                        'id' => $compositional['id'] ?? null,
                        'systemname' => $compositional['systemname'] ?? null,
                        'attribute' => $compositional['attribute'] ?? null,
                        'is_locked' => ($compositional['is_locked'] ?? false) ? 1 : 0,
                    ])
                @endforeach

            </div>

            <div class="overview-empty alert alert-info mt-3">There are no compositional attributes yet.</div>

        </div>

    </x-admin.panel>

@endsection 

