@extends('admin::layouts.app')
@section('content')

<nav class="content-header">
    <div class="breadcrumbs">
        <ol>
            <li>Properties</li>
        </ol>
    </div>

    <div class="controls">
        
        <x-admin.button type="create" />
    </div>
</nav>

<div class="content-body">

    <h1 class="title">Overview</h1>
    
    <x-admin.alert/>

    <div class="card card-overview">

        <div class="card-header">
            <div>
                <h2>Properties</h2>
            </div>
        </div>

        <div class="card-body ">

            <div class="overview-header">

                <div class="row row-item">
                    <div class="col">Name</div>

                    <div class="col-1 text-center">Status</div>

                    <div class="col-1 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">

                @foreach ($data as App::get('module'))

                    <div class="row row-item">

                        <div class="col">
                            {{$property->systemname}}
                        </div>

                        <div class="col-1 text-center">
                            {{$property->status == 1 ? 'Yes' : 'No'}}
                        </div>

                        <div class="col-1 controls">
                            <a href="{{ route('admin.'.App::get('module').'.edit', $property->id) }}">
                                <i class="fa-regular fa-pen-to-square"></i>
                            </a>
                            <a href="{{ route(App::get('module').'.destroy', $property->id) }}">
                                <i class="fa-solid fa-trash"></i>
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>

        </div>
        
    </div>
</div>
@endsection