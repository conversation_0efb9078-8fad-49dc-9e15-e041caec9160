@extends('admin::layouts.app')
@section('content')

<form action="{{ route('admin.'.App::get('module').'.store') }}" method="POST" enctype="multipart/form-data">      
<input type="hidden" name="id" value="{{ $data->id }}">
@csrf

<nav class="content-header">

    <div class="breadcrumbs">
        <ol>
            <li><a href="{{route('admin.'.App::get('module').'.index')}}">default</a></li>
            <li>{{ empty($data->id) ? 'Add' : $data->systemname }}</li>
        </ol>
    </div>

    <div class="controls">

        <x-admin.button type="back" />

        <x-admin.button type="save" />

    </div>
</nav>

<div class="content-body">

    <h1 class="title">{{ empty($data->id) ? 'Create default' : 'Edit ' . $data->name_nl }}</h1>

    <x-admin.alert/>

    <div class="card card-form">
        <div class="card-header">
            <div>
                <h2>General information</h2>
            </div>
        </div>
        <div class="card-body">

            <div class="row-group">
                <x-admin.form.row.systemname :any="$data" />
                <x-admin.form.row.toggle :any="$data" column="status" />
            </div>

                        
            <h3>Language specific</h3>

            <x-admin.locales/>

            <div class="row-block">
                <label class="label" for="name_nl">Name</label>
                <div class="input">
                    <input type="text" name="name_nl" id="name_nl" class="form-control" value="{{ old('name_nl', $data) }}">
                </div>
            </div>
        </div>
    </div>

</div>

</form>

@endsection