@extends('admin::layouts.app')
@section('content')

<x-admin.form :any="$data">

<nav class="content-header">

    <x-admin.breadcrumbs>
        {{ empty($data->id) ? 'Add' : $data->systemname }}
    </x-admin.breadcrumbs>

    <div class="controls">
        <x-admin.button type="back" />
        <x-admin.button type="save" />
    </div>
</nav>

<div class="content-body">

    <h1 class="title">{{ empty($data->id) ? 'Create Product' : 'Edit ' . $data->systemname }}</h1>

    <x-admin.alert/>

    <x-admin.panel title="General information">

        <x-admin.form.row.systemname :any="$data" />

        <x-admin.form.row.toggle :any="$data" column="is_active" label="Is active" />
        <x-admin.form.row.toggle :any="$data" column="in_webshop" label="In webshop" />
        <x-admin.form.row.toggle :any="$data" column="is_highlight" label="Is highlighted" />

        <x-admin.form.row.toggle :any="$data" column="is_external" label="Is external" />
        <x-admin.form.row.toggle :any="$data" column="is_printable" label="Is printable" toggle-reverse=".row-is_convection" toggle=".row-configurator-id" />
        <x-admin.form.row.toggle :any="$data" column="is_convection" label="Is convection" toggle-reverse=".row-is_printable,#type option[value={{App\Enums\Product\Type::CONFIGURATION}}]" />

        <x-admin.form.row.selectbox :any="$data" :items="App\Enums\Product\Type::class" column="type" label="Type" required="true" />
        <x-admin.form.row.selectbox :any="$data" :items="App\Enums\Product\TypeSub::class" column="type_sub" label="Type sub" required="true" />

        <x-admin.form.row name="configurator-id" label="Configurator template" required="true">
            <select id="configurator_id" name="configurator_id" class="form-control form-control-sm selectpicker px-0" multiple data-max-options="1">
                <optgroup label="Substrates">
                    <option data-type="fabrics" value="fabrics.substrate" {{ old('configurator_id', $data) == 'fabrics.substrate' ? ' selected' : '' }}>Fabrics</option>
                    <option data-type="sticker" value="sticker.substrate" {{ old('configurator_id', $data) == 'sticker.substrate' ? ' selected' : '' }}>Stickers</option>
                    <option data-type="panel" value="panel.substrate" {{ old('configurator_id', $data) == 'panel.substrate' ? ' selected' : '' }}>Panelen</option>
                </optgroup>
                <optgroup label="Configurators">
                    <option data-type="0" value="tendon" {{ old('configurator_id', $data) == 'tendon' ? 'selected' : '' }}>Peesdoeken</option>
                    <option data-type="0" value="tendon_door" {{ old('configurator_id', $data) == 'tendon_door' ? 'selected' : '' }}>Peesdoeken + deur</option>
                    <option data-type="0" value="sticker" {{ old('configurator_id', $data) == 'sticker' ? 'selected' : '' }}>Stickers</option>
                    <option data-type="0" value="panel" {{ old('configurator_id', $data) == 'panel' ? 'selected' : '' }}>Panelen</option>
                    <option data-type="0" value="ledgo" {{ old('configurator_id', $data) == 'ledgo' ? 'selected' : '' }}>LedGo</option>
                    <option data-type="0" value="flags" {{ old('configurator_id', $data) == 'flags' ? 'selected' : '' }}>Vlaggen</option>
                    <option data-type="0" value="frames" {{ old('configurator_id', $data) == 'frames' ? 'selected' : '' }}>Frames</option>
                    <option data-type="0" value="beachwing" {{ old('configurator_id', $data) == 'beachwing' ? 'selected' : '' }}>Beachwings</option>
                    <option data-type="0" value="rollup" {{ old('configurator_id', $data) == 'rollup' ? 'selected' : '' }}>RollUps</option>
                </optgroup>
            </select>
        </x-admin.form.row>

        <x-admin.form.row name="categories" label="Categories" required="true">
            <select name="categories[]" id="categories" class="form-control form-control-sm selectpicker px-0" multiple>
                @foreach ($categories as $category)
                    {!! $category !!}
                @endforeach
            </select>
        </x-admin.form.row>

        <x-admin.form.row name="category-id" label="Main Category">
            <select name="category_main" id="category_main" class="form-control form-control-sm selectpicker px-0">
                <option value="">Nothing selected yet</option>
            </select>
            <input type="hidden" name="category_id" value="{{ old('category_id', $data) }}" />
        </x-admin.form.row>

    </x-admin.panel>


    {{-- 
        <div class="row-block">
            <label class="label" for="image">Image</label>
            <div class="input">
                <input type="file" name="image" id="image" class="form-control">
            </div>
        </div> 

        <x-admin.form.card.product.pricing.substrate :any="$data"/>

        <x-admin.form.card.product.pricing.convections :any="$data"/>

        <x-admin.form.card.product.convections :any="$data"/>

        <x-admin.form.card.product.products :any="$data"/>
    --}}

    <x-admin.panel type="locales" :any="$data">

        @verbatim
            <x-admin.form.row.toggle :$any :$locale column="is_active" label="Is active" />
            <x-admin.form.row.toggle :$any :$locale column="is_indexable" label="Is indexable" />

            <x-admin.form.row.input-text :$any :$locale column="title" label="Title"  />
            <x-admin.form.row.input-text :$any :$locale column="title_sub" label="Subtitle" />
            <x-admin.form.row.input-text :$any :$locale column="title_custom" label="Title configurator" />
            <x-admin.form.row.input-text :$any :$locale column="title_tab" label="Document title" />
        
            <x-admin.form.row.texteditor :$any :$locale column="content_intro" label="Content intro" type="plaintext" />
            <x-admin.form.row.texteditor :$any :$locale column="content_top" label="Content Top" />
            <x-admin.form.row.texteditor :$any :$locale column="content" label="Content" />
            <x-admin.form.row.texteditor :$any :$locale column="content_bottom" label="Content Bottom" />

            <x-admin.form.row.meta-description :$any :$locale />
            <x-admin.form.row.meta-keywords :$any :$locale />
         @endverbatim

    </x-admin.panel>


    <x-admin.panel title="Properties">
        
        @foreach ($properties as $property)
            <div 
                class="property{{$property->is_global ? ' property-global' : ' collapse collapsed'}}" 
                data-id="{{ $property->id }}">

                <div class="row-block">
                    <label class="label" for="property_{{ $property->id }}">{{ $property->systemname }}</label>
                    <div class="input">
                        <select 
                            id="property_{{ $property->id }}" 
                            name="properties[{{ $property->id }}][]" 
                            class="form-control form-control-sm selectpicker"
                            multiple 
                            @if (!$property->is_multiple)
                                multiple data-max-options="1"
                            @endif
                            >

                            @foreach ($property->values as $propertyValue)

                                @php( $currentValue = in_array($propertyValue->id, $selectedPropertyValues) ? $propertyValue->id : 0)

                                @php($selected = in_array($propertyValue->id, old('properties.'.$property->id, [ $currentValue ]))
                                    ? ' selected'
                                    : '')

                                <option value="{{ $propertyValue->id }}"{{$selected}}>
                                    {{ $propertyValue->systemname }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

            </div>
        @endforeach

        <p class="no-properties alert alert-warning font-italic collapse collapsed">
            Are are no properties linked to any of the selected categories.<br>
            There are also no globally available properties at this moment.
        </p>

    </x-admin.panel>



    {{-- <x-admin.widget.attachments :module="App::get('module')" :any="$data" /> --}}
</div>

</x-admin.form>

@endsection 