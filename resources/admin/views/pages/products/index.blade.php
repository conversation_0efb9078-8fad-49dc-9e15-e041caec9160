@extends('admin::layouts.app')
@section('content')

<nav class="content-header">
    <div class="breadcrumbs">
        <ol>
            <li>Products</li>
        </ol>
    </div>

    <div class="controls">
        <x-admin.button type="create" />
    </div>
</nav>

<div class="content-body">

    <h1 class="title">Overview</h1>
    
    <x-admin.alert/>

    <x-lacodix-filter::model-filters :model="App\Models\Product::class" method="post" :action="route('admin.'.App::get('module').'.filter')" />

    <div class="card card-overview" data-type="{{App::get('module')}}">

        <div class="card-header">
            <div>
                <h2>Products</h2>
            </div>
        </div>

        <div class="card-body ">

            <div class="overview-header">

                <div class="row row-item">
                    <div class="col-1">SKU</div>
                    <div class="col">Name</div>
                    <div class="col-1 text-center">Group</div>
                    <div class="col-1 text-center">Type</div>
                    <div class="col-1 text-center">Sub type</div>
                    <div class="col-1 text-center">Bestseller</div>
                    <div class="col-1 text-center">Status</div> 
                    <div class="col-1 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">

                @foreach ($data as $row)

                    <x-admin.module.ProductRow :row="$row" :count="$data->count()" />
                        
                @endforeach
            </div>

        </div>
        
    </div>
    
</div>
@endsection