@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button button="module" :route="route('admin.items.index')" label="Items" />
    <x-admin.button button="create" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.itemgroups.index')}}">Item groups</a></li>
@endsection

@section('content')

    <x-admin.panel type="overview" title="Item Groups">

        <div class="overview">

            <div class="overview-header">
                <div class="row row-item">
                    <div class="col"><x-admin.link.sortable label="System name" attribute="systemname" /></div>
                    <div class="col-2 text-end"><x-admin.link.sortable label="Items" attribute="items_count" /></div>
                    <div class="col-2 text-end"><x-admin.link.sortable label="Products" attribute="products_count" /></div>
                    <div class="col-1 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">
                @foreach ($data as $row)

                    <div class="row row-item">
                        <div class="col fw-bold text-truncate"><a href="{{ route('admin.itemgroups.edit', $row->id) }}">{{ $row->systemname }}</a></div>
                        <div class="col-2 text-end"><a href="{{ route('admin.items.filter', ['item_group_filter' => [ $row->id ]]) }}">{{ $row->items_count }}</a></div>
                        <div class="col-2 text-end"><a href="{{ route('admin.products.filter', ['item_group_filter' => [ $row->id ]]) }}">{{ $row->products_count }}</a></div>     
                        <div class="col-1 controls">
                            <x-admin.button group="overview" button="edit" :parameters="$row->id" />
                            <x-admin.button group="overview" button="destroy" :parameters="$row->id" data-name="{{$row->systemlabel}}" :disabled="$row->is_locked" />
                        </div>
                    </div>

                @endforeach
            </div>

            <div class="overview-empty alert alert-info mt-3">There are no item groups yet.</div>
        </div>
        
    </x-admin.panel>

@endsection