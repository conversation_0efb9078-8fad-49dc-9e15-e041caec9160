@extends('admin::layouts.app')

@section('title', true)

@section('flags')
    <x-admin::icon icon="lock" class="flag flag-is-locked" :if="$data->exists && $data->is_locked" />
@endsection

@section('controls')
    <x-admin.button button="back" />
    <x-admin.button button="destroy" data-name="{{$data->systemlabel}}" :if="$data->exists && !$data->is_locked" />
    <x-admin.button button="store" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.itemgroups.index')}}">Item Groups</a></li>
    <li>{{ !$data->exists ? 'Create' : $data->systemlabel }}</li>
@endsection

@section('form')
    <x-admin.form
        :action="route('admin.itemgroups.store')"
        method="POST"
        :bind="$data"
    />
    <x-admin.form.input type="hidden" name="id" value="{{ $data->id }}" />
@endsection

@section('content')

    <x-admin.panel title="General information">
        <x-admin.form.row>
            <x-admin.form.label for="systemname" label="System name" required="true" />
            <x-admin.form.input name="systemname" required="true" />
        </x-admin.form.row>
    </x-admin.panel>


    <x-admin.blocks.ledger-accounts-panel :accounts="$data->ledgerAccounts" />

@endsection 