@extends('admin::layouts.app')
@section('content')

<nav class="content-header">
    <div class="breadcrumbs">
        <ol>
            <li>Menus</li>
        </ol>
    </div>

    <div class="controls">
    </div>
</nav>

<div class="content-body">

    <h1 class="title">Overview</h1>
    
    <x-admin.alert/>
    
    @foreach ( config('modules.menus') as $key => $menu)

        <div class="card card-overview mb-4 card-sortable" data-type="{{App::get('module')}}">
            
            <div class="card-header">
                <div>
                    <h2>{{$menu['systemname']}}</h2>
                </div>
            </div>

            <div class="card-body" >

                <div class="overview-header">

                    <div class="row row-item">

                        <div class="col">Title</div>

                        <div class="col-2 text-center">Type</div>
                        
                        <div class="col-2 text-center">Status</div>

                        <div class="col-1 controls">Options</div>
                    </div>
                </div>

                <div class="overview-body" data-sort="{{$key}}" >

                    @foreach ($data->where('systemname' , $key )->sortBy([
                        ['sort', 'asc']
                    ]) as $row)

                        <div class="row row-item" data-id="{{$row->id}}" data-type="{{$row->menuable_type}}">

                            <div class="col fw-bold">
                                <i class="fas fa-bars me-3 sorting-handle ui-sortable-handle"></i>
                                <a href="{{ route('admin.'.strtolower(Str::plural($row->menuable_type)).'.edit', $row->menuable_id) }}">
                                    {{$row->menuable->systemname}}
                                </a>
                            </div>

                            <div class="col-2 text-center">{!! $row->menuable_type !!}</div>

                            <div class="col-2 text-center">{!! $row->menuable->getStatusDot() !!}</div>

                            <div class="col-1 controls">
                                
                                <a href="{{ route('admin.'.strtolower(Str::plural($row->menuable_type)).'.edit', $row->menuable_id) }}">
                                    <i class="fa-regular fa-pen-to-square"></i>
                                </a>

                            </div>
                        </div>

                    @endforeach
                </div>

            </div>
            
        </div>
    @endforeach
</div>
@endsection