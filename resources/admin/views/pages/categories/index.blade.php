@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button button="create" />
@endsection

@section('breadcrumbs')
    <li>Categories</li>
@endsection

@section('content')

    <x-admin.panel type="overview" title="Categories">

        <div class="overview-header">
            <div class="row row-item">
                <div class="col">System name</div>
                <div class="col-1 text-end">Products</div>
                <div class="col-1 text-center">Menu</div>
                <div class="col-1 text-center">Status</div>
                <div class="col-2 controls text-end">Actions</div>
            </div>
        </div>

        <div class="overview-body">
            @foreach ($data as $row)
                <x-admin.module.CategoryRow :row="$row" :count="$data->count()" />
            @endforeach
        </div>

    </x-admin.panel>

@endsection