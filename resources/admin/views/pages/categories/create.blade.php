@extends('admin::layouts.app')
@section('content')

<x-admin.form :any="$data">

<nav class="content-header">

    <div class="breadcrumbs">
        <ol>
            <li><a href="{{route('admin.categories.index')}}">Categories</a></li>
            <li>{{ empty($data->id) ? 'Add' : $data->systemlabel }}</li>
        </ol>
    </div>

    <div class="controls">

        <x-admin.button button="back" />
        <x-admin.button button="save" />

    </div>
</nav>

<div class="content-body">

    <h1 class="title">{{ empty($data->id) ? 'Create Category' : 'Edit ' . $data->systemlabel }}</h1>

    <x-admin.alert/>

    <div class="card card-form">
        <div class="card-header">
            <div>
                <h2>General information</h2>
            </div>
        </div>
        <div class="card-body">

            <div class="row-group">
                
                <x-admin.form.row.systemname :any="$data" />
                <x-admin.form.row.toggle :any="$data" column="is_active" label="Status" />
                
                {{-- <x-admin.form.row.menus :any="$data" /> --}}
                {{-- <x-admin.form.row.toggle :data="$data" column="has_card_image" /> --}}
                {{-- <x-admin.form.row.toggle :any="$data" column="content_top_has_images" /> --}}

                <div class="row-block">
                    <label class="label" for="parent_id">Parent category</label>

                    <div class="input">
                        <select name="parent_id" id="parent_id" class="form-control form-control-sm selectpicker px-0">
                            <option value="">Nothing selected</option>
                            @foreach ($categories as $category)
                                {!! $category !!}
                            @endforeach
                        </select>
                    </div>
                </div>

                <x-admin.form.row.toggle :any="$data" column="is_itemgroup" label="Is Itemgroup" toggle=".panel-ledger-accounts" />


            </div>
        </div>
    </div>

    <x-admin.panel title="General Ledger Accounts" name="panel-ledger-accounts" class="{{$data->is_itemgroup ? '' : ' collapse collapsed'}}">
        <div class="row row-cols-1 row-cols-xxl-2">
            {{-- <div class="col"><x-admin.form.row.ledger-account :any="$data" :accounts="$ledgerAccounts" column="turnover" label="Turnover account" /></div>
            <div class="col"><x-admin.form.row.ledger-account :any="$data" :accounts="$ledgerAccounts" column="stock" label="Stock account" /></div>
            <div class="col"><x-admin.form.row.ledger-account :any="$data" :accounts="$ledgerAccounts" column="cost" label="Costs account" /></div>
            <div class="col"><x-admin.form.row.ledger-account :any="$data" :accounts="$ledgerAccounts" column="distribution" label="Distribution account" /></div>
            <div class="col"><x-admin.form.row.ledger-account :any="$data" :accounts="$ledgerAccounts" column="discount" label="Discount account" />            </div>
            <div class="col"><x-admin.form.row.ledger-account :any="$data" :accounts="$ledgerAccounts" column="dealer_discount" label="Supplier discount account" /></div> --}}
        </div>
    </x-admin-panel>

    <x-admin.panel type="locales" :any="$data">

        @verbatim
            <x-admin.form.row.toggle :$any :$locale column="is_active" label="Is active" />
            <x-admin.form.row.toggle :$any :$locale column="is_indexable" label="Is indexable" />

            <x-admin.form.row.input-text :$any :$locale column="title" label="Title"  />
            <x-admin.form.row.input-text :$any :$locale column="title_sub" label="Subtitle" />
            <x-admin.form.row.input-text :$any :$locale column="title_custom" label="Title configurator" />
            <x-admin.form.row.input-text :$any :$locale column="title_tab" label="Document title" />
        
            <x-admin.form.row.texteditor :$any :$locale column="content_intro" label="Content intro" type="plaintext" />
            <x-admin.form.row.texteditor :$any :$locale column="content_top" label="Content Top" />
            <x-admin.form.row.texteditor :$any :$locale column="content" label="Content" />
            <x-admin.form.row.texteditor :$any :$locale column="content_bottom" label="Content Bottom" />

            <x-admin.form.row.meta-description :$any :$locale />
            <x-admin.form.row.meta-keywords :$any :$locale />
         @endverbatim

    </x-admin.panel>

</div>

</x-admin.form>

@endsection