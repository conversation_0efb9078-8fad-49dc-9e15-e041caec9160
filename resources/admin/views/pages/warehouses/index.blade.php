@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button button="module" :route="route('admin.warehouseslocations.index')" label="Locations" />
    <x-admin.button button="module" :route="route('admin.warehousesstock.index')" label="Stock" />
    <x-admin.button button="create" />
@endsection

@section('breadcrumbs')
    <li>Warehouses</li>
@endsection

@section('filters', true)

@section('content')

    <x-admin.panel type="overview" title="Warehouses">

        <div class="overview">
            <div class="overview-header">
                <div class="row row-item">
                    <div class="col"><x-admin.link.sortable label="Systemname" attribute="systemname" /></div>
                    <div class="col-2"><x-admin.link.sortable label="Building" attribute="building_id" /></div>
                    <div class="col-1 text-end"><x-admin.link.sortable label="Locations" attribute="locations_count" /></div>
                    <div class="col-1 text-end"><x-admin.link.sortable label="Stock" attribute="stock_count" /></div>
                    <div class="col-2 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">
                @foreach ($data as $row)
                    <div class="row row-item">
                        <div class="col fw-bold text-truncate"><a href="{{ route('admin.warehouses.edit', $row->id) }}">{{ $row->systemname }}</a></div>
                        <div class="col-2">{{$row->building?->systemname}}</div>
                        <div class="col-1 text-end"><a href="{{ route('admin.warehouseslocations.filter', ['warehouse_filter' => $row->id]) }}">{{$row->locations_count}}</a></div>
                        <div class="col-1 text-end"><a href="{{ route('admin.warehousesstock.filter', ['warehouse_filter' => $row->id]) }}">{{$row->stock_count}}</a></div>
                        <div class="col-2 controls">
                            <x-admin.button group="overview" button="edit" :parameters="$row->id" />
                            <x-admin.button group="overview" button="destroy" :parameters="$row->id" data-name="{{$row->systemname}}" :disabled="$row->is_locked" />
                        </div>                  
                    </div>
                @endforeach
            </div>

            <div class="overview-empty alert alert-info mt-3">There are no warehouses yet.</div>
        </div>

    </x-admin.panel>

@endsection