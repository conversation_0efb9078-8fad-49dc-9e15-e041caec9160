@extends('admin::layouts.app')

@section('title', true)

@section('flags')
    <x-admin::icon icon="lock" class="flag flag-is-locked" :if="$data->exists && $data->is_locked" />
@endsection

@section('controls')
    <x-admin.button button="back" />
    {{-- <x-admin.button button="destroy" data-name="{{$data->systemlabel}}" :if="$data->exists && !$data->is_locked" /> --}}
    <x-admin.button button="store" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.warehouses.index')}}">Warehouses</a></li>
    <li>{{ !$data->exists ? 'Create' : $data->systemlabel }}</li>
@endsection

@section('form')
    <x-admin.form
        :bind="$data" 
        class="form-warehouses" 
        action="{{route('admin.warehouses.store')}}"
    />
    <x-admin.form.input type="hidden" name="id" />
@endsection

@section('content')

    <x-admin.panel title="General information">

        <x-admin.form.row>
            <x-admin.form.label for="systemname" label="Systemname" required="true" />
            <x-admin.form.input name="systemname" required="true" />
        </x-admin.form.row>
    
        <x-admin.form.row>
            <x-admin.form.label for="building_id" label="Building" required="true" />
            <x-admin.form.select 
                name="building_id" 
                required="true"
                :options="$buildings"
                :singleOptionSelected="true"
            />
        </x-admin.form.row>

    </x-admin.panel>

@endsection 