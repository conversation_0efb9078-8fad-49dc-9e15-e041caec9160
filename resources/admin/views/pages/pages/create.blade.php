@extends('admin::layouts.app')
@section('content')

<x-admin.form :any="$data">

<nav class="content-header">

    <div class="breadcrumbs">
        <ol>
            <li><a href="{{route('admin.'.App::get('module').'.index')}}">{{ucfirst(App::get('module'))}}</a></li>
            <li>{{ empty($data->id) ? 'Add' : $data->systemname }}</li>
        </ol>
    </div>

    <div class="controls">
        <x-admin.button type="back" />
        <x-admin.button type="save" />
    </div>
</nav>

<div class="content-body">

    <h1 class="title">{{ empty($data->id) ? 'Create Page' : 'Edit ' . $data->name_nl }}</h1>

    <x-admin.alert/>

    <div class="card card-form">
        <div class="card-header">
            <div>
                <h2>General information</h2>
            </div>
        </div>
        <div class="card-body">

            <div class="row-group">

                <x-admin.form.row.systemname :any="$data" />
                <x-admin.form.row.toggle :any="$data" column="is_active" label="Is active" />

                {{-- <x-admin.form.row.menus :any="$data" :App::get('module')/> --}}

                <div class="row-block">
                    <label class="label" for="icon">Icon</label>
                    <div class="input">
                        <x-admin.widget.iconpicker :any="$data"/>
                    </div>
                </div>
                

                <div class="row-block">
                    <label class="label" for="parent_id">Parent page</label>
                    <div class="input">       
                        <select name="parent_id" id="parent_id" class="form-control form-control-sm selectpicker px-0">
                            <option value="">Nothing selected</option>
                            @foreach ($pages as $page)
                                {!! $page !!}
                            @endforeach
                        </select>
                    </div>
                </div>

                {{-- <x-admin.form.row.hero :any="$data" :$heroes type="header" />
                <x-admin.form.row.hero :any="$data" :$heroes type="footer" /> --}}

            </div>
        </div>
    </div>
    
    <x-admin.panel type="locales" :any="$data">

        @verbatim
            <x-admin.form.row.toggle :$any :$locale column="is_active" label="Is active" />
            <x-admin.form.row.toggle :$any :$locale column="is_indexable" label="Is indexable" />

            <x-admin.form.row.input-text :$any :$locale column="title" label="Title"  />
            <x-admin.form.row.input-text :$any :$locale column="title_sub" label="Subtitle" />
            <x-admin.form.row.input-text :$any :$locale column="title_custom" label="Title configurator" />
            <x-admin.form.row.input-text :$any :$locale column="title_tab" label="Document title" />
        
            <x-admin.form.row.texteditor :$any :$locale column="content_intro" label="Content intro" type="plaintext" />
            <x-admin.form.row.texteditor :$any :$locale column="content_top" label="Content Top" />
            <x-admin.form.row.texteditor :$any :$locale column="content" label="Content" />
            <x-admin.form.row.texteditor :$any :$locale column="content_bottom" label="Content Bottom" />

            <x-admin.form.row.meta-description :$any :$locale />
            <x-admin.form.row.meta-keywords :$any :$locale />
         @endverbatim

    </x-admin.panel>

    <x-admin.widget.attachments :any="$data" />


    {{-- <div class="card card-form">
        
    
        <div class="card-header">
            <h2>Images</h2>
        </div>
    
        <div class="card-body">
            <div class="row-block">
                <label class="label" for="image">Image</label>
                <div class="input">
                    <input type="file" name="image" id="image" class="form-control">
                </div>
            </div>
        </div>
    </div> --}}
    
    
    

</div>

</x-admin.form>

@endsection