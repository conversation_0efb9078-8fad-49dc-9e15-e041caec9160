@extends('admin::layouts.app')
@section('content')

<nav class="content-header">
    <div class="breadcrumbs">
        <ol>
            <li>{{ucfirst(App::get('module'))}}</li>
        </ol>
    </div>

    <div class="controls">
        <x-admin.button type="create" />
    </div>
</nav>

<div class="content-body">

    <h1 class="title">Overview</h1>


    <div class="card card-overview card-sortable"  data-type="{{App::get('module')}}" >

        <div class="card-header">
            <div>
                <h2>{{ucfirst(App::get('module'))}}</h2>
            </div>
        </div>

        <div class="card-body">

            <div class="overview-header">

                <div class="row row-item">
                    <div class="col">Title</div>
                    <div class="col col-1 text-center">Menu</div>
                    <div class="col col-1 text-center">Status</div>
                    <div class="col-2 controls text-end">Actions</div>
                </div>
            </div>

            <div class="overview-body">
                @foreach ($data as $row)
                
                    <x-admin.module.PageRow :row="$row" :App::get('module') :count="$data->count()" />
                @endforeach
            </div>

        </div>
        
    </div>
</div>
@endsection