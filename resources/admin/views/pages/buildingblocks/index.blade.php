@extends('admin::layouts.app')

@section('title', 'BuildingBlocks')

@section('controls')
    <x-admin.button type="create" />
    <x-admin.button type="save" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.buildingblocks.index')}}">Buildingblocks</a></li>
@endsection

@section('form')
    <x-admin.form :bind="$data" />
@endsection

@section('content')

    <x-admin.page.row>
        <x-admin.page.col width="30">
           
            <x-admin.panel title="Panel">
                
                <x-admin.form.row>
                    <x-admin.form.label label="First name" for="firstname" />
                    <x-admin.form.input name="firstname" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label label="Last name" for="lastname" />
                    <x-admin.form.input name="lastname" />
                </x-admin.form.row>

            </x-admin.panel>

        </x-admin.page.col>
        <x-admin.page.col width="30">
            <h4>Blade directives</h4>

            
            @template('a') 
                Name: @template('b')[[firstname=John]] [[lastname=Doe]]@endtemplate 
            @endtemplate

            @template('template')
                <div class="bg-danger">
                    @template('innerTemplate')
                        <div class="bg-success">[[replacement_name=replacement_var]]</div>
                    @endtemplate
                    <div class="bg-warning">Text text</div>
                </div>
            @endtemplate

            @template('otherZooi')
                Another template
            @endtemplate

            <div class="fw-bold mt-3">console.log(templates);</div>
        </x-admin.page.col>

    </x-admin.page.row>

    <x-admin.page.row>
        <x-admin.page.col>

            <x-admin.panel title="Panel">
                
                {{-- SELECTBOX 
                :optionsExtra="[ ['variant', 'Variant specific'] ]"
                :optionsExtra="\App\Enums\Gender::class"
                :optionsExtraPosition="\App\Enums\FormSelectOptionsPosition::class" 
                 :optionsExtra="[ ['variant', 'Variant specific'] ]"
                :optionsExtraPosition="'sort_title_desc'"
                --}}

                {{--  Future Coolness  --}}
                {{-- <x-admin.form.row element="input" name="lastname" label="Lastname" />
                
                <x-admin.form.row element="input" name="lastname" label="Lastname">
                    <x-admin.form.toggle name="is_active" label="Bonus elements" :default="false" />
                </x-admin.form.row>  --}}
                {{--  Future Coolness  --}}

                <x-admin.form.row>
                    <x-admin.form.label label="First name" for="firstname" data-value="test" />
                    <x-admin.form.input name="firstname" />
                    <x-admin.form.select name="gender" :options="\App\Enums\Gender::class" />
                    <x-admin.form.select name="gender" selected="male" :options="[
                        [ 'id' => 'other',   'systemname' => 'other' ], 
                        [ 'id' => 'male',    'systemname' => 'male' ]]"
                    />
                    <x-admin.form.toggle name="is_active" :default="false" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label label="Last name" for="lastname" />
                    <x-admin.form.col class="col-3"><x-admin.form.input name="lastname" /></x-admin.form.col>
                    <x-admin.form.col class="col"><p class="form-text">Inputs not auto sized</p></x-admin.form.col>
                </x-admin.form.row>

                <x-admin.form.row noAutoSize>
                    <div class="col-3"><x-admin.form.label label="Last name" for="lastname" /></div>
                    <div class="col-3"><x-admin.form.input name="lastname" /></div>
                    <div class="col"><p class="form-text">Row not auto sized</p></div>
                </x-admin.form.row>

                @locale('de')
                <x-admin.form.row locale="nl">
                    <x-admin.form.label label="Greetings" for="greeting" />
                    <x-admin.form.input name="greeting" />
                    <x-admin.form.input name="greeting" locale="en" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label label="Greetings" for="greeting" />
                    <x-admin.form.input name="greeting" />
                    <x-admin.form.input name="greeting" locale="en" />
                </x-admin.form.row>
                @endlocale()

                <x-admin.form.row locale="nl">
                    <x-admin.form.label label="Greetings" for="greeting" />
                    <x-admin.form.textarea name="greeting" />
                    <x-admin.form.textarea textEditor="default" name="greeting" locale="en" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label label="Greetings" for="greeting" locale="nl" />
                    <x-admin.form.textarea name="greeting" locale="nl" />
                    <x-admin.form.textarea textEditor="mini" name="greeting" locale="en" />
                </x-admin.form.row>

            </x-admin.panel>

        </x-admin.page.col>
    </x-admin.page.row>

    <x-admin.panel title="Panel">

<div class="row">
<div class="col-12">
Current implemention = Left -> Right should become possible (see trait adminFormRow crap)
</div>
<div class="col-6">
<pre><code> 
@php
    echo htmlspecialchars('
<x-admin.form.select 
    col="4"
    :bind="$data->units->where(\'unit_group_id\', 3)->first()" 
    :options="$groups->where(\'value\', \'pricing\')->first()->units" 
    boundAttribute="unit_group_unit_id"
    name="unit[pricing][unit_id]"
    required="true"
/> 
');
@endphp
</code></pre>
</div>
<div class="col-6">
<pre><code> 
@php
    echo htmlspecialchars('
<x-admin.form.select 
    col="4"
    :options="$groups->where(\'value\', \'pricing\')->first()->units" 
    boundAttribute="units{unit_group_id=3}.unit_group_unit_id"
    name="unit[pricing][unit_id]"
    required="true"
/> 
');
@endphp
</code></pre>
</div>
</div>
        


        <x-admin.form.row>
            <x-admin.form.label label="First name" for="firstname" />
            <x-admin.form.input name="firstname" />
        </x-admin.form.row>

        <div class="form-group">
            <x-admin.form.label label="Last name" for="lastname" />
            <x-admin.form.input name="lastname" />
        </div>

    </x-admin.panel>

    <x-admin.panel title="Module config routes">

        <pre>
            <code>
'routes' => [                                  
    "units.index" => [                              //required: route name
        'method' => 'get',                          //optional: route method, default: get
        'path' => '/{unitType}/units',              //required: route path
        'action' => 'units'                         //optional: controller action, if not exists: with be created from route name (unitsIndex)
        'controller' => 'UnitsTypesUnits'           //optional: controller name, if not exists: config.controller will be used
        'name' => 'units.index'                     //optional: route name, if not exists: route name will be used       
    ],
    "units.store" => [
        'method' => 'post',
        'path' => '/{unitType}/units'
    ]
]
            </code>
        </pre>

    </x-admin.panel>


    <x-admin.panel type="overview" title="Warehouse item variants">
        
        <x-slot name="controls">
            (control) buttons
        </x-slot>

        <div class="overview">
            <div class="overview-header">

                <div>x-admin.panel.overview title="Warehouse item variants"</div>
                <div>x-admin.panel type="overview" :locales="true|false|undefined|attribute-only" title="Warehouse item variants"</div>

            </div>
            <div class="overview-body">
                <h5>Types</h5>
                <ul class="list-group">
                    <li class="list-group-item">default (or undefined/empty)</li>
                    <li class="list-group-item">overview</li>
                </ul>
            </div>
        </div>
    </x-admin.panel>


    <div class="row">
        <div class="col-4">

    <pre>
        <code>
            @verbatim
@dataSet('user', [
    [
        'id' => 1, 
        'name' => 'anne'
    ], [
        'id' => 2, 
        'name' => 'pipo'
    ]
], 'id')


@dataSetEntry('user', [
    'id' => 1, 
    'name' => 'asdsadasa'
])

@dataSetEntry('user', [
    'id' => 3, 
    'name' => 'dddddd'
])
    @endverbatim    
   
        </code>
    </pre>

</div>
<div class="col-4">

    <span>get gets data from dataset without ajax request</span>
    <pre>
        <code>
    data.set('user')
        .where('id', '=', 1)
        .where('name', '=', 'anne')
        .whereIn('name', ['anne', 'pipo'])
        .whereNotIn('name', ['joe', 'doe'])
        .get()
        </code>
    </pre>

    <p>Also available: getFirst()</p>

    <span>fetch does a ajax request</span>
    <pre>
        <code>
    data.set('user')
        .where('id', '=', 1)
        .where('name', '=', 'anne')
        .fetch()
        </code>
    </pre>

    <p>Also available: fetchFirst() getOrFetch() getOrFetchFirst()</p>

</div>
</div>



@endsection
