@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button button="module" :route="route('admin.warehouses.index')" label="Warehouses" />    
    <x-admin.button button="module" :route="route('admin.stock.index')" label="Stock" />
    <x-admin.button button="create" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.warehouses.index')}}">Warehouses</a></li>
    <li>Locations</li>
@endsection

@section('filters', true)

@section('content')

    <x-admin.panel type="overview" title="Warehouse locations">

        <div class="overview">

            <div class="overview-header">
                <div class="row row-item">
                    <div class="col"><x-admin.link.sortable label="Systemname" attribute="systemname" /></div>
                    <div class="col-2"><x-admin.link.sortable label="Warehouse" attribute="warehouse" /></div>
                    <div class="col-1 text-end"><x-admin.link.sortable label="Stock" attribute="stock_count" /></div>
                    <div class="col-2 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">
                @foreach ($data as $row)
                    <div class="row row-item">
                        <div class="col fw-bold text-truncate"><a href="{{ route('admin.warehouseslocations.edit', $row->id) }}">{{ $row->systemname }}</a></div>
                        <div class="col-2 text-truncate"><a href="{{ route('admin.warehouses.edit', $row->warehouse_id) }}">{{ $row->warehouse?->systemname }}</a></div>
                        <div class="col-1 text-end"><a href="{{ route('admin.warehousesstock.filter', ['warehouse_location_filter' => $row->id]) }}">{{$row->stock_count}}</a></div>
                        <div class="col-2 controls">
                            <x-admin.button group="overview" button="edit" :parameters="$row->id" />
                            <x-admin.button group="overview" button="destroy" :parameters="$row->id" data-name="{{$row->systemname}}" :disabled="$row->is_locked" />
                        </div>                  
                    </div>
                @endforeach
            </div>

            <div class="overview-empty alert alert-info mt-3">There are no warehouse locations yet.</div>
            
        </div>

    </x-admin.panel>

@endsection