@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button button="module" :route="route('admin.units.index')" label="Units" />
    <x-admin.button button="module" :route="route('admin.unitsgroups.index')" label="Groups" />
    <x-admin.button button="create" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.unitstypes.index')}}">Unit types</a></li>
@endsection

@section('filters', true)

@section('content')

    <x-admin.panel type="overview" title="Overview">

        <div class="overview">
            <div class="overview-header">
                <div class="row row-item">
                    <div class="col-3 col-lg-2"><x-admin.link.sortable label="Systemname" attribute="systemname" /></div>
                    <div class="col-3 col-lg-2"><x-admin.link.sortable label="Mode" attribute="mode" /></div>
                    <div class="col">Base unit</div>
                    <div class="col-2 text-end"><x-admin.link.sortable label="Units" attribute="units_count" /></div>
                    <div class="col-2 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">

                @foreach ($data as $row)

                    <div class="row row-item">
                        <div class="col-3 col-lg-2 fw-bold"><a href="{{ route('admin.unitstypes.edit', $row->id) }}">{{$row->systemname}}</a></div>
                        <div class="col-3 col-lg-2">{{ $row->mode }}</div>
                        <div class="col">
                            @isset($row?->baseUnit) 
                                <a href="{{ route('admin.units.edit', $row->baseUnit->id) }}">{{ $row->baseUnit->systemname }}</a>
                            @else
                                -
                            @endisset
                        </div>
                        <div class="col-2 text-end">
                            @if ($row->units_count > 0)
                                <a href="{{ route('admin.units.filter', ['unit_type_filter' => $row->id]) }}">{{$row->units_count}}</a>
                            @else
                                0
                            @endif
                        </div>
                        <div class="col-2 controls">
                            <x-admin.button group="overview" button="edit" :parameters="$row->id" />
                            <x-admin.button group="overview" button="destroy" :parameters="$row->id" data-name="{{$row->systemlabel}}" :disabled="$row->is_locked" />
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="overview-empty alert alert-info mt-3">There are no units types yet.</div>
        </div>

    </x-admin.panel>

@endsection