@extends('admin::layouts.app')

@section('title', true)

@section('flags')
    <x-admin::icon icon="lock" class="flag flag-is-locked" :if="$data->exists && $data->is_locked" />
@endsection

@section('controls')
    <x-admin.button button="back" />
    <x-admin.button button="destroy" data-name="{{$data->systemlabel}}" :if="$data->exists && !$data->is_locked" />
    <x-admin.button button="store" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.unitstypes.index')}}">Unit Types</a></li>
    <li>{{ !$data->exists ? 'Create' : $data->systemlabel }}</li>
@endsection

@section('form')

    <x-admin.form
        :action="route('admin.unitstypes.store')"
        method="POST"
        :bind="$data"
        x-data="{ 
            mode: '{{ old('mode', $data->mode) }}',
            unit_base_id: '{{ old('unit_base_id', $data->unit_base_id) }}'
        }"
    />
    <x-admin.form.input type="hidden" name="id" value="{{ $data->id }}" />

@endsection

@section('content')

    <x-admin.page.row>
        <x-admin.page.col col="6">
            
            <x-admin.panel title="General information">
                <x-admin.form.row>
                    <x-admin.form.label for="systemname" label="systemname" required="true" />
                    <x-admin.form.input name="systemname" required="true" />
                </x-admin.form.row>
                
                <x-admin.form.row>
                    <x-admin.form.label label="Mode" for="mode" />
                    <x-admin.form.select 
                        :options="\App\Enums\UnitTypeMode::class" 
                        name="mode" 
                        x-model="mode"
                    />
                </x-admin.form.row>

                <x-admin.form.row x-show="mode=='{{ \App\Enums\UnitTypeMode::CONVERSABLE }}'" x-collapse>
                    <x-admin.form.label label="Base unit" for="unit_base_id" />
                    <x-admin.form.select 
                        :options="$data->units" 
                        optionValueAttribute="id"
                        name="unit_base_id" 
                        x-model="unit_base_id"
                        x-collapse 
                    />
                </x-admin.form.row>
        
            </x-admin.panel>

        </x-admin.page.col>

        <x-admin.page.col>
           
            <x-admin.panel type="locales" title="Language specific">
                @foreach (config('locales') as $locale => $localeData)
                    <x-admin.form.row :locale="$locale">
                        <x-admin.form.label label="Name" for="name" />
                        <x-admin.form.input name="name" />
                    </x-admin.form.row>
                @endforeach
            </x-admin.panel>

        </x-admin.page.col>

    </x-admin.page.row>

    

    <x-admin.panel type="overview" title="Units">

         <div class="overview-header">
            <div class="row row-item">
                <div class="col">Systemname</div>
                <div class="col-1 text-end">Value</div>
                <div class="col-1 text-end" x-show="mode=='{{ \App\Enums\UnitTypeMode::CONVERSABLE }}'">Is base unit</div>
            </div>
        </div>

        <div class="overview-body units-container">

            @foreach ($data->units as $row)
                <div class="row row-item" data-id="{{$row->id}}">
                    <div class="col fw-bold"><a href="{{ route('admin.units.edit', $row->id) }}">{{$row->systemname}}</a></div>
                    <div class="col-1 text-end">{{$row->value}}</div>
                    <div class="col-1 text-end" x-show="mode=='{{ \App\Enums\UnitTypeMode::CONVERSABLE }}'">
                        <x-admin::icon icon="check" x-show="unit_base_id=={{$row->id}}" class="icon-base-unit" />
                    </div>
                </div>
            @endforeach

        </div>

    </x-admin.panel>

@endsection 