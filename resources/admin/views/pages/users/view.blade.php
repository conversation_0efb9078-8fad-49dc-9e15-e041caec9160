@extends('admin::layouts.app')
@section('content')

<nav class="content-header">
    
    <div class="breadcrumbs">
        <ol>
            <li><a href="{{ route('admin.'.App::get('module').'.index')}}">Contacts</a></li>
            <li>{{$data->getFullName()}}</li>
        </ol>
    </div>

    <div class="controls">
        <a class="btn btn-sm btn-outline-primary " href="{{ route('admin.'.App::get('module').'.index') }}">
            Back
        </a>
        @if (!$data->is_verified)
            <a class="btn btn-sm btn-outline-primary" href="{{ route('admin.'.App::get('module').'.activation', $data->id) }}">
                Activation
            </a>
        @else
            <a class="btn btn-sm btn-outline-primary" href="{{ route('admin.'.App::get('module').'.passwordForget', $data->id) }}">
                Forgot password
            </a>
        @endif
        
        <a class="btn btn-sm btn-primary" href="{{ route('admin.'.App::get('module').'.edit', $data->id) }}">
            Modify
        </a>
    </div>
</nav>

<div class="content-body">
    <h1 class="pt-3">{{$data->getFullName()}}</h1>
    <x-admin.alert/>

    <div class="card mt-5">
        <div class="card-header bg-body-tertiary border-bottom">
            <div class="card-title col-auto mb-0">Contact information</div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-12">
                    <div class="row form-group align-items-center">
                        <label for="company" class="col-sm-3 col-form-label">Company(ies)</label>
                        <div class="col-sm-9">
                            @foreach($data->companies as $company)

                                <a href="{{ route('admin.companies.edit', $company->id) }}">{{ $company->name }}</a>
                                
                                @if (!$loop->last)
                                    ,
                                @endif

                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="row form-group align-items-center">
                        <label for="firstname" class="col-sm-3 col-form-label">Firstname</label>
                        <div class="col-sm-9">
                            {{$data->firstname}}
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="row form-group align-items-center">
                        <label for="lastname" class="col-sm-3 col-form-label">Lastname</label>
                        <div class="col-sm-9">
                            {{$data->lastname}}
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="row form-group align-items-center">
                        <label for="language" class="col-sm-3 col-form-label">Language</label>
                        <div class="col-sm-9">
                            <img src="{{ URL::asset('/img/flags/4x3/'.$data->language.'.svg') }}" class="flag rounded" alt="Flag-{{$data->language}}">
                            
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="row form-group align-items-center">
                        <label for="email" class="col-sm-3 col-form-label">E-mail address</label>
                        <div class="col-sm-9">
                            {{$data->email}}
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="row form-group align-items-center">
                        <label for="verified" class="col-sm-3 col-form-label">Verified</label>
                        <div class="col-sm-9">
                            @if ($data->is_verified)
                                <i class="fas fa-check text-success"></i>
                            @else
                                <i class="fas fa-times text-danger"></i>
                            @endif
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</div>
@endsection