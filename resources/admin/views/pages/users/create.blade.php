@extends('admin::layouts.app')
@section('content')
<form action="{{ route('admin.'.App::get('module').'.store') }}" method="POST" enctype="multipart/form-data">
<input type="hidden" name="id" value="{{ $data->id }}">
@csrf

<nav class="content-header">

    <div class="breadcrumbs">
        <ol>
            <li><a href="{{route('admin.'.App::get('module').'.index')}}">Contacts</a></li>
            <li>{{ empty($data->id) ? 'Create' : $data->getFullName() }}</li>
        </ol>
    </div>

    <div class="controls">

        <x-admin.button type="back" />

        <x-admin.button type="save" />

    </div>
</nav>

<div class="content-body">

    <h2 class="title">{{ empty($data->id) ? 'Create Contact' : 'Edit ' . $data->getFullName() }}</h2>

    <x-admin.alert/>

    <div class="card card-form">

        <div class="card-header" >
            <div>
                <h2>Contact information</h2>
            </div>
        </div>
        
        <div class="card-body">
            
            <div class="row-block">
                <label class="label label-required" for="company_ids">Company ID(s)</label>
                
                <div class="input">

                    <select name="company_ids[]" id="company_ids" class="form-control form-control-sm selectpicker" multiple>
                        
                        <option value="" >Nothing selected</option>
                        
                        @foreach ($companies as $company)
                            <option value="{{ $company->id }}" 
                                
                                @if ($company->id == $companyId || $data->companies->pluck('id')->contains($company->id))
                                    selected
                                @elseif (!empty($companyId))
                                    disabled
                                @endif
                            
                                >
                                {{ $company->name }}
                            </option>
                        @endforeach
                    </select>

                </div>
            </div>
            
            <div class="row-block">
                <label class="label label-required" for="language">Language</label>
                <div class="input">

                        <select name="language" id="language" class="form-control text-uppercase selectpicker form-control-sm">
                                
                            @foreach (config('languages') as $lang)
                                <option value="{{ $lang['code'] }}" data-content= '<img src="{{URL::asset('/img/flags/4x3/'.$lang['code'].'.svg')}}" class="flag rounded" alt="Flag">' {{ old('language', $data) ==  $lang['code'] ? "selected" : "" }}>
                                </option>
                            @endforeach
    
                        </select>
                    </select>

                </div>
            </div>

            <div class="row-block">
                <label class="label label-required" for="exact_id">Exact ID</label>
                <div class="input">
                    <select name="exact_id" id="exact_id" class="form-control selectpicker form-control-sm">
                        @foreach (range(1, 5) as $value)

                            <option value="{{ $value }}" {{ old('exact_id', $data) == $value ? "selected" : "" }}>{{ $value }}</option>

                        @endforeach
                    </select>
                </div>
            </div>

            <x-admin.form.row.gender :any="$data" />

            <div class="row-block">

                <label class="label label-required" for="firstname">First name</label>

                <div class="input">
                    <input type="text" name="firstname" id="firstname" class="form-control" value="{{ old('firstname', $data) }}">
                </div>
            </div>
    
            <div class="row-block">

                <label class="label" for="lastname">Last name</label>

                <div class="input">
                    <input type="text" name="lastname" id="lastname" class="form-control" value="{{ old('lastname', $data) }}">
                </div>
            </div>

            <div class="row-group">
        
                <div class="row-block">

                    <label class="label" for="email">Email</label>

                    <div class="input">
                        <input type="email" name="email" id="email" class="form-control" value="{{ old('email', $data) }}">
                    </div>
                </div>

                <div class="row-block">

                    <label class="label" for="phonenumber">Phonenumber</label>

                    <div class="input">
                        <input type="text" name="phonenumber" id="phonenumber" class="form-control" value="{{ old('phonenumber', $data) }}">
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

</form>

@endsection