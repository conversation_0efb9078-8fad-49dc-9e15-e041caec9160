@extends('admin::layouts.app')
@section('content')


<nav class="content-header">
    <div class="breadcrumbs">
        <ol>
            <li>Contacts</li>
        </ol>
    </div>
</nav>

<div class="content-body">

    <h1 class="title">Overview</h1>
    
    <x-admin.alert/>

    <div class="card card-overview">

        <div class="card-header">
            <div>
                <h2>Contacts</h2>
            </div>
        </div>

        <div class="card-body ">

            <div class="overview-header">

                <div class="row row-item">

                    <div class="col">Name</div>

                    <div class="col-2 text-center">Gender</div>

                    <div class="col-3">Companies</div>

                    <div class="col-1 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">

                @foreach ($data as $row)
                    <div class="row row-item">

                        <div class="col fw-bold">
                            <a href="{{ route('admin.'.App::get('module').'.edit', $row->id) }}"> 
                                {{ $row->getFullName()}}
                            </a>
                        </div>

                        <div class="col-2 gender text-center" title="{{ucFirst($row->gender->value)}}">
                            {!! $row->gender->value == 'male' ? "<i class='fa-solid fa-mars male'></i>" : "<i class='fa-solid fa-venus female'></i>" !!}
                        </div>

                        <div class="col-3 text-truncate">
                            @foreach($row->companies as $company)

                                <a href="{{ route('admin.companies.edit', $company->id) }}">{{ $company->name }}</a>
                                
                                @if (!$loop->last)
                                    ,
                                @endif

                            @endforeach
                        </div>
                        

                        <div class="col-1 controls">

                            <a href="{{ route('admin.'.App::get('module').'.edit', $row->id) }}">
                                <i class="fa-regular fa-eye"></i>
                            </a>

                            <a href="{{ route('admin.'.App::get('module').'.edit', $row->id) }}">
                                <i class="fa-regular fa-pen-to-square"></i>
                            </a>

                            <a 
                                type="button" 
                                class="btn-delete" 
                                name="delete" 
                                href="#" 
                                data-href="{{ route('admin.'.App::get('module').'.destroy', $row->id) }}"
                                data-name="{{ $row->systemname }}"
                                data-text="">

                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>

        </div>
        
    </div>
</div>

@endsection