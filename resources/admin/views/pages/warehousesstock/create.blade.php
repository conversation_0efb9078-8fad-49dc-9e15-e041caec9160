@extends('admin::layouts.app')

@use('App\Support\StrSupport as Str')
@use('App\Enums\SystemContext')

@section('title', true)

@section('flags')
    <x-admin::icon icon="lock" class="flag flag-is-locked" :if="$data->exists && $data->is_locked" />
@endsection

@section('controls')
    <x-admin.button button="back" />
    <x-admin.button button="destroy" data-name="{{$data->systemlabel}}" :if="$data->exists && !$data->is_locked" />
    <x-admin.button button="store" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.items.index')}}">Items</a></li>
    <li>{{ !$data->exists ? 'Create' : $data->systemlabel }}</li>
@endsection

@section('form')
    <x-admin.form
        :bind="$data" 
        class="form-items" 
        action="{{route('admin.items.store')}}"
        x-data="{ 
            unit_group_warehouse_unit_id: '{{ old('units.1.unit_group_unit_id', $data?->units?->firstWhere('unit_group_id', 1)?->unit_group_unit_id )}}',
            unit_group_sales_unit_id: '{{ old('units.2.unit_group_unit_id', $data?->units?->firstWhere('unit_group_id', 2)?->unit_group_unit_id )}}',
            unit_group_pricing_unit_id: '{{ old('units.3.unit_group_unit_id', $data?->units?->firstWhere('unit_group_id', 3)?->unit_group_unit_id )}}',
            unit_group_weight_unit_id: '{{ old('units.4.unit_group_unit_id', $data?->units?->firstWhere('unit_group_id', 4)?->unit_group_unit_id )}}'
        }"
    />

    <x-admin.form.input type="hidden" name="id" />
    <x-admin.form.input type="hidden" name="company_id" value="{{ $companyId }}" />
@endsection

@section('content')


    <x-admin.panel title="General information">

        <x-admin.form.row>
            <x-admin.form.label for="code" label="Code" required="true" />
            <x-admin.form.input name="code" required="true" />
        </x-admin.form.row>

        <x-admin.form.row>
            <x-admin.form.label for="systemname" label="Systemname" required="true" />
            {{-- <x-admin.form.input name="systemname" required="true" /> --}}
            <x-admin.form.input name="systemname"  />
        </x-admin.form.row>
    
        <x-admin.form.row>
            <x-admin.form.label for="item_group_id" label="Group" />
            <x-admin.form.select 
                :options="$itemGroups" 
                name="item_group_id" 
                required="true" 
                optionDataAttributes="ledger_account_*_id" 
            />
        </x-admin.form.row>

        <x-admin.form.row>
            <x-admin.form.label for="type" label="Type" />
            <x-admin.form.select 
                :options="\App\Enums\ItemType::class" 
                name="type" 
                required="true" 
                :default="\App\Enums\ItemType::DEFAULT->value"
            />
        </x-admin.form.row>

        @foreach ($unitGroups as $unitGroup)

            @php
                $itemUnit = $data->units?->firstWhere('unit_group_id', $unitGroup->id);
            @endphp

            <x-admin.form.row class="row-unit-group" data-id="{{ $unitGroup->id }}">
                
                <x-admin.form.input 
                    type="hidden" 
                    name="units[{{ $unitGroup->id }}][id]" 
                    value="{{ old('units.'.$unitGroup->id.'.id', $itemUnit?->id ?? Str::uniqId()) }}" 
                />
                
                <x-admin.form.input 
                    type="hidden" 
                    name="units[{{ $unitGroup->id }}][unit_group_id]" 
                    valueAttribute="null"
                    value="{{ $unitGroup->id }}"
                    data-value="{{$unitGroup->id}}" 
                />

                <x-admin.form.label 
                    for="units[{{ $unitGroup->id }}][unit_group_unit_id]" 
                    label="{{ $unitGroup->systemname }} unit" 
                />

                @if ($unitGroup->value == 'weight')
                    <x-admin.form.input 
                        col="1" 
                        :bind="$itemUnit" 
                        valueAttribute="value" 
                        type="number" 
                        name="units[{{ $unitGroup->id }}][value]" 
                        required="true" 
                        is-locked="{{ $data->exists && $data->is_locked }}"
                    />
                @endif

                <x-admin.form.select
                    col="{{ $unitGroup->value == 'weight' ? 2 : 3 }}"
                    :options="$unitGroup->units->map(function($unit) {
                        return [
                            'id' => $unit?->id,
                            'systemname' => $unit?->unit?->systemname
                        ];
                    })" 
                    name="units[{{ $unitGroup->id }}][unit_group_unit_id]" 
                    :bind="null"
                    class="unit-select"
                    selected="{{ old('units.1.unit_group_unit_id', $data?->units?->firstWhere('unit_group_id', 1)?->unit_group_unit_id ) }}"
                    x-model="unit_group_{{ $unitGroup->value }}_unit_id"
                    is-locked="{{ $data->exists && $data->is_locked }}"
                    required="true"
                />

                <x-admin.form.col class="col-3 relations mb-n3">

                    @template('item_unit_relation', false)
                    <x-admin.form.row  
                        data-id="[[ID]]" 
                        data-relation-name="[[SYSTEMLABEL]]"
                        x-data="{ 
                            model: '[[MODEL]]' 
                        }"
                    >
                        <x-admin.form.input 
                            type="hidden" 
                            name="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][id]" 
                            value="[[ID]]" 
                        />

                        <x-admin.form.input 
                            type="hidden" 
                            name="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][unit_compositional_id]" 
                            value="[[UNIT_COMPOSITIONAL_ID]]" 
                        />

                        <x-admin.form.label 
                            col="4" 
                            for="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][model]" 
                            label="[[SYSTEMLABEL]]" 
                        />

                        <x-admin.form.select 
                            col="8"
                            name="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][model]" 
                            x-model="model"
                            data-live-search="true"
                            is-locked="{{ $data->exists && $data->is_locked }}"
                        >
                            @template('item_unit_relation_options')
                                @template('item_unit_relation_option')
                                <option value="[[VALUE]]">[[TITLE]]</option>
                                @endtemplate
                            @endtemplate                                
                        </x-admin.form.select>
         
                    </x-admin.form.row>
                    @endtemplate

                    @php
                        $relations = [];
                        $oldRelations = collect(old('units.'.$unitGroup->id.'.relations', []));

                        if ($unitGroup->value == 'weight') {

                            $relation = $itemUnit?->relations?->first();

                            $relationOld = $oldRelations?->first();

                            $relatable = $relation ?? $relationOld['model'] ?? null;

                            $relations[] = [
                                'id' => 
                                    $relation?->id ?? 
                                    $relationOld?->id ?? 
                                    Str::uniqId(),
                                'unit_group_id' => $unitGroup->id,
                                'unit_compositional_id' => null,
                                'systemlabel' => 'per',
                                'item_unit_relation_options' => $unitGroupWeightUnits->map(function($unit) {
                                    return [
                                        'value' => 'Unit::'.$unit->id,
                                        'title' => $unit->systemlabel
                                    ];
                                }),
                                'model' => $relatable instanceof \App\Models\BaseModel
                                    ? $relatable->relatable_type.'::'.$relatable->relatable_id
                                    : $relatable
                            ];

                        } else {

                            $unitCompositionals = $itemUnit?->unitGroupUnit?->unit?->compositionals;

                            if (count($oldRelation = $oldRelations?->pluck('unit_compositional_id')) > 0) {

                                $unitCompositionals = $unitGroup->units->map(function($unitGroupUnit) use ($oldRelation) {

                                    return $unitGroupUnit->unit?->compositionals?->whereIn('id', $oldRelation)?->count() > 0
                                        ? $unitGroupUnit->unit?->compositionals
                                        : null
                                    ;

                                })?->filter()?->first();
                            }

                            $relations = ($unitCompositionals?->sortBy('id')?->map(function($compositional) use ($unitGroup, $itemUnit, $properties, $oldRelations) {

                                $property = $properties?->firstWhere(function($property) use ($compositional) {
                                    return 
                                        $property->system_context === SystemContext::UNIT && 
                                        $property->system_context_unit_id === $compositional->unit_id &&
                                        $property->system_context_unit_compositional_id === $compositional->id
                                    ;
                                });

                                $relation = $itemUnit?->relations
                                    ->whereIn('relatable_id', $property?->values?->pluck('id'))
                                    ?->first()
                                ;

                                $relationOld = $oldRelations?->firstWhere('unit_compositional_id', $compositional->id);

                                $relatable = $relation ?? $relationOld['model'] ?? null;
                               
                                
                                $options = $property?->values?->map(function($propertyValue) {
                                    return [
                                        'value' => 'PropertyValue::'.$propertyValue->id,
                                        'title' => $propertyValue->systemlabel
                                    ];
                                });

                                if (is_null($options) ||
                                    $options->count() === 0) {
                                    return null;
                                }

                                return [
                                    'id' =>
                                        $relation?->id ??
                                        $relationOld?->id ??
                                        Str::uniqId(),
                                    'unit_group_id' => $unitGroup->id,
                                    'unit_compositional_id' => $compositional->id,
                                    'systemlabel' => $compositional->systemname,
                                    'item_unit_relation_options' => $options,
                                    'model' => $relatable instanceof \App\Models\BaseModel
                                        ? $relatable->relatable_type.'::'.$relatable->relatable_id
                                        : $relatable
                                ];
                            }) ?? collect([]))->filter();

                            Log::debug($relations->toArray());
                        }
                    @endphp

                    @foreach ($relations as $relation)                                

                        @capture('item_unit_relation_options')
                            @foreach ($relation['item_unit_relation_options'] as $option)
                                @usetemplate('item_unit_relation_option', $option)
                            @endforeach
                        @endcapture

                        @php
                            $relation['item_unit_relation_options'] = $item_unit_relation_options;
                        @endphp
                            
                        @usetemplate('item_unit_relation', $relation)

                    @endforeach

                </x-admin.form.col>

            </x-admin.form.row>

        @endforeach

    </x-admin.panel>

    <x-admin.panel title="General Ledger Accounts" name="panel-ledger-accounts">
        <div class="row row-cols-1 row-cols-xxl-2">

            @foreach ($ledgerAccountTypes as $type)
        
                <div class="col">
                
                    <div class="row-wrapper" data-type="grouped"> 
                        <div class="row-block">

                            <label class="label" for="ledger_account[{{$type->value}}][]">{{ucfirst($type->value)}}</label>        
                            <div class="input">

                                <x-admin.form.select
                                    :options="$ledgerAccountsOptions"      
                                    name="ledger_account[{{$type->value}}]" 
                                    :bind="$data->ledgerAccounts->where('type', $type->value)->first()"
                                    valueAttribute="ledger_account_id"
                                />

                            </div>
                        </div>
                    </div>

                </div>

            @endforeach
        </div>
    </x-admin-panel>

@endsection 