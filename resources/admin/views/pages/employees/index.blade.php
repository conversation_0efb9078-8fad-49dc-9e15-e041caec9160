@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button type="create" />
@endsection

@section('breadcrumbs')
    <li>Employees</li>
@endsection

@section('content')

    <x-admin.panel type="overview" title="Employees">
    
        <div class="overview-header">

            <div class="row row-item">
                <div class="col">Name</div>
                <div class="col-3">Username</div>
                <div class="col-3">Email</div>
                <div class="col-2">Roles</div>
                <div class="col-1 controls">controls</div>
            </div>
        </div>

        <div class="overview-body">

            @foreach ($data as $row)

                <div class="row row-item">
                    <div class="col">
                        {{ $row->fullName }}
                        @if ($row->gender == \App\Enums\Gender::MALE)
                            <i class="fa-solid fa-mars text-blue-600"></i>
                        @elseif ($row->gender == \App\Enums\Gender::FEMALE )
                            <i class="fa-solid fa-venus text-pink-500"></i>
                        @else
                            <i class="fa-solid fa-mars-and-venus text-indigo-400"></i>
                        @endif
                    </div>
                    <div class="col-3">{{ $row->username }}</div>
                    <div class="col-3">{{ $row->email }}</div>
                    <div class="col-2">
                        @forelse ( $row->roles as $role )
                            <span class="badge bg-secondary rounded-pill">
                                <a class="text-white" href="{{route('admin.permissions.edit', $role->id)}}">{{ $role->name }}</a>
                            </span>
                        @empty
                            -
                        @endforelse
                    </div>
                    <div class="col-1 controls">

                        <a href="{{route('admin.employees.edit', $row->id)}}" class="btn-control">
                            <i class="fa-regular fa-pen-to-square"></i>
                        </a>
                    </div>
                </div>

            @endforeach
        </div>

    </x-admin.panel>

@endsection