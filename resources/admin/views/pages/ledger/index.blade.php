@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
@endsection

@section('breadcrumbs')
    <li>Ledger</li>
@endsection

@section('filters', false)

@section('content')

    <x-admin.panel type="overview" title="Ledger Accounts">

        <div class="overview">
            <div class="overview-header">
                <div class="row row-item">
                    <div class="col">Title</div>
                    {{-- <div class="col-1 text-center">Menu</div>
                    <div class="col-1 text-center">Status</div> --}}
                    <div class="col-1 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">
                @foreach ($data as $row)
                    <x-admin.module.LedgerCategoryRow :row="$row" depth="0" />
                @endforeach
            </div>

            <div class="overview-empty alert alert-info mt-3">There are no ledger accounts yet.</div>
        </div>

    </x-admin.panel>

@endsection