@extends('admin::layouts.app')
@section('content')


<nav class="content-header">

    <x-admin.breadcrumbs>
        Import
    </x-admin.breadcrumbs>

    <div class="controls">
        <x-admin.button type="back" />
    </div>
</nav>

<div class="content-body">

    <h1 class="title">Import</h1>

    <x-admin.alert/>

    <x-admin.panel title="Search">

        <form action="{{ route('admin.'.App::get('module').'.import.search') }}" method="POST">
            @csrf

            <div class="row-block ">
                <div class="input">
                    <input type="text" name="query" id="query" class="form-control form-control-sm" value="{{old('query')}}">
                </div>
                <div class="col-auto">
                    <x-admin.button type="search" />
                </div>
            </div>

        </form>

    </x-admin.panel>
 
    @if (isset($data))
        <div class="card card-overview">

            <div class="card-header">
                <div>
                    <h2>Search results</h2>
                </div>
            </div>

            <div class="card-body ">

                @if ($data->count() == 0)
                    <x-admin.alert type="info" :message="'No results for: '.old('query')" />
                @else

                    <div class="overview-header">

                        <div class="row row-item">
                            <div class="col">Name</div>
                            <div class="col-3">Industry</div>
                            <div class="col-2">Sector</div>
                            <div class="col-1 text-center">Status</div>
                            <div class="col-1 text-center">Country</div>
                            <div class="col-1 controls">Actions</div>
                        </div>
                    </div>

                    <div class="overview-body">

                        @foreach ($data as $item)

                            <div class="row row-item">
                                <div class="col">{{$item['Name']}}</div>
                                <div class="col-3">{{$item['Industry'] ?? '-'}}</div>
                                <div class="col-2">{{$item['Pricelist_Sector__c'] ?? '-'}}</div>
                                <div class="col-1 text-center">{{$item['Account_Status__c'] ?? '-'}}</div>
                                <div class="col-1 text-center">
                                    @if (!is_null($item['country']))
                                        <x-admin.flag :code="$item['country']['code']" />
                                    @else 
                                        -
                                    @endif
                                    </div>
                                <div class="col-1 controls">
                                    <a class="text-primary" href="{{route('admin.companies.import.create', ['id' => $item['Id']])}}">
                                        <i class="fa-solid fa-cloud-arrow-down"></i>
                                    </a>
                                </div>
                            </div>

                        @endforeach
                    
                    </div>
                @endif

            </div>
            
        </div>
        @endif

</div>
@endsection