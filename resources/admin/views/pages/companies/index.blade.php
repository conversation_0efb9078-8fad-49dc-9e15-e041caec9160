@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button :route="route('admin.'.App::get('module').'.import.search')" label="Import" />
    <x-admin.button button="create" />
@endsection

@section('breadcrumbs')
    <li>{{ucfirst(App::get('module'))}}</li>
@endsection

@section('filters', true)

@section('content')

    <x-admin.panel type="overview" title="{{ucfirst(App::get('module'))}}">
    
        <div class="overview">

            <div class="overview-header">

                <div class="row row-item">
                    <div class="col"><x-admin.link.sortable label="name" attribute="name" /></div>
                    <div class="col-2 text-end">Country</div>
                    <div class="col-1 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">

                @foreach ($data as $row)
                    <div class="row row-item">

                        <div class="col fw-bold">
                            <a href="{{ route('admin.'.App::get('module').'.edit', $row->id) }}">
                                {{$row->name}}
                            </a>
                            {!! $row->is_key ? '<i class="fas fa-key text-success"></i>' : '' !!} 
                        </div>
                        
                        <div class="col-2 text-end">
                            <x-admin.flag :code="$row->country->code"/>
                        </div>
                        
                        <div class="col-1 controls">
                            <x-admin.button group="overview" button="edit" :parameters="$row->id" />
                            <x-admin.button group="overview" button="destroy" :parameters="$row->id" data-name="{{$row->systemname}}" :disabled="$row->is_locked" />
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="overview-empty alert alert-info mt-3">There are no {{ strtolower(App::get('module')) }} yet.</div>
        </div>

    </x-admin.panel>

@endsection