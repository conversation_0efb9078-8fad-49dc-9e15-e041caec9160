@extends('admin::layouts.app')

@section('title', 'Import')

@section('controls')
    <x-admin.button type="back"  />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.'.App::get('module').'.index')}}">{{ucfirst(App::get('module'))}}</a></li>
    <li>Import</li>
@endsection

@section('form')
    <x-admin.form method="post" action="{{ route('admin.'.App::get('module').'.import.search') }}" />
@endsection

@section('content')

    <x-admin.panel title="Search">

        <div class="row-block ">
            <div class="input">
                <input type="text" name="query" id="query" class="form-control form-control-sm" value="{{old('query')}}">
            </div>
            <div class="col-auto">
                <x-admin.button type="search" />
            </div>
        </div>

    </x-admin.panel>


    @if (isset($data))

        <x-admin.panel type="overview" title="Search results">

            <div class="overview-header">

                <div class="row row-item">
                    <div class="col-1">Number</div>
                    <div class="col">Name</div>
                    <div class="col-2">City</div>
                    <div class="col-2">Postal code</div>
                    <div class="col-1 text-center">Country</div>
                    <div class="col-1 controls">Actions</div>
                </div>

            </div>

            <div class="overview-body">
             
                @forelse ($data as $item)

                    <div class="row row-item">
                        <div class="col-1">{{App::get('module') != 'suppliers' ? $item->debitor_number : $item->creditor_number}}</div>
                        <div class="col">{{$item->name}}</div>
                        <div class="col-2">{{$item->city}}</div>
                        <div class="col-2">{{$item->postal_code}}</div>
                        <div class="col-1 text-center">
                            @if (!is_null($item->country))
                                <x-admin.flag :code="$item->country['code']" />
                            @else 
                                -
                            @endif
                        </div>
                        <div class="col-1 controls">
                            <a class="text-primary" href="{{route('admin.'.App::get('module').'.import', ['uniqId' => $item->uniq_id])}}">
                                <i class="fa-solid fa-cloud-arrow-down"></i>
                            </a>
                        </div>
                    </div>

                @empty
                    <x-admin.alert type="info" :message="'No results for: '.old('query')" />
                @endforelse
            </div>
        </x-admin.panel>
    
    @endif

@endsection 