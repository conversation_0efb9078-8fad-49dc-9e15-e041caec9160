@extends('admin::layouts.app')

@section('title', empty($data->id) ? 'Create' : 'Edit ' . $data->systemlabel)

@section('controls')
    <x-admin.button type="back"  />

    @if ($data->exists)
        @if ($data->is_supplier || $data->is_administration)
            <x-admin.button type="module" :route="route('admin.suppliersitems.supplier_index' , $data->id)" label="Items" />
        @endif
        <a class="btn btn-sm btn-outline-primary" href="{{ route('admin.contacts.create', $data->id) }}">Create Contact</a>
    @endif 

    <x-admin.button type="save" />

@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.'.App::get('module').'.index')}}">{{ucfirst(App::get('module'))}}</a></li>
    <li>{{ empty($data->id) ? 'Add' : $data->systemlabel }}</li>
@endsection

@section('form')
    <x-admin.form
        :action="route('admin.'.App::get('module').'.store')"
        :bind="$data"
        method="POST"
    />
        <input type="hidden" name="uniq_id" value="{{ old('uniq_id', $data) }}">
        <input type="hidden" name="is_supplier" value="{{ old('is_supplier', $data) }}">
        <input type="hidden" name="is_customer" value="{{ old('is_customer', $data) }}">
@endsection


@section('content')

    <div class="row">
        <div class="col-12 col-md-6">

            <x-admin.panel title="General information">

                <x-admin.form.row>
                    <x-admin.form.label for="systemname" label="Systemname" required="true" />
                    <x-admin.form.input name="systemname" required="true" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="website" label="Website" />
                    <x-admin.form.input name="website" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="phone" label="Phone" />
                    <x-admin.form.input name="phone" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="email" label="Email" />
                    <x-admin.form.input name="email" />   
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="email_financial" label="Email financial" />
                    <x-admin.form.input name="email_financial" />
                </x-admin.form.row>
                
            </x-admin.panel>

            <x-admin.panel title="Financial information">

                <x-admin.form.row>
                    <x-admin.form.label for="country_id" label="Country" />
                    <x-admin.form.select 
                        name="country_id" 
                        :options="$countries" 
                        data-live-search="true"
                    />
                </x-admin.form.row>


                <x-admin.form.row>
                    <x-admin.form.label for="taxIdentifier.value" label="Tax number" />
                    <x-admin.form.input name="taxIdentifier.value" />   
                </x-admin.form.row>

            </x-admin-panel>

        </div>  

        <div class="col-12 col-md-6">

            <x-admin.panel title="Classifications">
                 
                <x-admin.form.row>
                    <x-admin.form.label for="pricing_country_id" label="Pricing country" />
                    <select name="pricing_country_id" id="pricing_country_id" class="form-control form-control-sm selectpicker">      
                        @foreach ($usedCountries as $country)
                            <option data-content= '<img src="{{URL::asset('/img/flags/4x3/'.strtolower($country->code).'.svg')}}" class="flag rounded" alt="Flag"> {{$country->{'name_'.App::getLocale()} }}' value="{{ $country['id'] }}" {{ old('country_id', $data) ==  $country['id'] ? "selected" : "" }}>
                            </option>
                        @endforeach
                    </select>
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="pricing_countpricing_sector_idry_id" label="Pricing sector" />
                    <select name="sector_id" id="sector_id" class="form-control form-control-sm selectpicker">         
                        @foreach (config('modules.products.pricing.sectors' , []) as $sector)
                            <option value="{{ $sector['id'] }}" {{ old('pricing_sector_id', $data) ==  $sector['id'] ? "selected" : "" }}>{{ $sector['systemname']  }}</option>
                        @endforeach
                    </select>
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="is_key" label="Is key account" />
                    <x-admin.form.toggle name="is_key" />
                </x-admin.form.row>

                <div class="row-block">
                    <label class="label">Sales manager</label>
                    <div class="input">
                        Marc Hagens
                    </div>
                </div>

                <div class="row-block">

                    <label class="label label-required" for="admin_backoffice_id">Backoffice Manager</label>
    
                    <div class="input">
                        <select name="admin_backoffice_id" id="admin_backoffice_id" class="form-control form-control-sm selectpicker">
                            @foreach ($employees as $employee)
                                <option value="{{ $employee->id }}" {{ old('admin_backoffice_id', $data) ==  $employee->id ? "selected" : "" }}>{{ $employee->full_name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                

                <div class="row-block">
                    <label class="label">Kredietlimiet</label>
                    <div class="input">
                        € 0,00
                    </div>
                </div>

                <div class="row-block">
                    <label class="label">Betaalconditie</label>
                    <div class="input">
                        30 dagen
                    </div>
                </div>

                <div class="row-block">
                    <label class="label">Rating</label>
                    <div class="input">
                        7
                    </div>
                </div>
                
                <div class="row-block">
                    <label class="label">Employees</label>
                    <div class="input">
                        1 t/m 10 medewerkers
                    </div>
                </div>

                <div class="row-block">
                    <label class="label">Classification</label>
                    <div class="input">
                        Small Enterprise
                    </div>
                </div>

                <div class="row-block">
                    <label class="label">IBAN</label>
                    <div class="input">
                        -
                    </div>
                </div>

                
            </x-admin.panel>

            <x-admin.panel title="Financial">
                
                <div class="row-block">
                    <label class="label">Payment method</label>
                    <div class="input">On credit</div>
                </div>

                <div class="row-block">
                    <label class="label">BICCode</label>
                    <div class="input">-</div>
                </div>

            </x-admin.panel>

        </div>
    </div>

    <div class="row">
         <div class="col-12 col-md-6">

            <x-admin.panel title="Financial address">  
                
                <x-admin.form.row>
                    <x-admin.form.label for="addresses[invoice][name]" for="addresses[invoice][country_id]" label="Country" required="true" />
                    <x-admin.form.select 
                        name="addresses[invoice][country_id]" 
                        :bind="$data?->addresses?->where('type', 'invoice')->first()?->address"
                        data-live-search="true"
                        :options="$countries"
                        required="true" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="addresses[invoice][postalcode]" label="Postalcode" required="true" />
                    <x-admin.form.input name="addresses[invoice][postalcode]" required="true" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="addresses[invoice][housenumber]" label="Housenumber" />
                    <x-admin.form.input name="addresses[invoice][housenumber]" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="addresses[invoice][addition]" label="Addition" />
                    <x-admin.form.input name="addresses[invoice][addition]" />    
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="addresses[invoice][street]" label="Street" required="true" />
                    <x-admin.form.input name="addresses[invoice][street]" required="true" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="addresses[invoice][city]" label="City" required="true" />
                    <x-admin.form.input name="addresses[invoice][city]" required="true" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="addresses[invoice][state]" label="State" />
                    <x-admin.form.input name="addresses[invoice][state]" />
                </x-admin.form.row>

            </x-admin.panel>

        </div>
         <div class="col-12 col-md-6">

            <x-admin.panel 
                title="Delivery address"
                x-data="{ different_as_financial: false }"
            >      

                <x-admin.form.row>
                    <x-admin.form.label for="different_as_financial" label="Different as financial address" />
                    <x-admin.form.toggle name="different_as_financial" x-model="different_as_financial" />
                </x-admin.form.row>

                <div x-show="different_as_financial" class="address-delivery">
                    <x-admin.form.row>
                        <x-admin.form.label for="addresses[delivery][country_id]" label="Country" required="true" />
                        <x-admin.form.select 
                            name="addresses[delivery][country_id]" 
                            :bind="$data?->addresses?->where('type', 'delivery')->first()?->address"
                            data-live-search="true"
                            :options="$countries"
                            required="true" />
                    </x-admin.form.row>

                    <x-admin.form.row>
                        <x-admin.form.label for="addresses[delivery][postalcode]" label="Postalcode" required="true" />
                        <x-admin.form.input name="addresses[delivery][postalcode]" required="true" />
                    </x-admin.form.row>

                    <x-admin.form.row>
                        <x-admin.form.label for="addresses[delivery][housenumber]" label="Housenumber" />
                        <x-admin.form.input name="addresses[delivery][housenumber]" />
                    </x-admin.form.row>

                    <x-admin.form.row>
                        <x-admin.form.label for="addresses[delivery][addition]" label="Addition" />
                        <x-admin.form.input name="addresses[delivery][addition]" />
                    </x-admin.form.row>

                    <x-admin.form.row>
                        <x-admin.form.label for="addresses[delivery][street]" label="Street" required="true" />
                        <x-admin.form.input name="addresses[delivery][street]" required="true" />
                    </x-admin.form.row>

                    <x-admin.form.row>
                        <x-admin.form.label for="addresses[delivery][city]" label="City" required="true" />
                        <x-admin.form.input name="addresses[delivery][city]" required="true" />
                    </x-admin.form.row>

                    <x-admin.form.row>
                        <x-admin.form.label for="addresses[delivery][state]" label="State" />
                        <x-admin.form.input name="addresses[delivery][state]" />
                    </x-admin.form.row>

                </div>
            </x-admin.panel>

        </div>       
    </div>





    <div class="row-block">
        <label class="label">Credit outstanding</label>
        <div class="input">€ 564,41</div>
    </div>

    <x-admin.panel title="Relations">
                 
        <div class="row-block">
            <label class="label">Sales manager</label>
            <div class="input">
                Marc Hagens
            </div>
        </div>

        <div class="row-block">

            <label class="label label-required" for="admin_backoffice_id">Backoffice Manager</label>

            <div class="input">

                <select name="admin_backoffice_id" id="admin_backoffice_id" class="form-control form-control-sm selectpicker">

                    @foreach ($employees as $employee)
                        <option value="{{ $employee->id }}" {{ old('admin_backoffice_id', $data) ==  $employee->id ? "selected" : "" }}>{{ $employee->full_name }}</option>
                    @endforeach

                </select>
            </div>
        </div>

        <div class="row-block">
            <label class="label">Betaalwijze</label>
            <div class="input">
                On credit
            </div>
        </div>

        <div class="row-block">
            <label class="label">Kredietlimiet</label>
            <div class="input">
                € 0,00
            </div>
        </div>

        <div class="row-block">
            <label class="label">Betaalconditie</label>
            <div class="input">
                30 dagen
            </div>
        </div>

        <div class="row-block">
            <label class="label">Rating</label>
            <div class="input">
                7
            </div>
        </div>
        
        <div class="row-block">
            <label class="label">Employees</label>
            <div class="input">
                1 t/m 10 medewerkers
            </div>
        </div>

        <div class="row-block">
            <label class="label">Classification</label>
            <div class="input">
                Small Enterprise
            </div>
        </div>

        <div class="row-block">
            <label class="label">IBAN</label>
            <div class="input">
                -
            </div>
        </div>

        <div class="row-block">
            <label class="label">BICCode</label>
            <div class="input">
                -
            </div>
        </div>

        <div class="row-block">
            <label class="label">Credit outstanding</label>
            <div class="input">
                € 564,41
            </div>
        </div>
    </x-admin.panel> 


    
    <div class="card card-overview mb-4">
        <div class="card-header">
            <div>
                <h2>Contacts</h2>
            </div>
        </div>
        <div class="card-body">
            
            <div class="overview-header">
                <div class="row row-item">
                    
                    <div class="col col-1 text-center">Can Login</div> 
                    <div class="col col-1 text-center">Sent login</div> 
                    <div class="col col-1 text-center">Activated</div> 
                    <div class="col col">Fullname</div> 
                    <div class="col col-2">Email</div> 
                    <div class="col col-1 text-truncate text-end">Phone</div> 
                    <div class="col col-2 text-end">Last activity</div> 
                    <div class="col col-2 text-end">Activation sent at</div> 
                    <div class="col col-1 text-end">Options</div>
                </div>
            </div>

            <div class="overview-body">
                
                    @foreach ($data->users as $user)
                        <div class="row row-item " data-id="{{$user->id}}"> 

                            <div class="col col-1 text-center"> 
                                <div class="custom-control custom-switch mt-1 form-switch"> 
                                    <input type="checkbox" class="custom-control-input user-canlogin user-{{$user->id}} form-check-input" name="user_canlogin[u_{{$user->id}}]" value="1" id="user[u_{{$user->id}}]" {{$user->can_login ? 'checked' : ''}} > 
                                    <label class="custom-control-label" for="user[u_{{$user->id}}]"></label> 
                                </div> 
                            </div> 

                            <div class="col col-1 text-center"> 
                                <div class="custom-control custom-switch mt-1 form-switch"> 
                                    <input type="checkbox" class="custom-control-input sent-user form-check-input" name="user_sent[u_{{$user->id}}]" value="1" id="user_sent[u_{{$user->id}}]"> 
                                    <label class="custom-control-label" for="user_sent[u_{{$user->id}}]"></label> 
                                </div> 
                            </div> 
                            
                            <div class="col col-1 text-center">
                                {!! $user->is_verified == 1 ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'!!}
                                
                            </div> 
                            
                            <div class="col col">

                                {{-- <a href="{{ route($app.'.contacts.show', $user->id) }}">
                                    {{$user->getFullName()}}
                                </a> --}}

                            </div> 
                            <div class="col col-2">
                                <a href="mailto:{{$user->email}}">{{$user->email}}</a>
                            </div> 

                            <div class="col col-1 text-end">{!! $user->formatPhone()!!}</div> 
                
                            <div class="col col-2 text-end"></div> 
                            <div class="col col-2 text-end">{{$user->activation_sent_at ? $user->activation_sent_at->format('d-m-Y H:i:s') : '' }}</div> 
                            <div class="col col-1 text-end"> 
                                {{-- <a type="button" class="mr-2" href="#" onclick="MyWindow=window.open('https://testingportal.vanstraaten.com//admin/customer_management?adminlogin=582','MyWindow','width=1920,height=1080'); return false;"> 
                                    <i class="fas fa-sign-in-alt"></i> 
                                </a>  --}}
                                <a href="{{ route($app.'.contacts.edit', $user->id) }}">
                                    <i class="fas fa-edit"></i> 
                                </a>
                            </div> 
                        </div>
                    @endforeach
            </div>

        </div>
    </div>

    <div class="card card-form">
        <div class="card-header">
            <div>
                <h2>Settings</h2>
            </div>
        </div>
        <div class="card-body">
                dsfsdfsdf
        </div>
    </div>

    {{-- 
    <div class="row">

        @foreach (['invoice', 'delivery'] as $addressType)

            @php
                $address = ''; 
                if (isset($addresses)) {
                    $address = Arr::first(array_filter($addresses, fn($address) => $address['pivot']['type'] === $addressType));
                }

                if (!empty(old())) {
                    $address = [
                        'postalcode' => old("address_{$addressType}_postal_code"),
                        'housenumber' => old("address_{$addressType}_housenumber"),
                        'addition' => old("address_{$addressType}_addition"),
                        'street' => old("address_{$addressType}_street"),
                        'city' => old("address_{$addressType}_city"),
                        'country_id' => old("address_{$addressType}_country"),
                        'pivot' => [
                            'email' => old("address_{$addressType}_email"),
                            'phone' => old("address_{$addressType}_phone_number"),
                            'contacts' => old("address_{$addressType}_contacts") ?? [],
                            'language' => old("address_{$addressType}_country")
                        ]
                    ];
                }

                $same = false;
                
                if(!empty($address) && empty(old())) {
                    if (
                        $address['id']  == 
                        Arr::first(array_filter($addresses, fn($address) => $address['pivot']['type'] === 'invoice'))['id'] &&
                        $addressType == 'delivery'
                        ){ 

                        $same = true;
                    }

                }

                if (!empty(old('is_same') && $addressType == 'delivery')) {
                    $same = true;
                }
                
            @endphp

            <input type="hidden" name="{{$addressType}}_address_id" value="{{ $address['id'] ?? ''}}">

            <div class="col-6">    
                <div class="card card-form">
                    <div class="card-header">
                        <div>
                            <h2>{{ucfirst($addressType)}} address</h2>
                        </div>
                    </div>
                    <div class="card-body">

                        <div class="address-wrapper">

                            <div class="row-block {{ $addressType != 'invoice' ? '' : 'invisible' }}">
                                <label class="label" for="is_same">is_same</label>
                                <div class="input">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="is_same" id="is_same" value="1" {{ $same ? ' checked' : '' }}>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="address-container{{$same ? ' collapse collapsed' : ''}}">
                                <div class="row-block">
                                    <label class="label label-required" for="address_{{$addressType}}_postal_code">Postalcode</label>
                                    <div class="input">
                                        <input type="text" name="address_{{$addressType}}_postal_code" id="address_{{$addressType}}_postal_code" class="form-control" value="{{ $address['postalcode'] ?? '' }}">
                                    </div>
                                </div>
        
                                <div class="row-block">
                                    <label class="label" for="address_{{$addressType}}_housenumber">Housenumber</label>
                                    <div class="input">
                                        <input type="text" name="address_{{$addressType}}_housenumber" id="address_{{$addressType}}_housenumber" class="form-control" value="{{ $address['housenumber'] ?? '' }}">
                                    </div>
                                </div>
        
                                <div class="row-block">
                                    <label class="label" for="address_{{$addressType}}_addition">Addition</label>
                                    <div class="input">
                                        <input type="text" name="address_{{$addressType}}_addition" id="address_{{$addressType}}_addition" class="form-control" value="{{ $address['addition'] ?? '' }}">
                                    </div>
                                </div>
        
                                <div class="row-block">
                                    <label class="label label-required" for="address_{{$addressType}}_street">Street</label>
                                    <div class="input">
                                        <input type="text" name="address_{{$addressType}}_street" id="address_{{$addressType}}_street" class="form-control" value="{{ $address['street'] ?? '' }}">
                                    </div>
                                </div>
        
                                <div class="row-block">
                                    <label class="label label-required" for="address_{{$addressType}}_city">City</label>
                                    <div class="input">
                                        <input type="text" name="address_{{$addressType}}_city" id="address_{{$addressType}}_city" class="form-control" value="{{ $address['city'] ?? '' }}">
                                    </div>
                                </div>
        
                                <div class="row-block">
                                    
                                    <label class="label" for="address_{{$addressType}}_country">Country</label>
                    
                                    <div class="input">
                                        
                                        <select name="address_{{$addressType}}_country" id="address_{{$addressType}}_country" data-live-search="true" data-size="10"  class="form-control form-control-sm selectpicker px-0">
                                            @foreach ($countries as $country)
                                            <option value="{{$country->code}}" {{ (($address['pivot']['language'] ?? '')  == $country->code) ? 'selected' : '' }} >{{$country->systemname}}</option>
                                            @endforeach
                    
                                        </select>
                    
                                    </div>
                                </div>
        
                            </div>

                            <div class="row-block">
                                <label class="label" for="address_{{$addressType}}_email">Email  (specific)</label>
                                <div class="input">
                                    <input type="email" name="address_{{$addressType}}_email" id="address_{{$addressType}}_email" class="form-control" value="{{ $address['pivot']['email'] ?? '' }}">
                                </div>
                            </div>

                            <div class="row-block">
                                <label class="label" for="address_{{$addressType}}_phone_number">Phone Number (specific)</label>
                                <div class="input">
                                    <input type="tel" name="address_{{$addressType}}_phone_number" id="address_{{$addressType}}_phone_number" class="form-control" value="{{ $address['pivot']['phone'] ?? '' }}">
                                </div>
                            </div>

                            @if ($addressType != 'delivery')

                                @php

                                    $userIds = DB::table('companies_addresses_users')
                                    ->where('company_address_id', $address['pivot']['id'] ?? '')
                                    ->pluck('user_id');

                                    if (!empty(old('address_'.$addressType.'_contacts'))) {
                                        $userIds = collect(old('address_'.$addressType.'_contacts'));
                                    }

                                @endphp
                                
                                <div class="row-block">

                                    <label class="label" for="address_{{$addressType}}_contacts">Mail Contacts</label>
                    
                                    <div class="input">
                                        
                                        <select name="address_{{$addressType}}_contacts[]" id="address_{{$addressType}}_contacts" class="form-control form-control-sm selectpicker px-0" multiple>
                                            @isset ($data->users)

                                                @foreach ($data->users as $user)

                                                    <option value="{{$user->id}}" {{ $userIds->contains($user->id) ? 'selected' : '' }}>{{$user->getFullName()}}</option>
                                                @endforeach

                                            @endisset
                    
                                        </select>
                    
                                    </div>
                                </div>
                            @endif

                        </div>
                    </div>
                </div>
            </div>
            
        @endforeach
    </div> 

     <x-admin.form.card.company.pricing :any="$data"/>
    
    <x-admin.form.card.company.shipmentrules :any="$data"/> --}}

@endsection 