@extends('admin::layouts.app')

@use('App\Support\StrSupport as Str')
@use('App\Enums\SystemContext')

@section('title', true)

@section('flags')
    <x-admin::icon icon="lock" class="flag flag-is-locked" :if="$data->exists && $data->is_locked" />
@endsection

@section('controls')
    <x-admin.button button="back" route="{{ $company->exists
        ? route('admin.suppliersitems.supplier_index', $company->id)
        : route('admin.suppliersitems.index')
    }}" />
    <x-admin.button button="destroy" data-name="{{$data->systemlabel}}" :if="$data->exists && !$data->is_locked" />
    <x-admin.button button="store" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.suppliers.index')}}">Suppliers</a></li>
    @if ($company->exists)
        <li><a href="{{route('admin.suppliers.edit', $company->id )}}">{{ $company->name }}</a></li>
        <li><a href="{{route('admin.suppliersitems.supplier_index', $company->id )}}">Items</a></li>
    @else
        <li><a href="{{route('admin.suppliersitems.index')}}">Items</a></li>
    @endif
    <li>{{ !$data->exists ? 'Create' : $data->systemlabel }}</li>
@endsection

@section('form')
    <x-admin.form
        :bind="$data" 
        class="form-items" 
        action="{{ $company->exists
            ? route('admin.suppliersitems.supplier_store', $company->id)
            : route('admin.suppliersitems.store')  
        }}"
        x-data="{ 
            code: '{{ old('code', $data?->code )}}',
            systemname: '{{ old('systemname', $data?->systemname )}}',
            item_related_id: '{{ old('item_related_id', $data?->item_related_id )}}',
            unit_group_warehouse_unit_id: '{{ old('units.1.unit_group_unit_id', $data?->units?->firstWhere('unit_group_id', 1)?->unit_group_unit_id )}}',
            unit_group_sales_unit_id: '{{ old('units.2.unit_group_unit_id', $data?->units?->firstWhere('unit_group_id', 2)?->unit_group_unit_id )}}',
            unit_group_pricing_unit_id: '{{ old('units.3.unit_group_unit_id', $data?->units?->firstWhere('unit_group_id', 3)?->unit_group_unit_id )}}',
            unit_group_weight_unit_id: '{{ old('units.4.unit_group_unit_id', $data?->units?->firstWhere('unit_group_id', 4)?->unit_group_unit_id )}}'
        }"
    />

    <x-admin.form.input type="hidden" name="id" />
    @if ($company->exists || ($data->exists && $data->is_locked))
        <x-admin.form.input type="hidden" name="company_id" value="{{ $company->id }}" />
    @endif
@endsection

@section('content')

    <x-admin.panel title="General information">

        @if (!$company->exists && (!$data->exists || !$data->is_locked))
            <x-admin.form.input type="hidden" name="company_id" value="{{ $company->id }}" />

            <x-admin.form.row> {{-- company id --}}
                <x-admin.form.label for="company_id" label="Supplier" />
                <x-admin.form.select 
                    name="company_id" 
                    :options="$companies" 
                    required="true"
                />
            </x-admin.form.row>
        @endif

        <x-admin.form.row> {{-- code --}}
            <x-admin.form.label for="code" label="Code" />
            <x-admin.form.input name="code" required="true" x-model="code" data-value="null" />
        </x-admin.form.row>

        <x-admin.form.row> {{-- systemname --}}
            <x-admin.form.label for="systemname" label="systemname" />
            <x-admin.form.input name="systemname" x-model="systemname" data-value="null" />
        </x-admin.form.row>

        <x-admin.form.row> {{-- related item --}}
            <x-admin.form.label for="item_related_id" label="Related item" />
            <x-admin.form.select 
                x-model="item_related_id"
                name="item_related_id" 
                :options="$itemsRelated" 
                required="true"
            />
        </x-admin.form.row>

        @foreach ($unitGroups as $unitGroup)

            @php
                $itemUnit = $data->units?->firstWhere('unit_group_id', $unitGroup->id);
            @endphp


            <x-admin.form.row 
                class="row-unit-group" 
                data-id="{{ $unitGroup->id }}"
                :x-show="$unitGroup->value === 'warehouse' ? 'item_related_id!=\'\'': null"
                :x-collapse="$unitGroup->value === 'warehouse' ? true : null"
            >
                
                <x-admin.form.input 
                    type="hidden" 
                    name="units[{{ $unitGroup->id }}][id]" 
                    value="{{ old('units.'.$unitGroup->id.'.id', $itemUnit->id ?? Str::uniqId()) }}" 
                />
                
                <x-admin.form.input 
                    type="hidden" 
                    name="units[{{ $unitGroup->id }}][unit_group_id]" 
                    valueAttribute="null"
                    value="{{ $unitGroup->id }}"
                    data-value="{{$unitGroup->id}}" 
                />

                <x-admin.form.label 
                    for="units[{{ $unitGroup->id }}][unit_group_unit_id]" 
                    label="{{ $unitGroup->systemname }} unit" 
                />

                @if ($unitGroup->value == 'pricing')
                    <x-admin.form.input 
                        col="1" 
                        :bind="$itemUnit" 
                        valueAttribute="value" 
                        type="money" 
                        name="units[{{ $unitGroup->id }}][value]" 
                        required="true" 
                    />
                @endif

                <x-admin.form.select 
                    col="{{ $unitGroup->value == 'pricing' ? 2 : 3 }}"
                    :options="$unitGroup->units->map(function($unit) {
                        return [
                            'id' => $unit?->id,
                            'systemname' => $unit?->unit?->systemname
                        ];
                    })" 
                    name="units[{{ $unitGroup->id }}][unit_group_unit_id]" 
                    :bind="null"
                    x-model="unit_group_{{ $unitGroup->value }}_unit_id"
                    :selected="old('units.'.$unitGroup->id.'.unit_group_unit_id', $itemUnit?->unit_group_unit_id)"
                    class="unit-select"
                    required="true"
                />

                @if ($unitGroup->value == 'warehouse')
                    <x-admin.form.col class="col-3 relations mb-n3">

                        @template('item_unit_relation', false)
                        <x-admin.form.row  
                            class="row-item-unit-relation"
                            data-id="[[ID]]" 
                            data-name="[[SYSTEMLABEL]]"
                            x-data="{ 
                                model: '[[MODEL]]'
                            }"
                        >
                            <x-admin.form.input {{-- relation ID --}}
                                type="hidden" 
                                name="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][id]" 
                                value="[[ID]]" 
                            />

                            <x-admin.form.input {{-- unit compositional ID --}}
                                type="hidden" 
                                class="unit_compositional_id"
                                name="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][unit_compositional_id]" 
                                value="[[UNIT_COMPOSITIONAL_ID]]" 
                            />

                            <x-admin.form.label 
                                col="4" 
                                for="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][model]" 
                                label="[[SYSTEMLABEL]]" 
                            />

                            <x-admin.form.select {{-- model --}}
                                col="8"
                                name="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][model]" 
                                x-model="model"
                                :bind="null"
                                class="[[DISABLED]]"
                                tabindex="[[TAB_INDEX]]"
                                data-live-search="true"
                            >
                                @template('item_unit_relation_options')
                                    @template('item_unit_relation_option')
                                    <option value="[[VALUE]]">[[TITLE]]</option>
                                    @endtemplate
                                @endtemplate                                
                            </x-admin.form.select>
            
                        </x-admin.form.row>
                        @endtemplate

                        @php
                            $relations = [];
                            $oldRelations = collect(old('units.'.$unitGroup->id.'.relations', []));
                        
                            if ($unitGroup->value == 'weight') {

                                $relation = $itemUnit?->relations?->first();

                                $relationOld = $oldRelations?->first();

                                $relatable = $relation ?? $relationOld['model'] ?? null;

                                $relations[] = [
                                    'id' => 
                                        $relation?->id ?? 
                                        $relationOld?->id ?? 
                                        Str::uniqId(),
                                    'unit_group_id' => $unitGroup->id,
                                    'unit_compositional_id' => null,
                                    'systemlabel' => 'per',
                                    'item_unit_relation_options' => $unitGroupWeightUnits->map(function($unit) {
                                        return [
                                            'value' => 'Unit::'.$unit->id,
                                            'title' => $unit->systemlabel
                                        ];
                                    }),
                                    'model' => $relatable instanceof \App\Models\BaseModel
                                        ? $relatable->relatable_type.'::'.$relatable->relatable_id
                                        : $relatable
                                ];

                            } else {

                                $unitCompositionals = $itemUnit?->unitGroupUnit?->unit?->compositionals;

                                if (count($oldRelation = $oldRelations?->pluck('unit_compositional_id')) > 0) {
                                    $unitCompositionals = $unitGroup->units->map(function($unitGroupUnit) use ($oldRelation) {
                                        return $unitGroupUnit->unit?->compositionals?->whereIn('id', $oldRelation)?->count() > 0
                                            ? $unitGroupUnit->unit?->compositionals
                                            : null
                                        ;

                                    })?->filter()?->first();
                                }

                                $itemRelatedUnitRelations = $itemsRelated->firstWhere('id', $data->item_related_id ?? null)?->units?->firstWhere('unit_group_id', $unitGroup->id)?->relations;
        
                                $relations = ($unitCompositionals?->sortBy('id')?->map(function($compositional) use ($unitGroup, $itemUnit, $properties, $oldRelations, $itemRelatedUnitRelations) {

                                    $property = $properties?->firstWhere(function($property) use ($compositional) {
                                        return 
                                            $property->system_context === SystemContext::UNIT && 
                                            $property->system_context_unit_id === $compositional->unit_id &&
                                            $property->system_context_unit_compositional_id === $compositional->id
                                        ;
                                    });

                                    $relation = $itemUnit?->relations
                                        ->whereIn('relatable_id', $property?->values?->pluck('id'))
                                        ?->first()
                                    ;
                    
                                    $relationOld = $oldRelations?->firstWhere('unit_compositional_id', $compositional->id);

                                    $relatable = $relation ?? $relationOld['model'] ?? null;

                                    if (is_string($relatable)) {
                                        list($relatableType, $relatableId) = explode('::', $relatable);
                                        $relatable = (object)[
                                            'relatable_type' => $relatableType,
                                            'relatable_id' => $relatableId
                                        ];
                                    }
                                    
                                    $isSetAtRelatedItem = false;

                                    $itemRelatedUnitRelations?->each(function($relatedItemUnit) use ($relatable, &$isSetAtRelatedItem) {

                                        if (is_null($relatable)) return;

                                        if ($relatedItemUnit->relatable_type === $relatable->relatable_type &&
                                            $relatedItemUnit->relatable_id === $relatable->relatable_id) {
                                            $isSetAtRelatedItem = true;
                                        }
                                    });
                                
                                    $options = $property?->values?->map(function($propertyValue) {
                                        return [
                                            'value' => 'PropertyValue::'.$propertyValue->id,
                                            'title' => $propertyValue->systemlabel
                                        ];
                                    });

                                    if (is_null($options) ||
                                        $options->count() === 0) {
                                        return null;
                                    }

                                    return [
                                        'id' =>
                                            $relation->id ??
                                            $relationOld->id ??
                                            Str::uniqId(),
                                        'unit_group_id' => $unitGroup->id,
                                        'unit_compositional_id' => $compositional->id,
                                        'systemlabel' => $compositional->systemname,
                                        'item_unit_relation_options' => $options,
                                        'disabled' => $isSetAtRelatedItem 
                                            ? 'disabled'
                                            : '',
                                        'tab_index' => $isSetAtRelatedItem 
                                            ? '-1'
                                            : '',
                                        'model' => $relatable instanceof \App\Models\BaseModel || $relatable instanceof StdClass
                                            ? $relatable->relatable_type.'::'.$relatable->relatable_id
                                            : $relatable
                                    ];
                                }) ?? collect([]))->filter();
                            }
                        @endphp

                        @foreach ($relations as $relation)                                

                            @capture('item_unit_relation_options')
                                @foreach ($relation['item_unit_relation_options'] as $option)
                                    @usetemplate('item_unit_relation_option', $option)
                                @endforeach
                            @endcapture

                            @php
                                $relation['item_unit_relation_options'] = $item_unit_relation_options;
                            @endphp
                                
                            @usetemplate('item_unit_relation', $relation)

                        @endforeach

                    </x-admin.form.col>
                @endif

            </x-admin.form.row>

        @endforeach

    </x-admin.panel>


    <x-admin.panel type="overview" class="item-variants" title="Warehouse item variants">

        <div class="overview overview-static">
            <div class="overview-header">
                
                @template('item-variant-header')
                <div class="row row-item">
                    <div class="col-1">&nbsp</div>
                    <div class="col-3">Code</div>
                    <div class="col-3">Name</div>
                    <div class="col-2 text-end">price per [[pricing_unit={{$data?->units?->firstWhere('unit_group_id', 3)?->unitGroupUnit?->unit?->systemname}}]]</div>
                    <div class="col">
                        <div class="row units">
                            @template('units-header')

                                @template('unit-relations-header', false)
                                    <div class="col text-end">[[name]]</div>
                                @endtemplate

                                @foreach (($data?->units?->firstWhere('unit_group_id', 1)?->unitGroupUnit?->unit?->compositionals->sortBy('id')->toArray() ?? []) as $relation)
                         
                                    @usetemplate('unit-relations-header', [
                                        'name' => $properties?->firstWhere(function($property) use ($relation) {
                                            return 
                                                $property->system_context === SystemContext::UNIT && 
                                                $property->system_context_unit_id === $relation['unit_id'] &&
                                                $property->system_context_unit_compositional_id === $relation['id']
                                            ;
                                        })->systemname
                                    ])
                                @endforeach

                            @endtemplate
                        </div>
                    </div>
                </div>
                @endtemplate

            </div>

            <div class="overview-body item-variants-container">

                @template('item-variant', false)
                <div class="row row-item" data-id="[[ID]]">
                    <x-admin.form.toggle col="1" class="variant-toggle-active" name="variants[[[ID]]][is_active]" checked="[[is_active]]" />

                    <x-admin.form.col class="col-3"> {{-- code --}}
                        <div class="input-group input-group-sm">
                            <span class="input-group-text" x-text="code || '[empty]'"></span>
                            <x-admin.form.input :col="false" class="code" value="[[CODE]]" name="variants[[[ID]]][code]" />
                        </div>
                    </x-admin.form.col>

                    <x-admin.form.col class="col-3"> {{-- systemname --}}
                        <div class="input-group input-group-sm">
                            <span class="input-group-text" x-text="systemname || '[empty]'"></span>
                            <x-admin.form.input :col="false" class="name" value="[[NAME]]" name="variants[[[id]]][name]" />
                        </div>
                    </x-admin.form.col>
                  
                    <x-admin.form.input col="2" type="money" name="variants[[[ID]]][units][3][value]" value="[[VALUE]]" />

                    <div class="col">
                        <div class="row">
                            @template('relations')
                                @template('unit-relation')
                                <div class="col text-end composable [[visibility]]">
                                    [[name]]
                                    <input type="hidden" name="variants[[[VARIANT_ID]]][units][1][id]" value="[[UNIT_ID]]" class="unit-id" />
                                    <input type="hidden" name="variants[[[VARIANT_ID]]][units][1][relations][[[ID]]][id]" value="[[ID]]" class="id" />
                                    <input type="hidden" name="variants[[[VARIANT_ID]]][units][1][relations][[[ID]]][unit_compositional_id]" value="[[UNIT_COMPOSITIONAL_ID]]" />
                                    <input type="hidden" name="variants[[[VARIANT_ID]]][units][1][relations][[[ID]]][model]" value="[[MODEL]]" />
                                </div>
                                @endtemplate
                            @endtemplate
                        </div>
                    </div>

                    <input type="hidden" name="variants[[[ID]]][id]" value="[[ID]]" class="variant-id" />
                    <input type="hidden" name="variants[[[ID]]][uniq_id]" value="[[UNIQ_ID]]" />
                    <input type="hidden" name="variants[[[ID]]][units][3][id]" value="[[UNIT_PRICING_ID]]" class="unit-pricing-id" />
                </div>
                @endtemplate

                @php
                    $variants = [];
                    $oldVariants = collect(old('variants', null));

                    if ($oldVariants?->count() > 0) {

                        $variants = $oldVariants->map(function($variant) use ($properties) {
                            
                           // dd($variant);

                            $variant['is_active'] = ($variant['is_active'] ?? '1') == '1' ? 'true' : 'false';
                            $variant['value'] = intval(data_get($variant, 'units.3.value', null));
                            $variant['unit_pricing_id'] = data_get($variant, 'units.3.id', Str::uniqId());
                            
                            $variant['relations'] = collect(data_get($variant, 'units.1.relations', []))
                                ->map(function($relation) use ($variant, $properties) {

                                    $name = null;
                                    list($model, $propertyValueId) = explode('::', $relation['model'] ?? '');

                                    $properties->each(function($property) use ($propertyValueId, &$name) {
                                        $property->values->each(function($value) use ($propertyValueId, &$name) {
                                            if ($value->id === intval($propertyValueId ?? null)) {
                                                $name = $value->systemname;
                                            }
                                        });
                                    });

                                    $relation['name'] = $name;

                                    return $relation;
                                })->toArray()
                            ;

                            return $variant;
                        });

                    } else {

                      //  dd($data->variants);
                        foreach ($data->variants as $variant) {              
                           
                            $variantUnitWarehouse = $variant?->units?->firstWhere('unit_group_id', 1);
                            $variantUnitPricing = $variant?->units?->firstWhere('unit_group_id', 3);

                            $variantUnitCompositionals ??= $variantUnitWarehouse?->unitGroupUnit->unit?->compositionals;

                            $variants[] = [
                                'id' => $variant->id ?? null,
                                'code' => $variant->code ?? null,
                                'name' => $variant->systemname ?? null,     
                                'unit_pricing_id' => $variantUnitPricing?->id ?? null,
                                'value' => $variantUnitPricing?->value,
                                'value_formatted' => Number::convertStorageToMoney($variantUnitPricing?->value, 2),
                                'uniq_id' => implode('-', $variantUnitWarehouse?->relations
                                    ->map(function($relation) {
                                        return match($relation->relatable_type) {
                                            'PropertyValue' => 'pv::'.$relation->relatable_id,
                                            'Unit'          => 'u::'.$relation->relatable_id
                                        };
                                    })->toArray()),
                                'is_active' => $variant?->is_active ?? true ? 'true' : 'false',
                                'relations' => $variantUnitWarehouse?->relations
                                    ->map(function($relation) use ($variantUnitCompositionals, $variantUnitWarehouse, $properties) {

                                        return [
                                            'id' => $relation->id,
                                            'unit_id' => $variantUnitWarehouse->id ?? Str::uniqId(),
                                            'name' => $relation->relatable->systemname,
                                            'visibility' => 'visible',
                                            'unit_compositional_id' => $properties?->firstWhere(function($property) use ($relation) {
                                                return 
                                                    $property->system_context === SystemContext::UNIT && 
                                                    $property->id === ($relation?->relatable?->property_id ?? null)
                                                ;
                                            })?->system_context_unit_compositional_id ?? null,
                                            'model' => $relation->relatable_type.'::'.$relation->relatable_id
                                        ];
                                    })->toArray()
                            ];
                        }
                    }   
                @endphp
            
                @foreach ($variants as $variant)                                
                 
                    @capture('relations')
                        @foreach ($variant['relations'] ?? [] as $relation)
                            @usetemplate('unit-relation', [
                                'id'                    => $relation['id'] ?? null,
                                'variant_id'            => $variant['id'] ?? null,
                                'unit_id'               => $relation['unit_id'] ?? Str::uniqId(),
                                'unit_compositional_id' => $relation['unit_compositional_id'] ?? null,
                                'name'                  => $relation['name'] ?? 'NONE',
                                'visibility'            => $relation['visibility'] ?? 'visible',
                                'model'                 => $relation['model'] ?? null
                            ])
                        @endforeach
                    @endcapture
            
                    @usetemplate('item-variant', [
                        'id'                    => $variant['id'] ?? null,
                        'code'                  => $variant['code'] ?? null,
                        'name'                  => $variant['name'] ?? null,
                        'value'                 => $variant['value'] ?? null,
                        'value_formatted'       => Number::convertStorageToMoney($variant['value'] ?? null, 2),
                        'unit_pricing_id'       => $variant['unit_pricing_id'] ?? null,
                        'uniq_id'               => $variant['uniq_id'] ?? null,
                        'is_active'             =>  'true',
                        'relations'             => $relations ?? [],
                    ])

                @endforeach

            </div>

            <div class="overview-empty alert alert-info mt-3{{ count($variants) > 0 ? ' collapse collapsed' : '' }}">No variants possible or required.</div>
            <div 
                class="overview-errors alert alert-danger mt-3 collape collapsed"
                x-show="!unit_group_pricing_unit_id" 
                x-cloak 
                x-transition
            >Please select a pricing unit first.</div>
        </div>
    </x-admin.panel>


    <x-admin.panel type="overview" title="Rules & Constraints">

        @slot('controls')
            <div type="button" class="btn btn-sm btn-primary btn-rule-add">
                Add rule
            </div>
        @endslot

         <div class="overview overview-rules">

            <div class="overview-header">
                <div class="row row-item">
                    <div class="col-4">
                        <div class="row">

                            <div class="col-4 offset-2 offset-md-1 ">Type</div>
                            <div class="col">Applies to</div>
                        </div>                    
                    </div>
                    <div class="col-5">Specification</div>
                    <div class="col-3">
                        <div class="row">
                            <div class="col">Pricing</div>
                            <div class="col-auto text-end">Controls</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="overview-body rules-container"> 

                @template('row-rule', false)
                <div 
                    class="row row-item row-rule" 
                    x-bind:data-id="id"
                    x-data="{ 
                        id: '[[id]]',
                        is_active: [[is_active]],
                        is_locked: [[is_locked]],
                        type: '[[type]]',
                        apply_to: '[[apply_to]]',
                        unit_group_id: '[[unit_group_id]]',
                        unit_specification: '[[unit_specification]]',
                        unit_specification_min: '[[unit_specification_min]]',
                        unit_specification_max: '[[unit_specification_max]]',
                        pricing_type: '[[pricing_type]]',
                        pricing_price: '[[pricing_price]]',
                        pricing_percentage: '[[pricing_percentage]]'
                        
                    }"
                >
                    <x-admin.form.input 
                        type="hidden" 
                        name="rules[[[id]]][id]" 
                        value="[[id]]" 
                        x-model="id" 
                    />

                    <div class="col-4">
                        <div class="row d-flex align-items-center">
                
                            <x-admin.form.toggle {{-- is_active --}}
                                col="2 col-md-1"
                                class="rule-toggle-active" 
                                id="rule_is_active_[[id]]" 
                                name="rules[[[id]]][is_active]" 
                                x-model="is_active"
                            />

                            <x-admin.form.select {{-- type --}}
                                col="4"
                                id="rule_type_[[id]]"
                                name="rules[[[id]]][type]" 
                                class="rule-type"
                                required="true"
                                x-model="type"
                                :options="\App\Enums\ItemRuleType::class"
                            />
                            
                            @php
                             $options = collect($variants ?? [])
                                ->mapWithKeys(function($v) {
                                    return [
                                        'ItemVariant::'.$v['id'] => "{$v['name']}"
                                    ];
                                })
                                ->toArray();
                            @endphp

                            <x-admin.form.select {{-- apply_to --}}
                                :col="true"
                                id="rule_apply_to_[[id]]"
                                name="rules[[[id]]][apply_to]" 
                                class="rule-apply-to"
                                required="true"
                                x-model="apply_to"
                                :options="[
                                    'item' => 'Item', 
                                    'variants' => [
                                        'title' => 'Variants',
                                        'options' => $options
                                    ]
                                ]" 
                            />

                        </div>
                    </div>

                    <div class="col-5">
                        
                        <div class="input-group input-group-sm">
                    
                            <div class="input-group-addon w-25">
                                <x-admin.form.select 
                                    id="rule_unit_group_id_[[id]]"
                                    name="rules[[[id]]][unit_group_id]" 
                                    class="unit-group-id"
                                    x-model="unit_group_id"
                                    required="true"
                                    :options="[
                                        '1' => 'Warehouse', 
                                        '2' => 'Sales',
                                        '3' => 'Pricing'
                                    ]" 
                                />
                            </div>

                            <div class="input-group-addon w-25">
                                <x-admin.form.select 
                                    id="rule_unit_specification_[[id]]"
                                    name="rules[[[id]]][unit_specification]" 
                                    class="unit-specification"
                                    required="true"
                                    x-model="unit_specification"
                                    :options="\App\Enums\ItemRuleUnitSpecification::class"
                                />
                            </div>

                            <x-admin.form.input 
                                type="decimal"
                                id="rule_unit_specification_min_[[id]]"
                                name="rules[[[id]]][unit_specification_min]" 
                                class="form-control unit-specification-min w-10"
                                data-decimals="0"
                                data-min="0"
                                data-max="1000000000"
                                value="[[unit_specification_min]]"
                                x-model="unit_specification_min"
                                x-bind:data-range="unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::RANGE }}'"
                                data-range-max="#rule_unit_specification_max_[[id]]"
                                x-bind:required="
                                    unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::MIN }}' || 
                                    unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::RANGE }}'
                                "
                                x-show="
                                    unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::MIN }}' || 
                                    unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::RANGE }}'
                                "
                            />

                            <span 
                                class="input-group-text unit-specification-to px-1"
                                x-show="unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::RANGE }}'"
                                >to
                            </span>

                            <x-admin.form.input 
                                type="decimal"
                                id="rule_unit_specification_max_[[id]]"
                                name="rules[[[id]]][unit_specification_max]" 
                                class="form-control unit-specification-max w-10"
                                data-decimals="0"
                                data-min="0"
                                data-max="1000000000"
                                value="[[unit_specification_max]]"
                                x-model="unit_specification_max"
                                x-bind:data-range="unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::RANGE }}'"
                                data-range-min="#rule_unit_specification_min_[[id]]"
                                x-bind:required="
                                    unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::MAX }}' || 
                                    unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::RANGE }}'
                                "
                                x-show="
                                    unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::MAX }}' || 
                                    unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::RANGE }}'
                                "
                            />

                            <span 
                                class="input-group-text unit-group-value"
                                x-show="unit_group_id"
                            >[[unit_group_value]]
                            </span>

                        </div>                  

                    </div>

                    <div class="col-3">
                        <div class="row d-flex align-items-center">

                            <div class="col">

                                <div x-show="type == 'pricing'">

                                    <div class="input-group input-group-sm">
                                
                                        <div class="input-group-addon">
                                            <x-admin.form.select 
                                                id="rule_pricing_type_[[id]]"
                                                name="rules[[[id]]][pricing_type]" 
                                                x-model="pricing_type"
                                                class="pricing-type"
                                                x-bind:required="type == 'pricing'"
                                                :options="\App\Enums\ItemRulePricingType::class" 
                                            />
                                        </div>

                                        <span 
                                            class="input-group-text pricing-type-amount px-1"
                                            x-show="
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PRICE_FIXED }}' || 
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PRICE_SUBSTRACT }}' || 
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PRICE_ADD }}'
                                            "
                                        >&euro;
                                        </span>
                                        
                                        <x-admin.form.input 
                                            id="rule_pricing_price_value_[[id]]"
                                            name="_rules[[[id]]][pricing_price_value]" 
                                            type="money" 
                                            data-currency="€" 
                                            data-decimals="2"
                                            data-min="0.01"
                                            value="[[pricing_price]]"
                                            class="form-control"
                                            :noInputGroup="true"
                                            x-model="pricing_price"
                                            x-bind:required="
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PRICE_FIXED }}' || 
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PRICE_SUBSTRACT }}' || 
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PRICE_ADD }}'
                                            "
                                            x-show="
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PRICE_FIXED }}' || 
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PRICE_SUBSTRACT }}' || 
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PRICE_ADD }}'
                                            "
                                        />

                                        <x-admin.form.input 
                                            id="rule_pricing_percentage_value_[[id]]"
                                            name="_rules[[[id]]][pricing_percentage_value]" 
                                            type="decimal" 
                                            :noInputGroup="true"
                                            data-decimals="0"
                                            data-min="1"
                                            data-max="100"
                                            value="[[pricing_percentage]]"
                                            class="form-control"
                                            x-model="pricing_percentage"
                                            x-bind:required="
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PERCENTAGE_SUBSTRACT }}' || 
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PERCENTAGE_ADD }}'
                                            "
                                            x-show="
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PERCENTAGE_SUBSTRACT }}' || 
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PERCENTAGE_ADD }}'
                                            "
                                        />

                                        <span 
                                            class="input-group-text pricing-type-percentage px-1"
                                            x-show="
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PERCENTAGE_SUBSTRACT }}' || 
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PERCENTAGE_ADD }}'
                                            "
                                        >%</span>
                                    </div> 
                                </div>  

                            </div>

                            <div class="col-auto text-end">

                                <x-admin.button 
                                    group="overview" 
                                    button="destroy" 
                                    data-id="[[id]]"
                                    data-name="this rule" 
                                    data-confirm="btn-rule-delete-confirm" 
                                    x-bind:class="is_locked ? ' btn-control-disabled' : ''"
                                    x-bind:disabled="is_locked"
                                />

                            </div>
                        
                        </div>
                    </div>
                </div>
                @endtemplate

                @foreach ((old('rules', null) ?? $data?->rules?->toArray()) as $rule)     

                    @php
                        $rule['apply_to'] ??= $rule['item_variant_id'] ?? 'item';
                        if ($rule['apply_to'] != 'item' &&
                            strpos($rule['apply_to'], 'ItemVariant::') === false) {
                            $rule['apply_to'] = 'ItemVariant::'. $rule['apply_to'];
                        }  
                    @endphp

                    @usetemplate('row-rule', [
                        'id' => $rule['id'],
                        'type' => $rule['type'],
                        'apply_to' => $rule['apply_to'],
                        'is_active' => $rule['is_active'] ? 'true' : 'false',
                        'unit_group_id' => $rule['unit_group_id'],
                        'unit_specification' => $rule['unit_specification'],
                        'unit_specification_min' => $rule['unit_specification_min'],
                        'unit_specification_max' => $rule['unit_specification_max'],
                        'unit_specification_max_formatted' => is_null($rule['unit_specification_max'])
                            ? ''
                            : Number::convertStorageToDecimal($rule['unit_specification_max'], 2),
                        'unit_specification_min_formatted' => is_null($rule['unit_specification_min'])
                            ? ''
                            : Number::convertStorageToDecimal($rule['unit_specification_min'], 2),
                        'pricing_type' => $rule['pricing_type'],
                        'pricing_price' => in_array($rule['pricing_type'], ['percentage_add', 'percentage_substract'])
                            ? null
                            : $rule['pricing_price_value'] ?? $rule['pricing_value'] ?? '',
                        'pricing_price_formatted' => in_array($rule['pricing_type'], ['percentage_add', 'percentage_substract'])
                            ? null
                            : Number::convertStorageToMoney($rule['pricing_price_value'] ?? $rule['pricing_value'] ?? null, 2),
                        'pricing_percentage' => !in_array($rule['pricing_type'], ['percentage_add', 'percentage_substract'])
                            ? null
                            : $rule['pricing_percentage_value'] ?? $rule['pricing_value'] ?? '',
                        'pricing_percentage_formatted' => !in_array($rule['pricing_type'], ['percentage_add', 'percentage_substract'])
                            ? null
                            : Number::convertStorageToDecimal(($rule['pricing_percentage_value'] ?? $rule['pricing_value'] ?? null), 2),
                        'is_locked' => ($rule['is_locked'] ?? false) ? 'true' : 'false'
                    ]) 

                @endforeach

            </div>

            <div class="overview-empty alert alert-info mt-3">There are no rules yet.</div>
        </div>

    </x-admin.panel>

@endsection