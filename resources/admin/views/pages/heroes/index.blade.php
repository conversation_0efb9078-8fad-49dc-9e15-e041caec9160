@extends('admin::layouts.app')
@section('content')

<nav class="content-header">
    <div class="breadcrumbs">
        <ol>
            <li>Heroes</li>
        </ol>
    </div>

    <div class="controls">
        
        <x-admin.button type="create" />
    </div>
</nav>

<div class="content-body">

    <h1 class="title">Overview</h1>
    
    <x-admin.alert/>

    <div class="card card-overview">

        <div class="card-header">
            <div>
                <h2>Heroes</h2>
            </div>
        </div>

        <div class="card-body ">

            <div class="overview-header">

                <div class="row row-item">

                    <div class="col">Name</div>

                    <div class="col-2 text-center">Status</div>

                    <div class="col-1 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">
                @foreach ($data as $row)
                
                    <div class="row row-item">

                        <div class="col fw-bold">
                            <i class="fas fa-bars me-3 sorting-handle ui-sortable-handle"></i>
                            <a href="{{ route('admin.'.App::get('module').'.edit', $row->id) }}">
                                {{$row->systemname}}
                            </a>
                        </div>

                        <div class="col-2 text-center">{!! $row->getStatusDot() !!}</div>

                        <div class="col-1 controls">
                            
                            <a href="{{ route('admin.'.App::get('module').'.edit', $row->id) }}">
                                <i class="fa-regular fa-pen-to-square"></i>
                            </a>


                            <a 
                                type="button" 
                                class="btn-delete" 
                                name="delete" 
                                href="#" 
                                data-href="{{ route('admin.'.App::get('module').'.destroy', $row->id) }}"
                                data-name="{{ $row->systemname }}"
                                data-text="">
                    
                                <i class="fas fa-trash"></i>
                            </a>

                        </div>
                    </div>

                @endforeach
            </div>

        </div>
        
    </div>
</div>
@endsection