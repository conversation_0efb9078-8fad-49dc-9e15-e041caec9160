@extends('admin::layouts.app')
@section('content')

<form action="{{ route('admin.'.App::get('module').'.store') }}" method="POST" enctype="multipart/form-data">      
<input type="hidden" name="id" value="{{ $data->id }}">
@csrf

<nav class="content-header">

    <div class="breadcrumbs">
        <ol>
            <li><a href="{{route('admin.'.App::get('module').'.index')}}">Heroes</a></li>
            <li>{{ empty($data->id) ? 'Add' : $data->systemname }}</li>
        </ol>
    </div>

    <div class="controls">

        <x-admin.button type="back" />

        <x-admin.button type="save" />

    </div>
</nav>

<div class="content-body">

    <h1 class="title">{{ empty($data->id) ? 'Create default' : 'Edit ' . $data->name_nl }}</h1>

    <x-admin.alert/>

    <div class="card card-form">
        <div class="card-header">
            <div>
                <h2>General information</h2>
            </div>
        </div>
        <div class="card-body">

            <div class="row-group">
                <x-admin.form.row.systemname :any="$data" />

                <x-admin.form.row.toggle :any="$data" column="status" />

                
                <div class="row-block">

                    <label class="label label-required" for="size">Size</label>
                    
                    <div class="input">

                        <select name="size" id="size" class="form-control selectpicker px-0">
                                
                            <option value="">Nothing selected</option>
                            @foreach (config('modules.heroes.sizes') as $size)
                                <option value="{{ $size['name'] }}" {{ old('size', $data) == $size['name']  ? "selected" : "" }}>{{ $size['name'] }}</option>
                            @endforeach
                        </select>

                    </div>
                </div>

                <div class="row-block">
                    <label class="label" for="image">Image</label>
                    <div class="input">
                        <input type="file" name="image" id="image" class="form-control">
                    </div>
                </div>
    
            </div>
            
        </div>
    </div>

    <div class="card card-form">
        
    
        <div class="card-header">
            <h2>Language specific</h2>
            <div class="controls">
                <x-admin.locales/>
            </div>
        </div>
    
        <div class="card-body">

            <div class="tab-content">

                @foreach (config('languages') as $lang)

                    <div class="tab-pane fade pt-3 {{($lang['code'] == App::getLocale() ? 'show active' : '') }} " id="lang-{{$lang['code']}}" role="tabpanel">

                        <x-admin.form.row.status :any="$data" :column="'status_'.$lang['code']"/>

                        <x-admin.form.row.langtext :any="$data" :$lang column="title" />

                        <x-admin.form.row.langtext :any="$data" :$lang column="title_sub" />

                        <div class="row-block">

                            <label class="label" for="content_{{$lang['code']}}">content</label>
                        
                            <div class="input">
                                <textarea 
                                rows="1"
                                class="form-control" 
                                name="content_{{$lang['code']}}" 
                                id="content_{{$lang['code']}}"
                                placeholder="">{{ old('content_'.$lang['code'], $data) }}</textarea>
                            </div>

                        </div>
                        

                    </div>

                @endforeach

            </div>
    
        </div>
    </div>

    <div class="card card-form">
        
    
        <div class="card-header">
            <h2>Buttons</h2>
        </div>
    
        <div class="card-body">
            <x-admin.widget.buttonmanager :any="$data" />
        </div>
    </div>
</div>

</form>

@endsection