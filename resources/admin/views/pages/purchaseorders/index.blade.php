@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button button="create" />
@endsection

@section('breadcrumbs')
    <li>Purchase orders</li>
@endsection

@section('filters', true)

@section('content')

    {!! $tables !!}

    


    <x-admin.panel type="overview" title="Purchase orders">
    
        <div class="overview"> 
            <div class="overview-header">

                <div class="row row-item">

                    <div class="col col-4">
                        <div class="row">
                            <div class="col-2 text-end">Id</div>
                            <div class="col-3 text-end">Number</div>
                            <div class="col-7">Supplier</div>
                        </div>
                    </div>

                    <div class="col-3">Description</div>
                    
                    <div class="col col-5">
                        <div class="row">
                            <div class="col-2 text-end">Total <small>(incl)</small></div>
                            <div class="col-4 text-end">Status</div>
                            <div class="col-2 text-end text-truncate">Order date</div>
                            <div class="col-2 text-end text-truncate">Delivery date</div>
                            <div class="col-2 controls">controls</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="overview-body">

                @foreach ($data as $row)

                    <div class="row row-item">
                        <div class="col col-4">
                            <div class="row">
                                <div class="col-2 text-end">{{$row->id}}</div>
                                <div class="col-3 text-end">{{$row->order_number}}</div>
                                <div class="col-7">{{$row->company->name}}</div>
                            </div>
                        </div>

                        <div class="col-3">{{$row->description}}</div>
                        
                        <div class="col col-5 text-end">
                            <div class="row">
                                <div class="col-2 text-end">@money($row->total_incl)</div>
                                <div class="col-4 text-end">@orderBadge($row->state)</div>
                                <div class="col-2 text-end">17-09-24</div>
                                <div class="col-2 text-end">01-04-24</div>
                                <div class="col-2 controls">

                                    <a href="{{route('pdf.purchaseorder', $row->id)}}" target="_blank" class="btn btn-sm btn-primary">
                                        <i class="fa-solid fa-file-pdf"></i>
                                    </a>

                                    <a href="{{route('admin.purchaseorders.edit', $row->id)}}" class="btn btn-sm btn-primary">
                                        <i class="fa-solid fa-pen-to-square"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                
                @endforeach
            </div>

            <div class="overview-empty alert alert-info mt-3">There are no purchaseorders yet.</div>
        </div>

    </x-admin.panel>

@endsection