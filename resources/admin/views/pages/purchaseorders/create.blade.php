@extends('admin::layouts.app')

@section('title', true)

@section('title_elements')
    @orderBadge($data->state)
@endsection

@section('flags')
    <x-admin::icon icon="lock" class="flag flag-is-locked" :if="$data->exists && $data->is_locked" />
@endsection

@section('controls')
    <x-admin.button button="back" />
    <x-admin.button button="destroy" data-name="{{$data->systemlabel}}" :if="$data->exists && !$data->is_locked" />
    <x-admin.button button="store" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.purchaseorders.index')}}">Purchase orders</a></li>
    <li>{{ !$data->exists ? 'Create' : $data->systemlabel }}</li>
@endsection

{{-- @section('tabs')
    <x-admin.tab name="general" label="General" active="true"  />
    <x-admin.tab name="mail" label="Mails"  />
@endsection --}}

{{-- @section('controls')


    @if (!$data->is_sent)
        <x-admin.button type="save" />
    @else
        <div class="dropdown">
            <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown"
                aria-expanded="false">
                Upload
            </button>
            <ul class="dropdown-menu">
                <li>
                    <a class="dropdown-item btn-document-upload" 
                        data-documentable-type="{{ $data->getMorphClass() }}"
                        data-documentable-id="{{ $data->id }}" 
                        data-documentable-description="{{ $data->description }}"
                        data-document-classification="{{ App\Enums\DocumentClassification::PURCHASE_ORDER_CONFIRMATION }}" data-type="single"
                        href="#">Confirmation</a>
                </li>
                <li><a class="dropdown-item" href="#">PackingSlip</a></li>
                <li><a class="dropdown-item" href="#">DeliveryNote</a></li>
                <li><a class="dropdown-item" href="#">Invoice</a></li>
            </ul>
        </div>
    @endif

@endsection --}}

@section('form')

     <x-admin.form
        :action="route('admin.purchaseorders.store')"
        class="form-purchaseorders" 
        method="POST"
        :bind="$data"
        x-data="{
            company_id: '{{ old('company_id', $data->company_id ?? null) }}'
        }"
    />

        <input type="hidden" id="related_items" value="{{ $items?->toJSON() }}" />
        <input type="hidden" id="items" name="items" value="{{ old('items', $data?->items?->toJSON()) }}" />

        @foreach (['delivery', 'invoice'] as $addressType)
            <input type="hidden" name="addresses[{{ $addressType }}][id]" value="{{ old('addresses.' . $addressType . '.id') }}" />
            <input type="hidden" name="addresses[{{ $addressType }}][company_id]" value="{{ old('addresses.' . $addressType . '.company_id') }}" />
            <input type="hidden" name="addresses[{{ $addressType }}][company]" value="{{ old('addresses.' . $addressType . '.company') }}" />
            <input type="hidden" name="addresses[{{ $addressType }}][salution]" value="{{ old('addresses.' . $addressType . '.salution') }}" />
            <input type="hidden" name="addresses[{{ $addressType }}][firstname]" value="{{ old('addresses.' . $addressType . '.firstname') }}" />
            <input type="hidden" name="addresses[{{ $addressType }}][lastname]" value="{{ old('addresses.' . $addressType . '.lastname') }}" />
            <input type="hidden" name="addresses[{{ $addressType }}][address][id]" value="{{ old('addresses.' . $addressType . '.address.id') }}" />
            <input type="hidden" name="addresses[{{ $addressType }}][address][street]" value="{{ old('addresses.' . $addressType . '.address.street') }}" />
            <input type="hidden" name="addresses[{{ $addressType }}][address][housenumber]" value="{{ old('addresses.' . $addressType . '.address.housenumber') }}" />
            <input type="hidden" name="addresses[{{ $addressType }}][address][addition]" value="{{ old('addresses.' . $addressType . '.address.addition') }}" />
            <input type="hidden" name="addresses[{{ $addressType }}][address][postalcode]" value="{{ old('addresses.' . $addressType . '.address.postalcode') }}" />
            <input type="hidden" name="addresses[{{ $addressType }}][address][city]" value="{{ old('addresses.' . $addressType . '.address.city') }}" />
            <input type="hidden" name="addresses[{{ $addressType }}][address][country_id]" value="{{ old('addresses.' . $addressType . '.address.country_id') }}" />
        @endforeach
@endsection

@section('content')

    <div class="data-addresses" data-value="{{ json_encode($addresses) }}"></div>

    <x-admin.page.row>
        
        <x-admin.page.col col="4">

            <x-admin.panel title="General information">

                <x-admin.form.row>
                    <x-admin.form.label col="4" label="Description" for="description" />
                    <x-admin.form.input name="description" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label col="4" label="Supplier" for="company_id" />
                    <x-admin.form.select 
                        name="company_id"
                        :options="$suppliers"
                        required="true" 
                        data-search="true"
                        x-model="company_id"
                    />
                </x-admin.form.row>

                <x-admin.form.row> {{-- sent_at --}}
                    <x-admin.form.label col="4" label="Order date" for="sent_at" />
                    <x-admin.form.input 
                        type="datepicker" 
                        data-startdatetoday="true"
                        name="sent_at" 
                    />
                </x-admin.form.row>

                <x-admin.form.row> {{-- delivery_at --}}
                    <x-admin.form.label col="4" label="Delivery date" for="delivery_at" />
                    <x-admin.form.input 
                        type="datepicker" 
                        data-startdatetoday="true"
                        name="delivery_at" 
                    />
                </x-admin.form.row>

            </x-admin.panel>

        </x-admin.page.col>
        <x-admin.page.col col="3">

            <x-admin.panel class="panel-supplier" title="Supplier information">

                <div class="supplier" x-show="company_id !== ''">
                    <address></address>

                    <div class="email d-flex">
                        <div class="label w-25 fw-bold">Email:</div>
                        <div class="value"></div>
                    </div>

                    <div class="phone d-flex">
                        <div class="label w-25 fw-bold">Phone:</div>
                        <div class="value"></div>
                    </div>
                </div>

                <div class="message message-rule alert alert-info my-0" x-show="company_id === ''">Please select a supplier first.</div>

            </x-admin.panel>

        </x-admin.page.col>
        <x-admin.page.col>

            <x-admin.panel title="Delivery address" name="panel-address-delivery"></x-admin.panel>

        </x-admin.page.col>

         <x-admin.page.col>
            <x-admin.panel title="Invoice address" name="panel-address-invoice"></x-admin.panel>
        </x-admin.page.col>

    </x-admin.page.row>

    

    <x-admin.panel type="overview" title="Items">

        <x-slot name="controls">
            <div 
                type="button" 
                class="btn btn-sm btn-primary btn-item-add" 
                x-show="company_id !== ''" 
                x-cloak 
                x-transition
            >Add item
            </div>
        </x-slot>

        <div class="overview" x-show="company_id !== ''">

            <div class="overview-header">
                <div class="row row-item">
                    <div class="col-2 code">Code</div>
                    <div class="col-3 name">Name</div>
                    <div class="col-1 quantity text-end">Quantity</div>
                    <div class="col-1 unit">Units</div>
                    <div class="col-2 price-unit text-end">Price per unit <sup>(excl)</sup></div>
                    <div class="col-2 price-total text-end">Price total <sup>(excl)</sup></div>
                    <div class="col-1 controls text-truncate">Controls</div>
                </div>
            </div>

            <div class="overview-body items-container">

            </div>

            <div class="overview-footer">

            </div>

            <div class="overview-empty alert alert-info mt-3">There are no items yet.</div>
            
        </div>

        <div class="overview-errors alert alert-info mt-3" x-show="company_id === ''">Please select a supplier first.</div>

    </x-admin.panel>

@endsection

@template('item-create', false)
<div 
    class="item-create" 
    data-name="item-create" 
    data-uniq-id="[[UNIQ_ID]]"
    x-data="{ 
        item_id: '[[ITEM_ID]]',
        item_variant_id: '[[ITEM_VARIANT_ID]]',
        item_related_id: ''
    }"
>

    <div class="message-container"></div>

    <input type="hidden" name="id" value="[[ID]]" />

    <x-admin.form.row>
        <x-admin.form.label for="item_id" label="Item + variant" required="true" />
        <x-admin.form.select 
            col=""
            name="item_id" 
            :options="[]" 
            required="true" 
            data-search="true"
            x-model="item_id"
        />
         <x-admin.form.select 
            col=""
            name="item_variant_id" 
            :options="[]" 
            required="true" 
            data-search="true"
            x-model="item_variant_id"
            class="no-selectpicker"
        />
    </x-admin.form.row>

    <x-admin.form.row x-show="item_id !== ''">
        <x-admin.form.label for="item_related_id" label="Related item" required="true" />
        <x-admin.form.select 
            name="item_related_id" 
            :options="$items" 
            optionLabelAttribute="systemlabel"
            required="true" 
            data-search="true"
            {{-- x-model="item_related_id" --}}
        />
    </x-admin.form.row>
   
    <div class="item-wrapper border-top pt-3 collapse collapsed">
        
        <x-admin.form.row class="d-none"> {{-- code & name --}}
            <x-admin.form.label for="code" label="Code & Name" required="true" />
            <x-admin.form.input col="" name="code" value="[[CODE]]" />
            <x-admin.form.input col="" name="name" value="[[NAME]]" />
        </x-admin.form.row>

       @foreach ($unitGroups as $unitGroup)

            @php
                $itemUnit = $data->units?->firstWhere('unit_group_id', $unitGroup->id);
            @endphp

            <x-admin.form.row class="row-unit-group" data-id="{{ $unitGroup->id }}">
                
                <x-admin.form.input {{-- units[][id] --}}
                    type="hidden" 
                    name="units[{{ $unitGroup->id }}][id]" 
                    value="{{ old('units.'.$unitGroup->id.'.id', $itemUnit?->id ?? Str::uniqId()) }}" 
                />
                
                <x-admin.form.input {{-- units[][unit_group_id] --}}
                    type="hidden" 
                    name="units[{{ $unitGroup->id }}][unit_group_id]" 
                    valueAttribute="null"
                    value="{{ $unitGroup->id }}"
                    data-value="{{$unitGroup->id}}" 
                />

                <x-admin.form.label 
                    for="units[{{ $unitGroup->id }}][unit_group_unit_id]" 
                    label="{{ $unitGroup->systemname }} unit" 
                />

                <x-admin.form.input {{-- units[][value] --}}
                    col="1" 
                    {{-- :bind="$itemUnit"  --}}
                    valueAttribute="value" 
                    type="number" 
                    name="units[{{ $unitGroup->id }}][value]" 
                    required="true" 
                    is-locked="{{ $data->exists && $data->is_locked }}"
                />
       

                <x-admin.form.select {{-- units[][unit_group_unit_id] --}}
                    col="2"
                    :options="$unitGroup->units->map(function($unit) {
                        return [
                            'id' => $unit?->id,
                            'systemname' => $unit?->unit?->systemname
                        ];
                    })" 
                    name="units[{{ $unitGroup->id }}][unit_group_unit_id]" 
                    :bind="null"
                    class="unit-select"
                    selected="{{ old('units.1.unit_group_unit_id', $data?->units?->firstWhere('unit_group_id', 1)?->unit_group_unit_id ) }}"
                    {{-- x-model="unit_group_{{ $unitGroup->value }}_unit_id" --}}
                    is-locked="{{ $data->exists && $data->is_locked }}"
                    required="true"
                />

                <x-admin.form.col class="col-3 relations mb-n3">

                    @template('item_unit_relation', false)
                    <x-admin.form.row  
                        data-id="[[ID]]" 
                        class="row-relation"
                        data-relation-name="[[SYSTEMLABEL]]"
                        x-data="{ 
                            model: '[[MODEL]]' 
                        }"
                    >
                        <x-admin.form.input 
                            type="hidden" 
                            name="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][id]" 
                            value="[[ID]]" 
                        />

                        <x-admin.form.input 
                            type="hidden" 
                            class="unit_compositional_id"
                            name="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][unit_compositional_id]" 
                            value="[[UNIT_COMPOSITIONAL_ID]]" 
                        />

                        <x-admin.form.label 
                            col="4" 
                            for="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][model]" 
                            label="[[SYSTEMLABEL]]" 
                        />

                        <x-admin.form.select 
                            col="8"
                            name="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][model]" 
                            x-model="model"
                            data-live-search="true"
                            is-locked="{{ $data->exists && $data->is_locked }}"
                        >
                            @template('item_unit_relation_options')
                                @template('item_unit_relation_option')
                                <option value="[[VALUE]]">[[TITLE]]</option>
                                @endtemplate
                            @endtemplate                                
                        </x-admin.form.select>
         
                    </x-admin.form.row>
                    @endtemplate

                    @php
                        $relations = [];
                        $oldRelations = collect(old('units.'.$unitGroup->id.'.relations', []));

                        if ($unitGroup->value == 'weight') {

                            $relation = $itemUnit?->relations?->first();

                            $relationOld = $oldRelations?->first();

                            $relatable = $relation ?? $relationOld['model'] ?? null;

                            $relations[] = [
                                'id' => 
                                    $relation?->id ?? 
                                    $relationOld?->id ?? 
                                    Str::uniqId(),
                                'unit_group_id' => $unitGroup->id,
                                'unit_compositional_id' => null,
                                'systemlabel' => 'per',
                                'item_unit_relation_options' => $unitGroupWeightUnits->map(function($unit) {
                                    return [
                                        'value' => 'Unit::'.$unit->id,
                                        'title' => $unit->systemlabel
                                    ];
                                }),
                                'model' => $relatable instanceof \App\Models\BaseModel
                                    ? $relatable->relatable_type.'::'.$relatable->relatable_id
                                    : $relatable
                            ];

                        } else {

                            $unitCompositionals = $itemUnit?->unitGroupUnit?->unit?->compositionals;

                            if (count($oldRelation = $oldRelations?->pluck('unit_compositional_id')) > 0) {

                                $unitCompositionals = $unitGroup->units->map(function($unitGroupUnit) use ($oldRelation) {

                                    return $unitGroupUnit->unit?->compositionals?->whereIn('id', $oldRelation)?->count() > 0
                                        ? $unitGroupUnit->unit?->compositionals
                                        : null
                                    ;

                                })?->filter()?->first();
                            }

                            $relations = ($unitCompositionals?->sortBy('id')?->map(function($compositional) use ($unitGroup, $itemUnit, $properties, $oldRelations) {

                                $property = $properties?->firstWhere(function($property) use ($compositional) {
                                    return 
                                        $property->system_context === SystemContext::UNIT && 
                                        $property->system_context_unit_id === $compositional->unit_id &&
                                        $property->system_context_unit_compositional_id === $compositional->id
                                    ;
                                });

                                $relation = $itemUnit?->relations
                                    ->whereIn('relatable_id', $property?->values?->pluck('id'))
                                    ?->first()
                                ;

                                $relationOld = $oldRelations?->firstWhere('unit_compositional_id', $compositional->id);

                                $relatable = $relation ?? $relationOld['model'] ?? null;
                               
                                
                                $options = $property?->values?->map(function($propertyValue) {
                                    return [
                                        'value' => 'PropertyValue::'.$propertyValue->id,
                                        'title' => $propertyValue->systemlabel
                                    ];
                                });

                                if (is_null($options) ||
                                    $options->count() === 0) {
                                    return null;
                                }

                                return [
                                    'id' =>
                                        $relation?->id ??
                                        $relationOld?->id ??
                                        Str::uniqId(),
                                    'unit_group_id' => $unitGroup->id,
                                    'unit_compositional_id' => $compositional->id,
                                    'systemlabel' => $compositional->systemname,
                                    'item_unit_relation_options' => $options,
                                    'model' => $relatable instanceof \App\Models\BaseModel
                                        ? $relatable->relatable_type.'::'.$relatable->relatable_id
                                        : $relatable
                                ];
                            }) ?? collect([]))->filter();

                            Log::debug($relations->toArray());
                        }
                    @endphp

                    @foreach ($relations as $relation)                                

                        @capture('item_unit_relation_options')
                            @foreach ($relation['item_unit_relation_options'] as $option)
                                @usetemplate('item_unit_relation_option', $option)
                            @endforeach
                        @endcapture

                        @php
                            $relation['item_unit_relation_options'] = $item_unit_relation_options;
                        @endphp
                            
                        @usetemplate('item_unit_relation', $relation)

                    @endforeach

                </x-admin.form.col>

            </x-admin.form.row>

        @endforeach


        <x-admin.form.row>
            <x-admin.form.label for="units[1][pricing]" label="Price per" required="true" />
            <x-admin.form.col class="col col-md-4 col-lg-3 col-xl-2">
                <x-admin.form.input
                    type="money" 
                    class="pricing_unit_warehouse"
                    name="units[{{ $unitGroup->id }}][pricing]" 
                    required="true" 
                />
            </x-admin.form.col>
                    
        </x-admin.form.row>

        <x-admin.form.row>
            <x-admin.form.label for="units[2][sales]" label="Price per" required="true" />
            <x-admin.form.col class="col col-md-4 col-lg-3 col-xl-2">
                <x-admin.form.input
                    type="money" 
                    class="pricing_unit_sales"
                    name="units[{{ $unitGroup->id }}][sales]" 
                    required="true" 
                />
            </x-admin.form.col>         
        </x-admin.form.row>

        <x-admin.form.row>
            <x-admin.form.label for="units[3][pricing]" label="Price per" required="true" />
            <x-admin.form.col class="col col-md-4 col-lg-3 col-xl-2">
                <x-admin.form.input
                    type="money" 
                    class="pricing_unit_pricing"
                    name="units[{{ $unitGroup->id }}][pricing]" 
                    required="true" 
                />
            </x-admin.form.col>         
        </x-admin.form.row>

         <x-admin.form.row>
            <x-admin.form.label for="pricing" label="Price total" required="true" />
            <x-admin.form.col class="col col-md-4 col-lg-3 col-xl-2">
                <x-admin.form.input
                    type="money" 
                    class="pricing_total"
                    name="price_total" 
                    required="true" 
                />
            </x-admin.form.col>         
        </x-admin.form.row>
    
    </div>

</div>
@endtemplate



    {{-- @section('templates')

        <div class="template module-document-uploader" data-name="document-upload">

            <div class="uploader-container">

                <div class="row-wrapper" data-name="document-type">
                    <div class="row">
                        <label class="label col-4 required" for="document_type">Document type</label>
                        <div class="col">
                            <select name="document_type" id="document_type" class="form-control">
                                <option value="">Nothing selected</option>
                                @foreach (App\Enums\DocumentClassification::cases() as $item)
                                    <option value="{{ $item->value }}">{{ $item->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row-wrapper" data-name="documentable">
                    <div class="row">
                        <label class="label col-4 required" for="documentable">Attach to</label>
                        <div class="col documentable"></div>
                        <input type="hidden" name="documentable_type" value="[[DOCUMENTABLE_TYPE]]" />
                        <input type="hidden" name="documentable_id" value="[[DOCUMENTABLE_ID]]" />
                    </div>
                </div>

                <div class="row">
                    <div class="col">

                        <div class="uploader" data-type="[[TYPE]]">

                            <div class="dropzone">
                                <i class="fa-solid fa-upload"></i>
                                <label for="uploader-file">Drag & drop to upload</label>
                                <button type="button" class="btn btn-sm btn-primary btn-browse-file">Browse file</button>
                            </div>

                            <input type="file" name="uploader_file" id="uploader-file" />

                        </div>

                    </div>
                </div>

                <div class="file-uploader collapse collapsed">

                    <div class="progress-box state-uploading"> 
                        <svg class="ring" width="100%" height="100%">
                            <circle class="ring-back" stroke="#e7e7e7" stroke-width="7" fill="transparent" r="45%" cx="50%" cy="50%"></circle>
                            <circle class="ring-circle" stroke="#fa6819" stroke-width="7" fill="transparent" r="45%" cx="50%" cy="50%" style="stroke-dasharray: 326.726, 326.726; stroke-dashoffset: 0;"></circle>
                        </svg>
                        <div class="percentage"></div>
                        <div class="speed"></div>
                        <div class="status-text">Ready</div>
                    </div>



                </div>

            </div>

        </div>

        <div class="template" data-name="item-totals">

            <div class="row row-item fw-bold py-1 border-top">
                <div class="col-2 offset-8 text-end">Total <small>(excl)</small>:</div>
                <div class="col-1 text-end total-items">[[TOTAL_EXCL]]</div>
                <div class="col-1">&nbsp;</div>
            </div>
            <div class="row row-item fw-bold py-1">
                <div class="col-2 offset-8 text-end">Shipping costs <small>(excl)</small>:</div>
                <div class="col-1 text-end total-items">[[TOTAL_SHIPPING]]</div>
                <div class="col-1">&nbsp;</div>
            </div>
            <div class="row row-item fw-bold py-1">
                <div class="col-2 offset-8 text-end">Tax <small>([[TAX_RATE]])</small>:</div>
                <div class="col-1 text-end total-tax">[[TOTAL_TAX]]</div>
                <div class="col-1">&nbsp;</div>
            </div>
            <div class="row row-item fw-bold py-1 mb-3">
                <div class="col-2 offset-8 text-end border-top">Total <small>(incl)</small>:</div>
                <div class="col-1 text-end total-tax border-top">[[TOTAL_INCL]]</div>
                <div class="col-1">&nbsp;</div>
            </div>

        </div>

        <div class="template row row-item" data-name="item-row" data-uniq-id="[[UNIQ_ID]]">

            <div class="col-2 code">[[CODE]]</div>
            <div class="col-3 name">[[NAME]]</div>
            <div class="col-1 quantity text-end">[[QUANTITY]]</div>
            <div class="col-1 unit">[[UNIT]]</div>
            <div class="col-2 price-unit text-end">[[PRICE_UNIT]]</div>
            <div class="col-2 price-total text-end">[[PRICE_TOTAL]]</div>
            <div class="col-1 controls">
                <button type="button" class="btn btn-sm btn-primary btn-item-edit">
                    <i class="fa-regular fa-pen-to-square"></i>
                </button>

                <button type="button" class="btn btn-sm btn-primary btn-item-delete">
                    <i class="fas fa-trash"></i>
                </button>
            </div>

        </div>

        

    @endsection --}}
