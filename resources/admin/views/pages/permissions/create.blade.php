@extends('admin::layouts.app')

@section('title', empty($data->id) ? 'Create' : 'Edit ' . $data->name)

@section('controls')
    <x-admin.button type="back" />
    <x-admin.button type="save" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.'.App::get('module').'.index')}}">Permissions</a></li>
    <li>{{ empty($data->id) ? 'Add' : $data->name }}</li>
@endsection

@section('form')
    <x-admin.form />
@endsection

@section('content')

    <div class="row">
        <div class="col-12 col-xl">

            <x-admin.panel title="General information">
                <x-admin.form.row.input-text column="name" label="Systemname" required="true" />
            </x-admin.panel>

        </div>
        <div class="col-12 col-xl">

            <x-admin.panel type="locales" :any="$data">
                @verbatim
                    <x-admin.form.row.input-text :$locale column="description" label="Description"  />
                @endverbatim
            </x-admin.panel>

        </div>
    </div>

    <x-admin.panel title="Settings">

        @foreach ($settings as $setting)

            <x-admin.form.row.input-text 
                column="settings[{{ $setting->id }}]" 
                label="{{ $setting->systemname }}" 
                value="{{ $data->relatedSettings->where('setting_id', $setting->id)->first()?->value ?? $setting->value }}"
                value-original="{{ $setting->value }}"
                type="{{ $setting->type == \App\Enums\InputType::MONEY ? 'money' : 'text' }}"                 
            />

        @endforeach

    </x-admin.panel>

@endsection 