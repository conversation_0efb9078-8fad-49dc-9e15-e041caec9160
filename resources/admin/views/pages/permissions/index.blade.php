@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button type="create" />
@endsection

@section('breadcrumbs')
    <li>Permissions</li>
@endsection

@section('content')

    <x-admin.panel type="overview" title="Roles">
    
        <div class="overview-header">

            <div class="row row-item">
                <div class="col">Name</div>
                <div class="col-2 text-end">Admins</div>
                <div class="col-2 controls">controls</div>
            </div>
        </div>

        <div class="overview-body">

            @foreach ($data as $row)

                <div class="row row-item">
                    <div class="col">@name($row, 'description')</div>
                    <div class="col-2 text-end">{{ $row->admins_count }}</div>
                    <div class="col-2 controls">
                        <a href="{{route('admin.permissions.edit', $row->id)}}" class="btn btn-sm btn-primary">
                            <i class="fa-solid fa-pen-to-square"></i>
                        </a>
                    </div>
                </div>
            
            @endforeach
        </div>

    </x-admin.panel>

@endsection