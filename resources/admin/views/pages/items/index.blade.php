@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button button="module" :route="route('admin.itemgroups.index')" label="Groups" />
    <x-admin.button button="create" />
@endsection

@section('breadcrumbs')
    <li>Items</li>
@endsection

@section('filters', true)

@section('content')

    <x-admin.panel type="overview" title="Items">

        <div class="overview">
            <div class="overview-header">
                <div class="row row-item">
                    <div class="col-md-2 col-xl-2"><x-admin.link.sortable label="Code" attribute="code" /></div>
                    <div class="col"><x-admin.link.sortable label="Systemname" attribute="systemname" /></div>
                    <div class="col-1"><x-admin.link.sortable label="Group" attribute="group.systemname" /></div>
                    <div class="col-6">
                        <div class="row gx-1">
                            <div class="col-2"><x-admin.link.sortable label="Type" attribute="type" /></div>
                            <div class="col"><x-admin.link.sortable label="Warehouse unit" attribute="units.warehouse.groupunit.systemname" /></div>
                            <div class="col"><x-admin.link.sortable label="Sales unit" attribute="units.sales.groupunit.systemname" /></div>
                            <div class="col-3">
                                <div class="row gx-1">
                                    <div class="col-7 text-end text-truncate"><x-admin.link.sortable label="Related items" attribute="related_by_count" /></div>
                                    <div class="col-5 text-end text-truncate"><x-admin.link.sortable label="Products" attribute="products_count" /></div>
                                </div>
                            </div>
                            <div class="col-2 controls">Actions</div>
                        </div>
                    </div>

                </div>
            </div>

            <div class="overview-body">
                @foreach ($data as $row)
                    <div class="row row-item">

                        <div class="col-md-2 col-xl-2 fw-bold"><a href="{{ route('admin.items.edit', $row->id) }}">{{$row->code}}</a></div>
                        <div class="col fw-bold text-truncate"><a href="{{ route('admin.items.edit', $row->id) }}">{{ $row->systemname }}</a></div>
                        <div class="col-1"><a href="{{ route('admin.itemgroups.edit', $row->group->id) }}">{{ $row->group->systemname }}</a></div>

                        <div class="col-6">
                            <div class="row gx-1">
                                <div class="col-2">{{ $row->type }}</div>
                                <div class="col text-truncate">{{ $row->units?->firstWhere('unit_group_id', 1)?->unitGroupUnit?->unit?->systemname ?? '-' }}</div>
                                <div class="col text-truncate">{{ $row->units?->firstWhere('unit_group_id', 2)?->unitGroupUnit?->unit?->systemname ?? '-' }}</div>
                                <div class="col-3">
                                    <div class="row gx-1">
                                        <div class="col-7 text-end">{{ $row->related_by_count }}</div>
                                        <div class="col-5 text-end">{{ $row->products_count }}</div>
                                    </div>
                                </div>
                                <div class="col-2 controls">
                                    <x-admin.button group="overview" button="edit" :parameters="$row->id" />
                                    <x-admin.button group="overview" button="destroy" :parameters="$row->id" data-name="{{$row->systemname}}" :disabled="$row->is_locked" />
                                </div>

                            </div>
                        </div>                        
                    </div>
                @endforeach
            </div>

            <div class="overview-empty alert alert-info mt-3">There are no items yet.</div>
        </div>

    </x-admin.panel>

@endsection