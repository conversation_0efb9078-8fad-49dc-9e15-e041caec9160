@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
@endsection

@section('breadcrumbs')
    <li>Mails</li>
@endsection

@section('filters', false)

@section('content')

    <x-admin.panel type="overview" title="Mails">

        <div class="overview">
            <div class="overview-header">
                <div class="row row-item">
                    <div class="col-3"> 
                        <div class="row"> 
                            <div class="col-6 pr-1"><x-admin.link.sortable label="Company" attribute="company_id" /></div> 
                            <div class="col-6 pr-1"><x-admin.link.sortable label="Template" attribute="template" /></div> 
                        </div> 
                    </div> 
                    <div class="col"><x-admin.link.sortable label="Subject" attribute="subject" /></div> 
                    <div class="col-2 pr-1">Reciever(s)</div> 
                    <div class="col-3"> 
                        <div class="row"> 
                            <div class="col-5 text-right pr-1"><x-admin.link.sortable label="Sent at" attribute="sent_at" /></div> 
                            <div class="col-5 text-right px-1 "><x-admin.link.sortable label="Opened at" attribute="opened_at" /></div> 
                            <div class="col-2 text-right pl-1">Actions</div> 
                        </div> 
                    </div> 
                </div>
            </div>

            <div class="overview-body">
                @foreach ($data as $row)

                    <div class="row row-item">
                        <div class="col-3"> 
                            <div class="row"> 
                                <div class="col-6 pr-1"><x-admin.link.sortable label="Company" attribute="company_id" /></div> 
                                <div class="col-6 pr-1"><x-admin.link.sortable label="Template" attribute="template" /></div> 
                            </div> 
                        </div> 
                        <div class="col"><x-admin.link.sortable label="Subject" attribute="subject" /></div> 
                        <div class="col-2 pr-1">Reciever(s)</div> 
                        <div class="col-3"> 
                            <div class="row"> 
                                <div class="col-5 text-right pr-1"><x-admin.link.sortable label="Sent at" attribute="sent_at" /></div> 
                                <div class="col-5 text-right px-1 "><x-admin.link.sortable label="Opened at" attribute="opened_at" /></div> 
                                <div class="col-2 text-right pl-1">Actions</div> 
                            </div> 
                        </div> 
                    </div>


                    <div class="row row-item">

                        <div class="col-md-2 col-xl-2 fw-bold"><a href="{{ route('admin.items.edit', $row->id) }}">{{$row->code}}</a></div>
                        <div class="col fw-bold text-truncate"><a href="{{ route('admin.items.edit', $row->id) }}">{{ $row->systemname }}</a></div>
                        <div class="col-1"><a href="{{ route('admin.itemgroups.edit', $row->group->id) }}">{{ $row->group->systemname }}</a></div>

                        <div class="col-6">
                            <div class="row gx-1">
                                <div class="col-2">{{ $row->type }}</div>
                                <div class="col text-truncate">{{ $row->units?->firstWhere('unit_group_id', 1)?->unitGroupUnit?->unit?->systemname ?? '-' }}</div>
                                <div class="col text-truncate">{{ $row->units?->firstWhere('unit_group_id', 2)?->unitGroupUnit?->unit?->systemname ?? '-' }}</div>
                                <div class="col-3">
                                    <div class="row gx-1">
                                        <div class="col-7 text-end">{{ $row->related_by_count }}</div>
                                        <div class="col-5 text-end">{{ $row->products_count }}</div>
                                    </div>
                                </div>
                                <div class="col-2 controls">
                                    <x-admin.button group="overview" button="edit" :parameters="$row->id" />
                                    <x-admin.button group="overview" button="destroy" :parameters="$row->id" data-name="{{$row->systemname}}" :disabled="$row->is_locked" />
                                </div>

                            </div>
                        </div>                        
                    </div>
                @endforeach
            </div>

            <div class="overview-empty alert alert-info mt-3">There are no mails yet.</div>
        </div>

    </x-admin.panel>



<div class="content-body">

    <h1 class="title">Overview</h1>
    
    <x-admin.alert/>

    <div class="card card-overview">

        <div class="card-header">
            <div>
                <h2>Mails</h2>
            </div>
        </div>

        <div class="card-body ">

            <div class="overview-header">

                <div class="row row-item">
                    
                    <div class="col col-2">Companies</div>
                    <div class="col col-2">Template</div>
                    <div class="col col">Subject</div>
                    <div class="col col-2">Reciever</div>
                    <div class="col col-2">
                        <div class="row">
                            <div class="col-8 text-end">Date</div>    
                            <div class="col-4 text-end">View</div>    
                        </div>
                    </div>
                    
                </div>

            </div>

            <div class="overview-body">
                @foreach ($data as $row)
                
                    <div class="row row-item">

                        <div class="col col-2">

                            @foreach($row->recipients->where('type' , App\Enums\LogMailRecipient\Type::TO) as $recipient)

                                @foreach ($recipient->users as $user)
                                    @foreach ($user->companies as $company)
                                        <div>
                                            <a href="{{ route($app.'.companies.edit', $company->id) }}">
                                                {{$company->name}}
                                            </a>
                                        </div>
                                    @endforeach
                                @endforeach
                            @endforeach
                        </div>
                        <div class="col col-2">{{$row->template}}</div>
                        <div class="col col">{{$row->subject}}</div>
                        <div class="col col-2">
                            @foreach($row->recipients->where('type' , App\Enums\LogMailRecipient\Type::TO) as $recipient)
                                <div>{{$recipient->mail}}</div>
                            @endforeach
                        </div>
                        <div class="col col-2">
                            <div class="row">
                                <div class="col-8 text-end">{{$row->created_at}}</div>    
                                <div class="col-4 text-end">
                                                
                                        <a href="{{ route('admin.'.App::get('module').'.show', $row->id) }}">
                                            <i class="fa-regular fa-eye"></i>
                                        </a>

                                </div>    
                            </div>
                        </div>
                        
                    </div>

                @endforeach
            </div>

        </div>
        
    </div>
</div>
@endsection