@extends('admin::layouts.app')
@section('content')

<nav class="content-header">
    
    <div class="breadcrumbs">
        <ol>
            <li><a href="{{ route('admin.'.App::get('module').'.index')}}">Mails</a></li>
            <li>{{$data->subject}}</li>
        </ol>
    </div>
</nav>

<div class="content-body">
    <h1 class="pt-3">{{$data->subject}}</h1>
    <x-admin.alert/>

    <div class="row">
        <div class="col-12 col-md-8">
            <div class="card mt-5">
                <div class="card-header bg-body-tertiary border-bottom">
                    <h2 class="card-title col-auto mb-0">Content</h2>
                </div>
                <div class="card-body">
                    <div class="row">
                        <iframe style="width: 100%;min-height: 600px;" class="border" src="data:text/html,<?=Temp::encodeURIComponent($data['content']);?>"></iframe>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-12 col-md-4">
            <div class="card mt-5">
                <div class="card-header bg-body-tertiary border-bottom">
                    <h2 class="card-title col-auto mb-0">Reciptients</h2>
                </div>
                <div class="card-body">
                    <div class="row">

                        <div class="col-3 fw-bold"> Type</div>
                        <div class="col-4 fw-bold"> Name</div>
                        <div class="col-5 fw-bold"> Email</div>

                        @foreach ($data->recipients as $recipient)
                            
                            <div class="col-3">{{ ucfirst(strtolower($recipient->type->value == '' ? '-' : $recipient->type->value)) }}</div>

                            <div class="col-4"> {{ $recipient->name == '' ? '-' : $recipient->name}}</div>

                            <a href="mailto:{{$recipient->mail}}" class="col-5"> {{ $recipient->mail == '' ? '-' : $recipient->mail}}</a>
                                
                        @endforeach
                    </div>
                </div>
            </div>

            @if ($data->attachments->count() > 0)

                <div class="card mt-5">
                    <div class="card-header bg-body-tertiary border-bottom">
                        <h2 class="card-title col-auto mb-0">Attachments</h2>
                    </div>
                    <div class="card-body">
                        @foreach ($data->attachments as $attachment)
                            <a href="{{ URL::asset('img/'.$attachment->filename) }}">{{$attachment->filename}}</a>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
            
    </div>
    
</div>
@endsection