@extends('admin::layouts.app')
@section('content')

<form action="{{ route('admin.'.App::get('module').'.store') }}" method="POST" enctype="multipart/form-data">
<input type="hidden" name="id" value="{{ isset($data) ? $data->id : '' }}">
<input type="hidden" name="locale_current" value="{{ app()->getLocale() }}">
@csrf

<nav class="content-header">

    <div class="breadcrumbs">
        <ol>
            <li><a href="{{route('admin.'.App::get('module').'.index')}}">Usps</a></li>
            <li>{{ empty($data->id) ? 'Add' : $data->systemname }}</li>
        </ol>
    </div>

    <div class="controls">

        <x-admin.button type="back" />

        <x-admin.button type="save" />
    </div>
</nav>

<div class="content-body">

    <h1 class="title">{{ empty($data->id) ? 'Create Usps' : 'Edit ' . $data->systemname }}</h1>
    
    <x-admin.alert/>

    <div class="card card-form">
        <div class="card-header">
            <div>
                <h2>General information</h2>
            </div>
        </div>

        <div class="card-body">
            
            <x-admin.form.row.systemname :any="$data" />

            <div class="row-block">
                 
                <label class="label label-required" for="systemname">Icon</label>

                <div class="input">

                    <x-admin.widget.iconpicker :any="$data"/>
                </div>
            </div>
            
            <x-admin.form.row.status :any="$data" column="status"/>
            <x-admin.form.row.toggle :any="$data" column="is_menu_top" />
            
        </div>
    </div>

    <div class="card card-form">
        
    
        <div class="card-header">
            <h2>Language specific</h2>
            <div class="controls">
                <x-admin.locales/>
            </div>
        </div>
    
        <div class="card-body">
            <div class="tab-content">
                @foreach (config('languages') as $lang)

                    <div class="tab-pane fade pt-3 {{($lang['code'] == App::getLocale() ? 'show active' : '') }} " id="lang-{{$lang['code']}}" role="tabpanel">

                        <x-admin.form.row.status :any="$data" :column="'status_'.$lang['code']"/>

                        <x-admin.form.row.langtext :any="$data" :$lang column="title" :required="1" />

                        <x-admin.form.row.langtext :any="$data" :$lang column="title_sub" />
                        
                        <x-admin.form.row.langtext :any="$data" :$lang column="text" />

                    </div>
                @endforeach
            </div>
    
        </div>
    </div>
</div>


</form>
@endsection