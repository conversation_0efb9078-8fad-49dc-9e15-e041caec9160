@extends('admin::layouts.app')
@section('content')

<nav class="content-header">
    <div class="breadcrumbs">
        <ol>
            <li>Usps</li>
        </ol>
    </div>
    <div class="controls">
        
        <x-admin.button type="create" />
    </div>
</nav>

<div class="content-body">

    <h1 class="title">Overview</h1>
    
    <x-admin.alert/>

    <div class="card card-overview">

        <div class="card-header">
            <div>
                <h2>Usps</h2>
            </div>
        </div>

        <div class="card-body ">

            <div class="overview-header">

                <div class="row row-item">
                    <div class="col col-1 text-start">Icon</div> 
                    <div class="col">Title</div> 
                    <div class="col col-2 text-center">Top menu</div> 
                    <div class="col col-2 text-center">Footer</div> 
                    <div class="col col-1 text-center">Products</div> 
                    <div class="col col-2 text-center">Status</div> 
                    <div class="col col-2 text-end">Options</div>
                </div>
            </div>

            <div class="overview-body">

                @foreach ($data as $row)

                    <div class="row row-item">

                        <div class="col col-1 "> 
                            <i class="fas fa-bars me-3 sorting-handle ui-sortable-handle"></i> 

                            @if ($row->icon != '' )
                                <i class="fa fa-{{ $row->icon}}"></i> 
                            @endif

                        </div> 

                        <div class="col fw-bold">
                            <a href="{{ route('admin.'.App::get('module').'.edit', $row->id) }}">{{ $row->title_nl}}</a>
                        </div> 
                        
                        <div class="col col-2 text-center"></div> 
                        
                        <div class="col col-2 text-center"></div> 

                        <div class="col col-1 text-center"></div> 
                        <div class="col-4"> 
                            <div class="row"> 
                                <div class="col-6 text-center">
                                    <span class="flag-icon flag-icon-en" title="en"></span>&nbsp;
                                    <span class="flag-icon flag-icon-nl" title="nl"></span>&nbsp;
                                    <span class="flag-icon flag-icon-de" title="de"></span>
                                </div> 

                                <div class="col-6 text-end ps-0"> 
                                    <a href="{{ route('admin.'.App::get('module').'.edit', $row->id) }}">
                                        <i class="fa-regular fa-pen-to-square"></i>
                                    </a>
        

                                    <a 
                                        type="button" 
                                        class="btn-delete" 
                                        name="delete" 
                                        href="#" 
                                        data-href="{{ route('admin.'.App::get('module').'.destroy', $row->id) }}"
                                        data-name="{{ $row->systemname }}"
                                        data-text="">
                            
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div> 

                            </div> 
                        </div>

                    </div>

                 @endforeach

            </div>

        </div>
        
    </div>
</div>

@endsection