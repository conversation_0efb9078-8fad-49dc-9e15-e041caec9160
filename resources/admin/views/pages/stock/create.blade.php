@extends('admin::layouts.app')
@section('content')

<x-admin.form :any="$data">

<nav class="content-header">

    <div class="breadcrumbs">
        <ol>
            <li><a href="{{route('admin.'.App::get('module').'.index')}}">Items</a></li>
            <li>{{ empty($data->id) ? 'Add' : $data->name }}</li>
        </ol>
    </div>

    <div class="controls">
        <x-admin.button type="back" />
        <x-admin.button type="save" />
    </div>
</nav>


<div class="content-body">

    <h1 class="title">{{ empty($data->id) ? 'Create' : 'Edit ' . $data->name }}</h1>

    <x-admin.alert/>

    <x-admin.panel title="General information">
        <x-admin.form.row.systemname :any="$data" />
        <x-admin.form.row.input-text :any="$data" column="description" Label="Description" />
        <x-admin.form.row.selectbox 
            :any="$data" 
            :items="$categories" 
            class="ledger-category"
            column="category_id" 
            label="Category" 
            required="true" 
            optionDataAttributes="ledger_account_*_id" 
        />

    </x-admin.panel>

    <x-admin.panel title="General Ledger Accounts" name="panel-ledger-accounts">
        <div class="row row-cols-1 row-cols-xxl-2">
            {{-- <div class="col"><x-admin.form.row.ledger-account :any="$data" :accounts="$ledgerAccounts" column="turnover" label="Turnover account" /></div>
            <div class="col"><x-admin.form.row.ledger-account :any="$data" :accounts="$ledgerAccounts" column="stock" label="Stock account" /></div>
            <div class="col"><x-admin.form.row.ledger-account :any="$data" :accounts="$ledgerAccounts" column="cost" label="Costs account" /></div>
            <div class="col"><x-admin.form.row.ledger-account :any="$data" :accounts="$ledgerAccounts" column="distribution" label="Distribution account" /></div>
            <div class="col"><x-admin.form.row.ledger-account :any="$data" :accounts="$ledgerAccounts" column="discount" label="Discount account" /></div>
            <div class="col"><x-admin.form.row.ledger-account :any="$data" :accounts="$ledgerAccounts" column="dealer_discount" label="Supplier discount account" /></div> --}}
        </div>
    </x-admin-panel>

</div>

</x-admin.form>

@endsection 