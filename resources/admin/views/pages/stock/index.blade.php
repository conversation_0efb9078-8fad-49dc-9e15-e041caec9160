@extends('admin::layouts.app')
@section('content')


<nav class="content-header">

    <div class="breadcrumbs">
        <ol>
            <li><a href="{{route('admin.'.App::get('module').'.index')}}">Stock</a></li>
        </ol>
    </div>

    <div class="controls">
        <x-admin.button type="create" />
    </div>
</nav>

<div class="content-body">

    <h1 class="title">Overview</h1>

    <x-admin.alert/>

    <div class="card card-overview">

        <div class="card-header">
            <div>
                <h2>Items</h2>
            </div>
        </div>

        <div class="card-body ">

            <div class="overview-header">

                <div class="row row-item">
                    <div class="col-2">Systemname</div>
                    <div class="col">Description</div>
                    <div class="col-2">Products</div>
                    <div class="col-2 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">

                @foreach ($data as $row)
                    <div class="row row-item">

                        <div class="col-2 fw-bold">
                            <a href="{{ route('admin.'.App::get('module').'.edit', $row->id) }}">
                                {{$row->systemname}}
                            </a>
                        </div>

                        <div class="col  fw-bold text-truncate">
                            <a href="{{ route('admin.'.App::get('module').'.edit', $row->id) }}">
                                {{ $row->description }}
                            </a>
                        </div>
                       
                        <div class="col-2">{{ $row->products_count }}</div>
                        
                        <div class="col-2 controls">

                            <a href="{{ route('admin.'.App::get('module').'.edit', $row->id) }}">
                                <i class="fa-regular fa-pen-to-square"></i>
                            </a>

                            <a 
                                type="button" 
                                class="btn-delete" 
                                name="delete" 
                                href="#" 
                                data-href="{{ route('admin.'.App::get('module').'.destroy', $row->id) }}"
                                data-name="{{ $row->systemname }}"
                                data-text="
                                    {{ $row->products_count > 0 ? 'there are currently ' . $row->products_count . ' user(s) attached' : '' }}
                                ">

                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>

        </div>
        
    </div>
</div>
@endsection