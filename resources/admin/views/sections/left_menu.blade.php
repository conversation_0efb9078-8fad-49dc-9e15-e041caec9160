@php

$data = [
    [
        'title' => 'CRM',
        'childs' => [
            [
                'title' => 'Resellers',
                'url' => 'resellers',
                'icon' => 'truck-field',
                'is_active' => false
            ],[
                'title' => 'Suppliers',
                'url' => 'suppliers',
                'icon' => 'truck-field',
                'is_active' => true,
                'childs' => [
                    [
                        'title' => 'Items',
                        'url' => 'suppliers/items',
                        'is_active' => true,
                    ]
                ]
            ], [
                'title' => 'Customers',
                'url' => 'customers',
                'icon' => 'building',
                'is_active' => false
            ], [
                'title' => 'Contacts',
                'url' => 'contacts',
                'icon' => 'users',
                'is_active' => false
            ],
        ]
    ],
    [
        'title' => 'Warehouse',
        'childs' => [
            [
                'title' => 'Warehouses',
                'url' => 'warehouses',
                'icon' => 'warehouse',
                'childs' => [
                    [
                        'title' => 'Locations',
                        'url' => 'warehouseslocations'
                    ], 
                    [
                        'title' => 'Stock',
                        'url' => 'warehousesstock'
                    ]
                ]
            ], 
            [
                'title' => 'Items',
                'url' => 'items',
                'icon' => 'boxes-stacked',
                'childs' => [
                    [
                        'title' => 'Groups',
                        'url' => 'itemgroups'
                    ]
                ]
            ], 
            [
                'title' => 'Purchase orders',
                'url' => 'purchaseorders',
                'icon' => 'user',
                'is_active' => false
            ],
        ]
    ],
    [
        'title' => 'Financial',
        'childs' => [
            [
                'title' => 'Ledger',
                'url' => 'ledger',
                'icon' => 'user'
            ], 
            // [
            //     'title' => 'Item Groups',
            //     'url' => 'itemgroups',
            //     'icon' => 'lock'
            // ],
        ]
],
    [
        'title' => 'HRM',
        'childs' => [
            [
                'title' => 'Employees',
                'url' => 'employees',
                'icon' => 'user',
                'is_active' => false
            ], [
                'title' => 'Permissions',
                'url' => 'permissions',
                'icon' => 'lock',
                'is_active' => false
            ],
        ]
    ],

    [
        'title' => 'CMS',
        'childs' => [
            [
                'title' => 'Orders',
                'url' => 'orders',
                'icon' => 'rectangle-list',
                'is_active' => false
            ],
            [
                'title' => 'Pages',
                'url' => 'pages',
                'icon' => 'file',
                'is_active' => false
            ],
            [
                'title' => 'Products',
                'url' => 'products',
                'icon' => 'box',
                'is_active' => false
            ],
            [
                'title' => 'Categories',
                'url' => 'categories',
                'icon' => 'layer-group',
                'is_active' => false
            ],
            [
                'title' => 'Events',
                'url' => 'events',
                'icon' => 'calendar-days',
                'is_active' => false
            ],
            [
                'title' => 'Usps',
                'url' => 'usps',
                'icon' => 'sign-hanging',
                'is_active' => false
            ],
            [
                'title' => 'Heroes',
                'url' => 'heroes',
                'icon' => 'window-maximize',
                'is_active' => false
            ],
            [
                'title' => 'Publications',
                'url' => 'publications',
                'icon' => 'newspaper',
                'is_active' => false
            ],
            [
                'title' => 'Menus',
                'url' => 'menus',
                'icon' => 'bars',
                'is_active' => false
            ], [
                'title' => 'Mails',
                'url' => 'mails',
                'icon' => 'envelope',
                'is_active' => false
            ],
        ]
    ],
    [
        'title' => 'Settings',
        'childs' => [
            [
                'title' => 'Units',
                'url' => 'units',
                'icon' => 'ruler-combined',
                'childs' => [
                    [
                        'title' => 'Types',
                        'url' => 'unitstypes',
                    ], [
                        'title' => 'Groups',
                        'url' => 'unitsgroups',
                    ]
                ]
            ],
            [
                'title' => 'Properties',
                'url' => 'properties',
                'icon' => 'cubes'
            ],
            [
                'title' => 'Config',
                'url' => 'config',
                'icon' => 'screwdriver-wrench'
            ],
            [
                'title' => 'Building Blocks',
                'url' => 'buildingblocks',
                'icon' => 'flask-gear',
                'is_active' => false
            ],

    
        ]
    ],
];

@endphp

<nav class="left-menu d-flex navbar-vertical navbar-expand-sm">
    <div class="top-left">

        <div class="row ms-0 mt-2">
            
            <div class="col-6 d-flex align-items-center">
                <a href="{{route('admin.auth.index')}}" class="ratio ratio-2x1 ratio-cover logo">
                    <img src="{{ URL::asset('/img/logo/logo.png') }}" alt="logo">
                </a>
            </div>

            <div class="col-6 d-flex align-items-center">

                <div class="avatar avatar-3xl" id="navbarDropdownUser" role="button" data-bs-toggle="dropdown">

                    {{-- <img class="rounded-circle" src="{{ URL::asset('/img/employee.jpg') }}" alt="employee-image"> --}}

                    <div class="dropdown-menu dropdown-caret dropdown-menu-end py-0" >
                        <div class="bg-white dark__bg-1000 rounded-2 py-2">
                          <a class="dropdown-item" href="#!">Set status</a>
                          <a class="dropdown-item" href="pages/user/profile.html">Profile &amp; account</a>
                          <a class="dropdown-item" href="#!">Feedback</a>
      
                          <div class="dropdown-divider"></div>
                          <a class="dropdown-item" href="pages/user/settings.html">Settings</a>
                          <a class="dropdown-item" href="{{route('admin.auth.logout')}}">Logout</a>
                        </div>
                      </div>
                </div>
            </div>
    
        </div>
    </div>

    <div class="menu ps-3">

        @foreach ($data as $row)

            <ul class="navbar-nav flex-column" id="navbarVerticalNav">
                
                <li class="nav-item">

                    <div class="row navbar-vertical-label-wrapper mt-2 mb-2">
                        <div class="col-auto navbar-vertical-label">{{$row['title']}}</div>
                        <div class="col ps-0">
                            <hr class="mb-0 navbar-vertical-divider" />
                        </div>
                    </div>
                    
                </li>

                @foreach ($row['childs'] as $item)

                    @php
                        $active = (request()->is('admin/'.$item['url'])) ? ' active' : '';
                        $hasChilds = isset($item['childs']) && count($item['childs']) > 0 ? ' nav-link-has-childs' : '';

                        $isActiveClass = '';
                        $isActiveHref = '/admin/'.$item['url'];
                        if (!app()->environment('local')) {
                            $isActiveClass = isset($item['is_active']) && $item['is_active'] === false ? ' text-danger' : '';
                            $isActiveHref = isset($item['is_active']) && $item['is_active'] === false ? '#' : '/admin/'.$item['url'];
                        }
                    @endphp
                
                    <a class="nav-link{{$active.$hasChilds.$isActiveClass}}" href="{{ $isActiveHref }}" role="button">
                        <div class="d-flex align-items-center">
                            <span class="nav-link-icon">
                                <x-admin::icon :icon="$item['icon']" />
                            </span>
                            <span class="nav-link-text ps-1">{{$item['title']}}</span>
                        </div>
                    </a>

                    @if (isset($item['childs']))
                        @foreach ($item['childs'] as $item)

                            @php
                                $active = (request()->is('admin/'.$item['url'])) ? 'active' : '';
                            @endphp
                        
                            <a class="nav-link nav-link-child ps-1 {{$active}}" href="/admin/{{$item['url']}}" role="button">
                                <div class="d-flex align-items-center">
                                    <span class="nav-link-icon">
                                        @isset($item['icon'])
                                            <x-admin::icon :icon="$item['icon']" />
                                        @else
                                            -
                                        @endisset
                                    </span>
                                    <span class="nav-link-text">{{$item['title']}}</span>
                                </div>
                            </a>
                        @endforeach
                    @endif

                @endforeach
            </ul>

        @endforeach

        <div class="mb-5"></div>
    </div>
</nav>
