<div 
    class="widget-buttonmanager"
    data-buttons="<?=htmlentities(json_encode($any->buttons ? $any->buttons : []));?>"
    data-type="standalone"
    data-language="{{App::getLocale()}}"
    data-languages="{{ implode(',', array_column(config('languages'), 'code'))}}">

    <div class="row">
        <div class="col-12 buttons-container sortable">

            <div class="no-buttons offset-3 alert alert-warning" role="alert">
                There are no buttons yet!
            </div>

        </div>
    </div>

    <div class="row mt-2">
        <div class="col-12">
            <div class="float-end">
                <button type="button" class="btn btn-primary btn-sm btn-add">Add button</button>
            </div>
        </div>
    </div>

    <div class="d-none row block-button-template mb-3 button" data-id="">

        <div class="col-3 text-left">
            <iframe class="border-0 w-100 h-100" src="/previews/buttonmanager"></iframe>
        </div>

        <div class="col-1 text-left">
            <i class="fas fa-bars mt-2 sorting-handle"></i>
        </div>

        <div class="col">
            <div class="row">

                <div class="col-12 ">
                    <div class="form-group row row-block row-languages ">
                        <label for="languages" class="label">Languages</label>

                        <div class="input">
                            <select 
                                id="language" 
                                name="language" 
                                data-set-empty="true"
                                data-multiple-separator=" "
                                class="form-control language-select p-0"
                                multiple>
                                @foreach ( config('languages') as $lang )
                                
                                    @php
                                        $imgUrl = URL::asset('/img/flags/4x3/'.$lang['code'].'.svg'); 
                                    @endphp

                                    <option value="{{$lang['code']}}" data-content="<img class='flag rounded' alt='Flag' src='{{ $imgUrl }}' />" selected></option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-12">
                
                    <div class="row">

                        <div class="col-3">
                            <div class="form-check">
                                <input 
                                    class="form-check-input mt-2 button-type" 
                                    type="radio" 
                                    name="button_type"
                                    value="internal"
                                    id="button_type_internal">  
                                <label class="form-check-label label-ignore col-form-label-sm" for="button_type_internal">
                                    Internal link
                                </label>
                            </div> 
                        </div>
                    
                        <div class="col-9">
                            <div class="row form-group">
                                <div class="col-12">
                                    <select 
                                        id="link_internal" 
                                        name="link_internal" 
                                        data-set-empty="true"
                                        class="form-control buttons-input">
                                            <option value="page.92">1</option>
                                            <option value="page.92">2</option>
                                            <option value="page.92">3</option>
                                    </select>
                                </div>       
                            </div>
                        </div>

                    </div>

                    <div class="row">
                    
                        <div class="col-3">
                            <div class="form-check">
                                <input 
                                    class="form-check-input mt-2 button-type" 
                                    type="radio" 
                                    name="button_type"
                                    value="external"
                                    id="button_type_external">  
                                <label class="form-check-label label-ignore col-form-label-sm" for="button_type_external">   
                                    External link
                                </label>
                            </div> 	
                        </div>
                    
                        <div class="col-9">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <div class="input-group-text">https://</div>
                                </div>
                                <input 
                                    type="text" 
                                    class="form-control buttons-input" 
                                    name="button_url" 
                                    value="" 
                                    id="link_external" 
                                    placeholder="">
                            </div>
                        </div>
                    
                    </div>
                </div>

                <!-- Style -->
                <div class="col-12 mt-2">

                    <div class="form-group row-block">
                        
                        <label for="text" class="label">Style</label>
                        <div class="input">
                            <div class="row align-items-center">
                                <div class="col-8">

                                    <select 
                                        id="style" 
                                        name="style" 
                                        class="form-control buttons-input p-0" 
                                        data-style="btn-selectbox">
                                        <option value="0">Nothing selected</option>

                                        @foreach ($styles as $item)
                                            <option value="{{$item['class']}}">{{$item['name']}}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-4">
                                    <div class="form-group row d-flex justify-content-between align-items-center">
                                        <label for="outline" id="outline_label_default " class="col mb-0">Outlined</label>
                                        <div class="col-sm-5 text-end">
                                            <div class="custom-control custom-switch mt-1 form-check form-switch">
                                                <input 
                                                    type="checkbox" 
                                                    class="custom-control-input buttons-input button-outline form-check-input" 
                                                    name="outline"
                                                    value="1"
                                                    id="outline">
                                                <label class="custom-control-label" id="outline_label" for="outline"></label>
                                            </div>    
                                        </div>
                                        
                                    </div> 
                                </div>

                            </div> 
                        </div>
                    </div>
                    
                </div>

                <div class="col-12">
                    <div class="form-group row row-block">
                        <label for="text" class="label">Icon</label>

                        <div class="input">
                            <x-admin.widget.iconpicker :any="$any" class="buttons-icon buttonmanager" />
                        </div>
                    </div>
                </div>

                <!-- Text -->
                <div class="col-12">
                    <div class="form-group row row-block">
                        <label for="text" class="label">Text</label>
                        <div class="input">

                            @foreach ( config('languages') as $lang )

                                <div class="input-group mb-1">
                                    <span class="input-group-text">
                                        <x-admin.flag :code="$lang['code']"/>
                                    </span>
                                    <input type="text" class="form-control buttons-input buttons-input-text" id="text_{{$lang['code']}}">
                                </div>
                            @endforeach

                            <div class="no-languages alert alert-warning d-none">
                                Please select an language first
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        
        <div class="col-auto text-right">
            <button type="button" class="btn btn-danger btn-sm btn-remove btn-icon">
                <div>
                    <i class="fas fa-times"></i>
                </div>
            </button>                
        </div>
    
    </div>

    <input type="hidden" name="buttons" id="button-data" value="" />
</div>