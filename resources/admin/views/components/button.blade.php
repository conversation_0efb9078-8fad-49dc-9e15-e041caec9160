{{-- define default values for attributes --}}
@props([
    'size' => 'sm',
    'style' => 'primary',
])

@php
    //check if there is slot data otherwise check for  label attribute
    $label = $slot->isNotEmpty() 
        ? $slot 
        : $label
    ;

    $style = match($button) {
        'back' => 'outline-primary',
        'destroy' => 'danger',
        'module' => 'warning',
        default => $style,
    };

    $classBtn = ($group ?? '') == 'overview' 
        ? ' btn-control'
        : ' btn btn-'.$size.' btn-'.$style
    ;

    if ($disabled === true) {
        $classBtn .= ' btn-control-disabled';
    }

    $classList = explode(' ', trim($attributes->get('class', '')));

    $attributes = $attributes->merge([
        'class' => 
            $classBtn.
            (!in_array('btn-control-icon', $classList) && is_string($icon) ? ' btn-control-icon' : '').
            (!in_array('btn-control-label', $classList) && !is_null($label) ? ' btn-control-label' : '')
    ]);

    if ($button == 'destroy') {

        $attributes = $attributes->merge([
            'class' => ' btn-delete',
            'data-href' => $disabled === true  
                ? '#'
                : ($route ?? '#'),
            'data-name' => $name ?? 'this item',
            'data-text' => $text ?? '',
        ]);

        $route = null;
    }

@endphp

@if ($element == 'a')
    <a 
        {!! $attributes !!}
        href="{{ $route ?? '#' }}"
    >
        @if (is_string($icon))
            <x-admin::icon :icon="$icon" />
        @endif

        @if(filled($label))
            <div class="btn-label">{{ $label }}</div>
        @endif
    </a>

@elseif ($element == 'button')

    <button
        type="{{ $type ?? 'button' }}"
        {!! $attributes !!}
    >
        @if (is_string($icon))
            <x-admin::icon :icon="$icon" />
        @endif

        @if(filled($label))
            <div class="btn-label">{{ $label }}</div>
        @endif
    </button>

@endif