<div {!! $attributes->merge([
    'class' => 'card card-'.($type ?? 'form')
]) !!}
>

    <div class="card-header">

        @if(trim($title ?? '') !== '')
            {{-- Slot "title" exists --}}
            <div class="card-title">
                {{ $title }}
            </div>
        @elseif(isset($title))
            {{-- Fallback: use the $title variable if set --}}
            <h2 class="card-title">{{ $title }}</h2>
        @endif

  
        @if(trim($controls ?? '') !== '')
            <div class="card-controls ms-auto">
                {{ $controls }}
            </div>
        @elseif(isset($controls))
            {{-- Fallback: use the $title variable if set --}}
            <div class="card-controls">{{ $controls }}</div>
        @endif

        @isset($type)
            @if ($type == "locales")
                <div class="card-locales">
                
                    <ul class="nav nav-locales nav-tabs">

                        @foreach (config('locales') as $locale)
                        
                            <li class="nav-item">
                                <div 
                                    :class="{'active': _locale === '{{ $locale['code'] }}'}"
                                    class="nav-link"
                                    data-locale="{{$locale['code']}}"
                                    x-model="_locale"
                                    x-on:click="_locale = '{{$locale['code']}}'"
                                >
                                    <span>
                                        <x-admin.flag :code="$locale['code']"/>
                                    </span>
                                </div>
                            </li>

                        @endforeach
                    </ul>

                </div>
            @endif
        @endisset

    </div>

    <div class="card-body">

        {{ $slot }}

    </div>
</div>