<div class="row row-item" data-id="{{ $row->id }}">
    
    <div 
        class="col" 
        data-bs-toggle="collapse" 
        data-bs-target=".row-item-childs-{{$row->id}}"
        aria-expanded="false">

        {!! str_repeat('&nbsp;', $depth * 5) !!}

        <x-admin::icon icon="plus" class="ms-1 me-2 icon-collapse" />
        <x-admin::icon icon="minus" class="ms-1 me-2 icon-collapsed" />   
        <span class="fw-bold">{{ $row->code }} - {{ $row->systemname }}</span>

    </div>

    <div class="col-1 controls">
        {{-- <x-admin.button group="overview" button="edit" :parameters="$row->id" /> --}}
    </div>

    <div class="row-item-childs col-12 row-item-childs-{{$row->id}} collapse collapsed">

        @foreach($row['descendants'] as $descendant)
            <x-admin.module.ledger-category-row :row="$descendant" :depth="$depth + 1" />
        @endforeach

        @foreach($row['accounts'] as $account)
            <div class="row row-item" data-id="{{ $account->id }}">
                <div class="col">
                    {!! str_repeat('&nbsp;', ($depth + 2) * 5) !!}
                    {{ $account->code }} - {{ $account->systemname }}
                </div>
                <div class="col-1 controls">
                    {{-- <x-admin.button group="overview" button="edit" :parameters="$account->id" /> --}}
                </div>
            </div>
        @endforeach
    </div>

</div>
