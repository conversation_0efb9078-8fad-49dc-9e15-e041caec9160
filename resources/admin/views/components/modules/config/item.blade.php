@foreach ($elements as $e)

    @if ($e != '') 
        <div class="row-locale{{$e == App::getLocale() ? '' : ' collapse'}}" data-locale="{{$e}}">

        @php($e = '_'.$e)
    @endif

    @if ($any->type->value == 'boolean') 

        <x-admin.form.toggle
            :label="ucfirst($label)"
            :checked="$any->{'value'.$e}"
            :name="'config_'.$any->id.$e"
            :class="$class"
        />

    @endif

    @if ($any->type->value == 'string') 

        <div class="row-block {{$class}}">
            <label class="label {{ $required ? 'label-required' : '' }}" for="config_{{$any->id.$e}}">{{ucfirst($label)}}</label>
            <div class="input">
                <input 
                    id="config_{{$any->id.$e}}"
                    class="form-control" 
                    type="text" 
                    name="config_{{$any->id.$e}}" 
                    value="{{ old('value'.$e, $any) }}"
                    data-original="{{ $any->{'value'.$e} }}"
                />
            </div>
        </div>

    @endif

    @if ($e != '') 
        </div>
    @endif

@endforeach