<div class="row row-item" data-id="{{ $row->id }}">
    
    <div class="col">
        <i class="fas fa-bars me-3 sorting-handle ui-sortable-handle {{$count > 1 ? '' : 'invisible'}}"></i>

        {!! str_repeat('&nbsp;', $depth * 5) !!}

        @if ($depth >= 1)
            <i class="fas fa-level-up-alt fa-rotate-90 ms-1 me-2"></i>
        @endif

        <a href="{{route('admin.'.$module.'.edit' , $row->id)}}" class="fw-bold">
            {{ $row->systemname }}
        </a>

    </div>

    <div class="col-1 text-center">
        {!! $row->is_menu == 1 ? '<i class="fa-solid fa-check text-success"></i>' : '<i class="fa-solid fa-xmark text-danger"></i>' !!}
    </div>

    <div class="col-1 text-center">
        {!! $row->getStatusDot() !!}
    </div>

    <div class="col-2 controls">

        <a href="{{ route('admin.'.$module.'.edit', $row->id) }}">
            <i class="fa-regular fa-pen-to-square"></i>
        </a>

        <a 
            type="button" 
            class="btn-delete" 
            name="delete" 
            href="#" 
            data-href="{{ route('admin.'.$module.'.destroy', $row->id) }}"
            data-name="{{ $row->systemname }}"
            data-text="">

            <i class="fas fa-trash"></i>
        </a>
    </div>

    <div class="row-item-childs col-12 sortable">

        @foreach($row['descendants'] as $descendant)

            <x-admin.module.pagerow :row="$descendant" :count="$row['descendants']->count()"  :depth="$depth + 1" />
        @endforeach
    </div>

</div>
