<div class="row row-item" data-id="{{ $row->id }}">
    
    <div class="col">
        <i class="fas fa-bars me-3 sorting-handle ui-sortable-handle {{$count > 1 ? '' : 'invisible'}}"></i>

        {!! str_repeat('&nbsp;', $depth * 5) !!}

        @if ($depth >= 1)
            <i class="fas fa-level-up-alt fa-rotate-90 ms-1 me-2"></i>
        @endif

        <a href="{{route('admin.categories.edit' , $row->id)}}" class="fw-bold">
            {{ $row->systemlabel }}
        </a>

    </div>

    <div class="col-1 text-center">
        {{ $row->products_count }}
    </div>

    <div class="col-1 text-center">
        {!! $row->is_menu == 1 ? '<i class="fa-solid fa-check text-success"></i>' : '<i class="fa-solid fa-xmark text-danger"></i>' !!}
    </div>

    <div class="col-1 text-center">
        {!! $row->getStatusDot() !!}
    </div>

    <div class="col-2 controls">

        <div class="col-1 controls">
            <x-admin.button group="overview" button="edit" :parameters="$row->id" />
            <x-admin.button group="overview" button="destroy" :parameters="$row->id" data-name="{{$row->systemlabel}}" :disabled="$row->is_locked ?? false" />
        </div>

    </div>

    <div class="row-item-childs col-12 sortable">

        @foreach($row['descendants'] as $descendant)
            <x-admin.module.CategoryRow :row="$descendant" :count="$row['descendants']->count()"  :depth="$depth + 1" />
        @endforeach
    </div>

</div>
