<div class="row row-item" data-id="{{ $row->id }}">

    <div class="col-1">
        <i class="fas fa-bars me-3 sorting-handle ui-sortable-handle {{$count > 1 ? '' : 'invisible'}}"></i>
        {{$row->id}}
    </div>
    
    <div class="col fw-bold">
        <a href="{{ route($app.'.'.$module.'.edit', $row->id) }}">
            {{$row->systemname}}
        </a>
    </div>

    <div class="col-1 text-center">
        {!! $group !!}
    </div>

    <div class="col-1 text-center">
        {!! ucFirst($row->type->value) !!}
    </div>

    <div class="col-1 text-center">
    </div>

    <div class="col-1 text-center">
        {!! $row->is_highlight == 1 ? 'Yes' : '-' !!}
    </div>

    <div class="col-1 text-center">
        {!! $row->getStatusDot() !!}
    </div>

    <div class="col-1 controls">
        

        <a href="{{ route($app.'.'.$module.'.edit', $row->id) }}">
            <i class="fa-regular fa-pen-to-square"></i>
        </a>

        <a 
            type="button" 
            class="btn-delete" 
            name="delete" 
            href="#" 
            data-href="{{ route($app.'.'.$module.'.destroy', $row->id) }}"
            data-name="{{ $row->systemname }}">
            <i class="fas fa-trash"></i>
        </a>
    </div>
</div>
