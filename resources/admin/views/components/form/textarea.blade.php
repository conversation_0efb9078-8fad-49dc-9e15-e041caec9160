@aware([
    'withinRow',
    'withinCol',
    'noAutoSize'
])

@props([ 
    'noAutoSize' => $noAutoSize ?: $attributes->has('noAutoSize')
])

@if(($withinRow && !$withinCol && !$noAutoSize) || $col !== false)
    <div class="col{{ $col !== false ? ($col === true ? '' : '-'.$col) : '' }}">
@endif


@if($floating) <div class="form-floating"> @endif

    <textarea

        id="{{ Str::bracketsToSnakeCase($attributes->get('id') ?? $id()) }}"
        name="{{ $name }}"

        {{--  Placeholder is required as of writing  --}}
        @if($floating && !$attributes->get('placeholder'))
            placeholder="{{ $attributes->get('label') ?? $name }}"
        @endif

        {!! $attributes->except(['id'])->merge([
            'class' => 
                'form-control'.
                ($textEditor ? ' texteditor' : '').
                ($hasError($name) ? ' is-invalid' : '').
                ($isLocked ? ' is-locked ' : '')
            ]) 
        !!}

        @if($textEditor) 
            data-type="{{ $textEditor }}" 
            data-editor-uniq-id="{{ uniqid() }}"
        @endif

    >{!! $value !!}</textarea>

    @if($floating)
        <x-admin.form.label :label="$label" :for="Str::bracketsToSnakeCase($attributes->get('id') ?: $id())" />
    @endif

@if($floating) </div> @endif

@if($hasErrorAndShow($name))
    <x-admin.form.errors :name="$name" />
@endif

@if(($withinRow && !$withinCol && !$noAutoSize) || $col !== false)
    </div>
@endif