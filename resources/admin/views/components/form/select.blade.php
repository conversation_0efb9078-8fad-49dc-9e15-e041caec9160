@aware([
    'withinRow',
    'withinCol',
    'noAutoSize'
])

@props([ 
    'noAutoSize' => $noAutoSize ?: $attributes->has('noAutoSize')
])

@if(($withinRow && !$withinCol && !$noAutoSize) || $col !== false)
    <div class="col{{ $col !== false ? ($col === true || $col === '' ? '' : '-'.$col) : '' }}">
@endif

@php
    //if is multiple and name has no brackets, add them
    if ($multiple && 
        $multiple === true && 
        strpos($name, '[]') === false) {
        $name .= '[]';
    }

    $isDisabled = $attributes->has('disabled') || ($hasSingleOptionSelected && $singleOptionSelectedElement === 'disabled');
@endphp

@if ($isLocked)
    <div class="form-control-locked">
@endif

<select
    id="{{ Str::bracketsToSnakeCase($attributes->get('id') ?? $id()) }}"
    name="{{ $name }}"

    @if($multiple)
        multiple
    @endif

    @if($placeholder)
        placeholder="{{ $placeholder }}"
    @endif

    {!! $attributes->except([ 'id' ])
        ->merge([
            'class' => 
                'form-control form-control-sm'.
                ($hasError($name) ? ' is-invalid' : '').
                ($isLocked ? ' is-locked' : ' selectpicker').
                ($isLocked || $isDisabled ? ' disabled' : ''),
            'tabindex' => $isLocked || $isDisabled ? '-1' : '0',
        ]) 
    !!}
>

    @if(!$multiple)
        <option value="">{{ $placeholder ?? 'Nothing selected' }}</option>
    @endif

    @if ($options)
        {!! $renderOptions($options) !!}
    @else
        {!! $slot !!}
    @endif

</select>

@if ($isLocked)
    </div>
@endif

{{-- @if ($isLocked)
    <span {!! $attributes->merge([
        'class' => ' value-is-locked '
        ])
    !!}>{{ implode(', ', array_column($getOptionsSelected(), 'text')) }}</span>
@endif --}}

@if($hasErrorAndShow($name))
    <x-admin.form.errors :name="$name" />
@endif

@if(($withinRow && !$withinCol && !$noAutoSize) || $col !== false)
    </div>
@endif