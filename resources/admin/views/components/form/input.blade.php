@aware([
    'withinRow',
    'withinCol',
    'noAutoSize'
])

@props([ 
    'noAutoSize' => $noAutoSize ?: $attributes->has('noAutoSize'),
])

@php
    $valueReplacement = $value;
    if (in_array($type, [ 'money', 'decimal' ])) {
        $valueReplacement = $transformReplacementName($value, '', '_formatted');
    } 
@endphp

@if($type !== 'hidden')
    @if(($withinRow && !$withinCol && !$noAutoSize) || $col !== false)
        <div class="col{{ $col !== false ? ($col === true || $col === '' ? '' : '-'.$col) : '' }}">
    @endif
@endif


@if (!$isLocked &&
    !$attributes->has('noInputGroup') && (
    in_array($type, [ 'money', 'datepicker' ]) ||
    $attributes->has('suffix') ||
    $attributes->has('prefix')
))



    <div class="input-group input-group-sm {{ $attributes->has('class-group') ? $attributes->get('class-group') : '' }}">

        @if ($attributes->has('prefix'))
            <span class="input-group-text">
                {{ $attributes->get('prefix') }}
            </span>
        @endif

        @if ($type == 'money')
            <span class="input-group-text">
                {{ $attributes->get('data-currency') ?? '€' }}
            </span>
        @endif
@endif


@php
    //get attribute x-show
    $xShow = $attributes->get('x-show');
    $xModel = $attributes->get('x-model') ?? '';

    $attributesExcepts = [ 
        'id',
        'placeholder',
        'data-currency',
        'data-decimals',
        'noInputGroup',
        'suffix',
        'prefix',
        'class-group'
    ];

    if (in_array($type, [ 'money', 'decimal' ])) {
        $attributesExcepts[] = 'x-model';
    }
@endphp

@if ($type !== 'hidden' && $isLocked)
    <div class="form-control-locked">
@endif

<input
    {!! $attributes
        ->except($attributesExcepts)
        ->merge([
            'class' => 
                ($type === 'datepicker' ? ' datepicker ' : '').
                ($type !== 'hidden' ? ' form-control form-control-sm ' : '').
                ($hasError($name) ? ' is-invalid ' : '').
                ($isLocked ? ' is-locked ' : '')
        ])
    !!}

    id="{{ Str::bracketsToSnakeCase($attributes->get('id') ?? $id()) }}"
    type="{{ in_array($type, [ 'money', 'decimal', 'datepicker' ]) ? 'text' : $type }}"
    value="{{ in_array($type, [ 'money', 'decimal' ]) && is_numeric($valueReplacement) 
        ? Number::convertStorageToMoney($valueReplacement, $attributes->get('data-decimals') ?? 2) 
        : $valueReplacement }}"
    name="{{ (in_array($type, [ 'money', 'decimal' ]) ? '_' : '').$name }}"

    @if(in_array($type, [ 'money', 'decimal' ]))
        data-type="{{ $type }}"
        data-decimals="{{ $attributes->get('data-decimals') ?? 2 }}"
    @endif

    @if($type === 'money')
        data-currency="{{ $attributes->get('data-currency') ?? '€' }}"
    @endif

    @if($type !== 'hidden' && $floating)
        placeholder="{{ $attributes->get('placeholder') ?? $attributes->get('label') ?? $name }}"
    @endif

    @if ($type === 'datepicker')
        autocomplete="off"
    @endif
/>

@if ($type !== 'hidden' && $isLocked)
    </div>
@endif

    {{-- <span {!! $attributes->merge([
        'class' => ' value-is-locked '.
            ($type === 'datepicker' ? ' datepicker ' : '')
        ])
    !!}>
        @if (in_array($type, [ 'money', 'decimal' ]))
            {{  Number::convertStorageToMoney($valueReplacement, $attributes->get('data-decimals') ?? 2) }}
        @else
            {{ $valueReplacement }}
        @endif
    </span> --}}


@if (in_array($type, [ 'money', 'decimal' ]))
    <input
        type="hidden" 
        name="{{ $name }}" 
        id="{{ $type }}_{{ Str::bracketsToSnakeCase($attributes->get('id') ?? $id()) }}" 
        value="{{ $value }}" 
        {{ $attributes->get('x-model') ? 'x-model='.$attributes->get('x-model') : '' }}
    />
@endif

@if (!$isLocked &&
    !$attributes->has('noInputGroup') && (
    in_array($type, [ 'money', 'datepicker' ]) ||
    $attributes->has('suffix') ||
    $attributes->has('prefix')
))

    @if ($type == 'datepicker')
        <span class="input-group-text">
            <x-admin::icon icon="calendar-days" />
        </span>
    @endif

    @if ($attributes->has('suffix'))
        <span class="input-group-text">
            {{ $attributes->get('suffix') }}
        </span>
    @endif
    
    </div>
@endif

@if($type !== 'hidden')

    @if($hasErrorAndShow($name))
        <x-admin.form.errors :name="$name" />
    @endif

    @if(($withinRow && !$withinCol && !$noAutoSize) || $col !== false)
        </div>
    @endif

@endif