@aware([
    'withinPageRow',
    'withinPageCol',
    'pageColWidth',
    'withinRow',
    'withinCol',
    'noAutoSize'
])

@props([ 
    'labelCol' => '-'.($col ? $col : '3'),
    'noAutoSize' => $noAutoSize ?: $attributes->has('noAutoSize')
])

@if ($withinPageCol)
    @if ($col === false)
        @php
            if ($pageColWidth == 100)   $labelCol = '-3';
            if ($pageColWidth == 50)    $labelCol = '-5';
        @endphp 
    @else
        @php
            $labelCol = $col === true ? '' : '-'.$col;
        @endphp

    @endif
@endif

@php
    if (isset($attributes['for'])) {
        if ($locale && !preg_match('/\[[a-zA-Z]{2}\]/', $attributes['for'])) {
            $attributes['for'] = $attributes['for'].'['.$locale.']';
        }

        $attributes['for'] = Str::bracketsToSnakeCase($attributes['for']);
    }
@endphp   

@if($label)

    @if(($withinRow && !$noAutoSize))
        <div class="col{{$labelCol}}">
    @endif

    <label {!! $attributes->merge([ 
        'class' => $withinRow && !$noAutoSize ? 'col-form-label' : 'form-label',
    ]) !!}>{!! $label !!}</label>
    
    @if($withinRow && !$noAutoSize)
        </div>
    @endif
@endif