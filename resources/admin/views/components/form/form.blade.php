
@php   
    //use Support DataSupport xDataToJson function
    $raw = $attributes->get('x-data', '{}');
    $xData = Data::xDataToJson($attributes->get('x-data', '{}')) ?: [];

    // Add _locale if not already set
    if (!array_key_exists('_locale', $xData)) {
        $xData['_locale'] = old('locale', app()->getLocale());
    }
@endphp

<form method="{{ $spoofMethod ? 'POST' : $method }}"
    {!! $attributes->except('x-data')->merge([
        'class' => 'form'.($hasError() ? ' needs-validation' : ''),
        'x-data' => json_encode($xData)
    ]) !!}
>

@unless(in_array($method, ['HEAD', 'GET', 'OPTIONS']))
    @csrf
@endunless

@if($spoofMethod)
    @method($method)
@endif

<x-admin.form.input type="hidden" name="_locale" x-bind:value="_locale" />