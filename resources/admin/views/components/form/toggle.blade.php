@aware([
    'withinRow',
    'withinCol',
    'noAutoSize'
])

@props([ 
    'noAutoSize' => $noAutoSize ?: $attributes->has('noAutoSize')
])

@if(($withinRow && !$withinCol && !$noAutoSize) || $col !== false)
    <div class="col{{ $col !== false ? ($col === true ? '' : '-'.$col) : '' }}">
@endif

@php
    //catch all attribute starting with x- and remove them from the attributes
    $inputAttributes = $attributes->whereStartsWith('x-');  
    
    $attributes = $attributes->except(collect($inputAttributes)
        ->keys()
        ->toArray()
    );
@endphp

<div {!! $attributes->except([ 'id' ])
    ->merge([ 
        'class' => 
            'form-check form-switch mt-1'.
            ($isLocked ? ' is-locked ' : '')
         ]) !!}>

    <input 
        id="{{ Str::bracketsToSnakeCase($attributes->get('id') ?? $id()) }}"
        name="{{ $name }}"
        class="form-check-input {{ ($hasError($name) ? ' is-invalid' : '') }}"
        type="checkbox"
        value="1"

        {{ $inputAttributes }}

        @if ($checkedReplacementName)
            data-checked="[[{{$checkedReplacementName}}={{$checked}}]]"
        @else
            @if($checked)
                checked
            @endif
        @endif
    />

    <input 
        type="hidden" 
        name="_{{ $name }}"
        class="form-check-input-hidden"
        value="{{ $checked ? 1 : 0 }}" 
    /> 
    
    <label class="form-check-label d-none" for="{{ $attributes->get('id') ?? $id() }}">{{ $name }}</label>

    @if($hasErrorAndShow($name))
        <x-admin.form.errors :name="$name" />
    @endif

</div>

@if(($withinRow && !$withinCol && !$noAutoSize) || $col !== false)
    </div>
@endif