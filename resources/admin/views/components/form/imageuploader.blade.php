
@php
    $uniqId = $attributes->get('data-uniq-id') ?? Str::uniqId();
    $name = $attributes->get('name') ?? 'uploader_'.$uniqId;
@endphp

<div 
    {!! $attributes->merge([
        'class' => 'image-uploader',
        'data-name' => $name,
        'data-type' => 'single',
        'data-editor' => 'true',
        'data-aspectratio' => '3x2',
        'data-freeform' => 'false',
        'data-items-per-row' => '4',
        'data-uniq-id' => $uniqId
    ]) !!}
>

    <div class="media-container">

        @template('image-block-media', false)
        <div class="image-block image-block-media">
            <div class="image-block-content">

                <div class="media-wrapper">
                    <img class="media" src="" alt="">
                </div>

                <div class="controls">
                    <a class="btn-control">
                        <!-- font awseome edit pencil icon -->
                        <i class="fa-regular fa-pen-to-square"></i>
                    </a>
                    <a class="btn-control">
                        <!-- font awseome trans icon -->
                        <i class="fa-regular fa-trash-alt"></i>
                    </a>
                </div>
            </div>

            <input type="hidden" name="{{$name}}[media][]" value="[[uniq_id]]">
        </div>
        @endtemplate

        <div class="image-block image-block-uploader">
            <div class="image-block-content">
                <p><small><i>Drag &amp; drop images(s) here to upload</i></small></p> 
                
                <input type="file" class="d-none" name="upload" multiple=""> 
                
                <button type="button" class="btn btn-secondary btn-sm btn-upload">Upload</button> 
            </div>
        </div>

    </div>

</div>