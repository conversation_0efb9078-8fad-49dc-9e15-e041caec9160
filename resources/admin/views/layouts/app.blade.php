<!doctype html>
<html lang="nl">
<head>
    
    <title>{{ config('app.name', env('APP_NAME')) }}</title>
    
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">    
    
    @vite([
        'resources/app.js',
        'resources/admin/js/app.js',
        ...app(\App\Services\BladeService::class)->getAssets(),
    ])

    <link rel="stylesheet" href="/vendor/fontawesome/css/all.min.css">
   
    {{-- <script type="module" src="{{ asset('vendor/bootstrap-select/dist/js/i18n/defaults-'.config('locales.'.App::getLocale())['locale'].'.js') }}"></script> --}}

    @include('admin::sections.js_configs')

</head>
<body>

    <main class="main module-{{App::get('module')}}">

        @include('admin::sections.left_menu')

        <div class="content">

            @yield('form', '')

            <nav class="content-header">

                <div class="content-header-top">
                    
                    @hasSection('breadcrumbs')
                        <div class="breadcrumbs">
                            <ol>
                                @yield('breadcrumbs')
                            </ol>
                        </div>
                    @endif

                    @hasSection('controls')
                        <div class="controls">
                            @yield('controls')
                        </div>
                    @endif

                </div>
                <div class="content-header-bottom">

                    @hasSection('title')
                        <div class="title-wrapper">

                            @if (trim(View::getSection('title')) === '1')
                                @if (!isset($data) || !$data->exists)
                                    <h1 class="title">Create</h1>
                                @elseif (isset($data) && $data->exists)
                                    <h1 class="title">Edit {{$data->systemlabel}}</h1>
                                @endif
                            @else
                                <h1 class="title">@yield('title')</h1>
                            @endif

                            @hasSection('title_elements')
                                <div class="title-elements">
                                    @yield('title_elements')
                                </div>
                            @endif

                        </div>
                    @endif

                    @hasSection('flags')
                        @if(trim(View::getSection('flags')) !== '')
                            <div class="header-flags">
                                @yield('flags')
                            </div>
                        @endif
                    @endif
                    
                    @hasSection('tabs')
                        <div class="tabs">
                            <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                @yield('tabs')
                            </div>
                        </div>
                    @endif
                </div>

            </nav>

            <div class="content-body">
                
                <x-admin.alert/>

                @hasSection('filters')
                    @if(trim(View::getSection('filters')) === '1')

                        @php
                            $filterRoute = str_replace('_index', '_filter', Route::currentRouteName());
                            $filterRoute = str_replace('.index', '.filter', $filterRoute);
                            
                            if (!Route::has($filterRoute) || 
                                !Str::endsWith(Route::getRoutes()->getByName($filterRoute)?->action['uses'] ?? null, '@filter')
                            ) {
                                $filterRoute = 'admin.'.App::get('module').'.filter';
                            }
                        @endphp
                
                        @if ((App::get('model') ?? null) != null && isset($data) && Route::has($filterRoute))

                            @php
                          
                                $routeClass = Route::getRoutes()->getByName($filterRoute);

                                $routeParameters = array_filter(array_map(function($param) {
                                    
                                    if (Route::current()->parameter($param) !== null) {
                                        return Route::current()->parameter($param);
                                    }

                                    if (!is_null(request()->route($param))) {
                                        return request()->route($param);
                                    }

                                    return request()->input($param);

                                }, $routeClass->parameterNames()));   
                                
                              
                            @endphp

                            <x-lacodix-filter::model-filters 
                                :model="resolve('App\\Models\\'.App::get('model'))" 
                                method="post" 
                                :action="route(
                                    $filterRoute, 
                                    count($routeParameters) > 0 
                                        ? $routeParameters
                                        : []
                                    )" 
                            />
                        @endif
                    @else 
                        @yield('filters')
                    @endif

                @elseif (isset($filters))
                    @dd($filters)
                    {{-- <x-lacodix-filter::model-filters 
                        :model="$filters" 
                        method="post" 
                        :action="route($app.'.'.App::get('module').'.filter')" 
                    /> --}}
                @endif                    

                @yield('content')
            
            </div>

            @hasSection('form')
                </form>
            @endif

        </div>

    </main>

    @hasSection('templates')
        @dd('VIEW HAS DEPRECATED TEMPLATES')
        <div class="templates d-none">
            @yield('templates')
        </div>
    @endif  
  
    @renderStack('templates')
    @renderDataSets()

    <script type="text/javascript">
        @php
            echo 'let catIds = '.json_encode($catIds = [
                'substrate' => 1,
                'convection' => 7,
                'accessory' => 6,
                'fabrics' => 2,
                'sticker' => 3,
                'panel' => 4,
            ]).';';

            echo 'let buttons = [ {title: "Text link", value: ""},';

            foreach (App\Models\Button::getStyles() as $item) {
                
                echo '{ title: "Button '.$item['name'].'", value: "btn button-'.$item['class'].' btn-'.$item['class'].'"},';
                echo '{ title: "Button '.$item['name'].' - Outlined", value: "btn button-outline-'.$item['class'].' btn-outline-'.$item['class'].'"},';
            }
            echo '];';
            
        @endphp

        let links = [
            {
                title: "products- 1" , value: "/1"
            },
        ]
    </script>

    @if (in_array(App()->environment(), ['local', 'development']) && config('app.debug'))
        <script>
            PhpDebugBar.DebugBar.prototype.recomputeBottomOffset = () => {};
        </script>
    @endif
</body>
</html>