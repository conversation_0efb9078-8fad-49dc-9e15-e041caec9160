import "bootstrap-datepicker/dist/css/bootstrap-datepicker.min.css";
import '../sass/app.scss';

import * as datepicker from 'bootstrap-datepicker';
$.fn._datepicker = $.fn.datepicker.noConflict();

import * as functions from './functions.js';
Object.keys(functions).forEach((key) => {
    window[key] = functions[key];
});



import 'owl.carousel';
import '@fancyapps/fancybox';
import 'tinymce/tinymce';
import 'tinymce/models/dom/model';
import 'tinymce/themes/silver/theme';
import 'tinymce/icons/default/icons';
import 'tinymce/skins/ui/oxide/skin.min.css' 
import 'tinymce/plugins/autoresize';
import 'tinymce/plugins/media';
import 'tinymce/plugins/quickbars';
import 'tinymce/plugins/link';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/wordcount';
import 'tinymce/plugins/code';
import 'tinymce/plugins/table';
import 'tinymce/plugins/autosave';
import 'tinymce/plugins/fullscreen';

import './datasets.js';

import { IconPicker } from './widgets/iconpicker.js';
import { Modal } from './widgets/modal.js';
import { TagsPicker } from './widgets/tagspicker.js';
import { setTextEditor } from './widgets/texteditor.js';
import DocumentUploader from './widgets/DocumentUploader.js';
import { ImageUploader } from './widgets/imageuploader.js';

window.tinymce = tinymce;
window.Modal = Modal;

window.ImageUploader = ImageUploader;

jQuery.fn.extend({
    set: function (name, value) {
        return this.each(function () {
            $(this)
                .prop(name, value)
                .attr(name, value)
            ;
        });
    }
});

jQuery.fn.extend({
    switchClass: function (targetClass, replacementClass) {
        return this.each(function () {
            $(this)
                .removeClass(targetClass)
                .addClass(replacementClass)
            ;
        });
    }
});

let documentUploader,
    imageUploaders = [],
    timer = null
;
$(document).ready(function() {
    // Select the sidemenu element
    const $sidemenu = $('.left-menu .menu');

   

    // Scroll event handler
    function handleScroll() {
        const scrollPos = $sidemenu.scrollTop();
        document.cookie = 'menuLeftScrollPosition=' + encodeURIComponent(scrollPos) + '; path=/';
    }

    // Function to get the maximum scroll height
    function getMaxScrollHeight() {
        return $sidemenu[0].scrollHeight - $sidemenu[0].clientHeight;
    }

    // Function to restore scroll position
    function restoreScrollPosition() {
        const savedScrollPos = getCookie('menuLeftScrollPosition');
        if (!savedScrollPos) {
            // Bind scroll event if no restoration is needed
            $sidemenu.on('scroll', handleScroll);
            return;
        }

        const targetPos = parseInt(savedScrollPos, 10);

        // Temporarily unbind scroll event to prevent it from firing during restoration
        $sidemenu.off('scroll', handleScroll);

        // Poll for scroll height to stabilize
        let attempts = 0;
        const maxAttempts = 20; // Increased to allow more time
        const pollInterval = 25; // Check every 100ms

        const pollScrollHeight = setInterval(function() {
            const currentMaxScroll = getMaxScrollHeight();

            if (currentMaxScroll >= targetPos || attempts >= maxAttempts) {
                // Scroll height is sufficient or max attempts reached
                clearInterval(pollScrollHeight);
                $sidemenu.scrollTop(targetPos);

                // Verify the scroll position
                if ($sidemenu.scrollTop() !== targetPos) {
                    // Retry setting scroll position
                    let retryAttempts = 0;
                    const maxRetryAttempts = 5;
                    const retryInterval = setInterval(function() {
                        if ($sidemenu.scrollTop() !== targetPos && retryAttempts < maxRetryAttempts) {
                            console.log('Retrying scroll position:', targetPos, 'Current:', $sidemenu.scrollTop());
                            $sidemenu.scrollTop(targetPos);
                            retryAttempts++;
                        } else {
                            clearInterval(retryInterval);
                            // Rebind scroll event
                            $sidemenu.on('scroll', handleScroll);
                            console.log('Final scroll position:', $sidemenu.scrollTop());
                        }
                    }, 100);
                } else {
                    // Rebind scroll event
                    $sidemenu.on('scroll', handleScroll);
                }
            } else {
                attempts++;
                if (attempts >= maxAttempts) {
                    clearInterval(pollScrollHeight);
                    // Set to maximum available scroll position as fallback
                    $sidemenu.scrollTop(currentMaxScroll);
                    $sidemenu.on('scroll', handleScroll);
                }
            }
        }, pollInterval);
    }

    // Wait for window load to ensure all content is rendered
    $(window).on('load', function() {
        restoreScrollPosition();
    });
});

$(function () {

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });


    // $(document).on('keyup', [
    //     'input[data-type=money]', 
    //     'input[data-type=decimal]'
    // ].join(', '), function (e) {
    //     $(this).val($(this).val().replace(/[^0-9\.\,]/g, ''));
    // });

    $(document).on('change', 'input[data-type=money]', function (e) {

        console.log('on(change, input[data-type=money]');

        $(this).val($(this).val().replace(/[^0-9\.\,]/g, ''));

        if ($(this).val() == '') {
            $('#money_'+$(this).attr('id')).set('value', 0);
            return;
        }

        let val = normalizePrice($(this).val()),
            decimals = $(this).data('decimals') ?? 2,
            rangeSiblingElement
        ;

        if (val !== null && !isNaN(val)) {

            val = parseFloat(val.toFixed(decimals));

            if (($(this).data('range') || false) === true &&
                (rangeSiblingElement = $($(this).data('range-min') || null))?.length == 1) {

                let valSibling = normalizePrice(rangeSiblingElement.val())?.toFixed(decimals) || null;
                if (valSibling !== null) {
                    ++valSibling;
                }

                val = Math.max(...[
                    val, 
                    valSibling
                ].filter(v => (v || null) !== null));
            }

            if (($(this).data('range') || false) === true &&
                (rangeSiblingElement = $($(this).data('range-max') || null))?.length == 1) {

                let valSibling = normalizePrice(rangeSiblingElement.val())?.toFixed(decimals) || null;
                if (valSibling !== null) {
                    --valSibling;
                }

                val = Math.min(...[
                    val, 
                    valSibling
                ].filter(v => (v || null) !== null));
            }

            if ($(this).data('min') && val < parseFloat($(this).data('min'))) {
                val = parseFloat($(this).data('min').toFixed(decimals));
            }

            if ($(this).data('max') && val > parseFloat($(this).data('max'))) {
                val = parseFloat($(this).data('max').toFixed(decimals));
            }

            val = formatPrice(val, decimals);

            $(this).val(val);
            $('#money_'+$(this).attr('id')).set('value', convertFromMoneyToStorage(val));

            return;
        }

        $(this).val('');
        $('#money_'+$(this).attr('id')).set('value', 0);
    });

    $(document).on('change', 'input[data-type=decimal]', function (e) {

        $(this).val($(this).val().replace(/[^0-9\.\,]/g, ''));

        if ($(this).val() == '') {
            $('#decimal_'+$(this).attr('id')).set('value', 0);
            return;
        }

        let val = normalizePrice($(this).val()),
            decimals = $(this).data('decimals') ?? 2,
            rangeSiblingElement
        ;

        if (val !== null && !isNaN(val)) {

            val = parseFloat(val.toFixed(decimals));

            let isRange = ($(this).attr('data-range') || 'false') === 'true';

            if (isRange &&
                (rangeSiblingElement = $($(this).data('range-min') || null))?.length == 1) {

                let valSibling = normalizePrice(rangeSiblingElement.val())?.toFixed(decimals) || null;
                if (valSibling !== null) {
                    ++valSibling;
                }

                val = Math.max(...[
                    val, 
                    valSibling
                ].filter(v => (v || null) !== null));
            }

            if (isRange &&
                (rangeSiblingElement = $($(this).data('range-max') || null))?.length == 1) {

                let valSibling = normalizePrice(rangeSiblingElement.val())?.toFixed(decimals) || null;
                if (valSibling !== null) {
                    --valSibling;
                }

                val = Math.min(...[
                    val, 
                    valSibling
                ].filter(v => (v || null) !== null));
            }

            if ($(this).data('min') && val < parseFloat($(this).data('min'))) {
                val = parseFloat($(this).data('min').toFixed(decimals));
            }

            if ($(this).data('max') && val > parseFloat($(this).data('max'))) {
                val = parseFloat($(this).data('max').toFixed(decimals));
            }
         
            val = val.toString().replace('.', ',');

            $(this).val(val);
            $('#decimal_'+$(this).attr('id')).set('value', convertFromMoneyToStorage(val));
            return;
        }

        $(this).val('');
        $('#decimal_'+$(this).attr('id')).set('value', 0);
    });

    $('.image-uploader').each(function () {
        imageUploaders.push(new ImageUploader($(this)));
    });

    $('.datepicker:not([data-startdatetoday]')._datepicker({
        format: 'dd-mm-yyyy',
        autoclose: true,
        calendarWeeks: true,
        todayHighlight: true
    });

    $('.datepicker[data-startdatetoday]')._datepicker({
        format: 'dd-mm-yyyy',
        autoclose: true,
        calendarWeeks: true,
        todayHighlight: true,
        startDate: new Date()
    });

    $('.texteditor').each(function () {
        setTextEditor(
            $(this).data('editor-uniq-id'),
            $(this).data('type')
        );
    });



    $('.filters .filter-container select.selectpicker').on('changed.bs.select', function() {
        $(this).closest('form').submit();
    });
    $('.filters .filter-container input[type=checkbox').on('change', function() {
        $(this).closest('form').submit();
    });


    //GLOBAL BUTTONS
    
    $(document).on('click', '.btn-delete-close', function () {

        if (timer) clearTimeout(timer);

        bootstrap.Modal.getInstance($('body > .modal')).hide();
    });

    $(document).on('click', '.btn-delete.btn-control-disabled', function () {

        new Modal({
            "title": 'Cannot delete',
            "content": $("<p>This item cannot be deleted.</p>"),
            "buttons": [
                '<button type="button" class="btn btn-primary btn-delete-close">Ok</button>'
            ], 
            "callBack": function() {
                timer = setTimeout(() => {
                    bootstrap.Modal.getInstance($('body > .modal')).hide();
                }, 5000);
            }
        });
    });

    $(document).on('click', '.btn-delete:not(.btn-control-disabled)', function () {

        let name = 'this item',
            text = '',
            href = '',
            id = '',
            classConfirm = 'btn-delete-confirm'
        ;

        if (typeof $(this).data('name') != 'undefined') {
            name = "<b>" + $(this).data('name') + "</b>";
        }

        if (typeof $(this).data('text') != 'undefined') {
            text = "<br>" + $(this).data('text') + "";
        }

        if (typeof $(this).data('href') != 'undefined') {
            href = ` data-href="${$(this).data('href')}"`;
        }

        if (typeof $(this).data('confirm') != 'undefined') {
            classConfirm = $(this).data('confirm');
        }

        if (typeof $(this).data('id') != 'undefined') {
            id = ` data-id="${$(this).data('id')}"`;
        }

        new Modal({
            "title": 'Are you sure',
            "content": $("<p>Are you sure you want to delete " + name + "?" + text+ "</p>"),
            "buttons": [
                '<button type="button" class="btn btn-primary" data-bs-dismiss="modal">Cancel</button>',
                `<button type="button" class="btn btn-danger ${classConfirm}"${href}${id}>Yes</button>`
            ]
        });
    });

    $(document).on('click', '.btn-delete-confirm', function () {
        window.location.href = $(this).data('href');
    });

    $('.overview:not(.overview-static) .overview-body').each(function () {

        let observer = new MutationObserver(function(mutations) {
            setOverviewVisibility($(mutations[0].target).closest('.overview'));
        });

        observer.observe(
            $(this)[0],
            { childList: true }
        );

        setOverviewVisibility($(this).closest('.overview'));
    });





    $(document).on('change', '.input-decimals', function (e) {
        $(this).val(inputDecimals($(this).val(), typeof $(this).data('decimals') !== 'undefined'
            ? parseInt($(this).data('decimals'))
            : 2)
        );
    });

    $('.iconpicker:not(.buttonmanager)').each(function () {
        new IconPicker($(this));
    });

    $('.input-keywords').each(function () {
        let keywordPicker = new TagsPicker($(this), 'keyword');
    });

    

    $('main select:not(.no-selectpicker)').selectpicker();

    $(document).on('change changed.bs.select', 'select', function() {
        setSelectData($(this));
    });

    $('select').each(function() {
        setSelectData($(this));
    });


    
    $('.card-sortable .overview-body').sortable({
        handle: ".sorting-handle",
        stop: function (e, ui) {
            updateSortOrder($(e.target));
        }
    });

    $('.row-item-childs.sortable').sortable({
        handle: ".sorting-handle",
        stop: function (e, ui) {
            updateSortOrder($(e.target));
        }
    });

    
    //not working properly
    $(document).on('click', '.btn-cancel-modal', function () {
        $(this).closest('.modal').modal('hide');
    });


    // $('.texteditor').each(function() {
    //     console.log('texteditor', $(this), setTextEditor($(this).data('type')));
    //     setTextEditor($(this).data('type'));
    // });

    

    $('.tagspicker').each(function () {
        new TagsPicker($(this));
    });

    //set checkbox value
    $(document).on('change', '.form-check-input', function () {
        $('input[name="_' + $(this).attr('name') + '"]').val($(this).is(':checked')
            ? 1
            : 0
        );
    });

    //prevent submit
    $('.form').on('keypress', function (e) {
        if (e.key === 'Enter') {
            e.preventDefault();
        }
    });

    $('input[type=checkbox][data-checked]').each(function() {
        
        $(this)
            .set('checked', [ true, 'true', '1' ].indexOf($(this).data('checked')) !== -1)
            .removeAttr('data-checked')
        ;
    });


    documentUploader = new DocumentUploader();
});