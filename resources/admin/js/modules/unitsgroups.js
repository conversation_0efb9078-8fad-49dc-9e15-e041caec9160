
function setUnitAddButtonVisibility() {

    let unitIds = $('.overview-units .row-unit').map(function() {
        return $(this).data('unit-id');
    }).get();

    let units = _data.set('units')
        .whereNotIn('id', unitIds)
        .get()
    ;

    if (units.length == 0) {
        $('.btn-unit-add').addClass('disabled');
        return false;
    } 
 
    $('.btn-unit-add').removeClass('disabled');
    return true;
}

$(function() {

    $(document).on('click', '.modal .btn-delete-unit-confirm', function() {
        
        let id = $(this).data('id');

        bootstrap.Modal.getInstance($('body > .modal')).hide();

        $('.overview-units .row-unit[data-unit-group-unit-id="'+id+'"]').slideUp(function() {
            $(this).remove();
            setUnitAddButtonVisibility();
        });
    });
      
    $(document).on('click', '.btn-unit-add-confirm', function() {
        
        let unitId = $(this).closest('.modal').find('select').selectpicker('val');

        if (unitId == '') return;
   
        let unit = _data.set('units').where('id', '=', parseInt(unitId)).getFirst();
       
        if (!unit) return;

        let unitType = _data.set('units_types').where('id', '=', unit.unit_type_id).getFirst();

        if (!unitType) return;

        $('.overview-units .overview-body').append(getTemplate('unit-row', {
            'id': uniqueId(),
            'unit_id': unit.id,
            'unit_systemname': unit.systemname,
            'unit_type_id': unit.unit_type_id,
            'unit_type_systemname': unitType.systemname,
            'unit_value': unit.value,
            'is_locked': 0,
            'unit_base_unit': unitType.unit_base_id == unit.id
        }));

        setUnitAddButtonVisibility();

        bootstrap.Modal.getInstance($('body > .modal')).hide();       
    }); 

    $('.btn-unit-add').on('click', function() {

        if (!setUnitAddButtonVisibility()) {
            return;
        }

        let element = getTemplate('unit-modal');
        
        $('.overview-units .row-unit').each(function() {
            element.find('select option[value="'+$(this).data('unit-id')+'"]').remove();
        });

        new Modal({
            "title": 'Add unit',
            "centered": true,
            "content": element,
            "buttons": [
                '<button type="button" class="btn btn-outline-primary" data-bs-dismiss="modal">Cancel</button>',
                `<button type="button" class="btn btn-success btn-unit-add-confirm">Add</button>`
            ]
        });

    });

    setUnitAddButtonVisibility();
});