import { ShipmentPricingRules } from '../widgets/shipmentpricingrules.js';
let shipmentRules;

$(document).ready(function () {

    $(document).on('change', '#is_key', function() {
        if ($(this).is(':checked')) {
            $('.panel-company-pricing').slideDown();
        } else {
            $('.panel-company-pricing').slideUp();
        }
    });
    $('#is_key').trigger('change');

    $(document).on('change', 'input[name=is_same]', function() {
        setAddressVisibility()
    });

    setAddressVisibility();

    if ($('.panel-shipment-rules').length > 0) {

        shipmentRules = new ShipmentPricingRules();
    }
    
    $(document).find('.price-normalize').trigger('change');
});

function setAddressVisibility() {

    $(document).on('change', 'input[name=is_same]', function() {
        if ($('input[name=is_same]').is(':checked')) {
            $(this).closest('.address-wrapper').find('.address-container').slideUp();
            $('input[name=delivery_address_id]').val($('input[name=invoice_address_id]').val())
        } else {
            $(this).closest('.address-wrapper').find('.address-container').slideDown();
            $('input[name=delivery_address_id]').val('')
        }
    });
}


let prefix = '.panel-company-pricing ',
    products = $(prefix).data('products') ?? [],
    confections = $(prefix).data('confections') ?? [],
    pricings = $(prefix).data('pricings') ?? [];

let selectProductOptions = [];

products.forEach(function(p) {
    selectProductOptions.push('<option value="'+p.id+'">'+p.systemname+'</option>');
});

let selectConfectionOptions = [];
confections.forEach(function(p) {
    selectConfectionOptions.push('<option value="'+p.id+'">'+p.systemname+'</option>');
});


function checkPricingVisibility()
{
    if ($(prefix+'.pricing-container .row-pricing').length == 0) {
        $(prefix+'.pricing-container .no-pricings').show();
    } else {
        $(prefix+'.pricing-container .no-pricings').hide();
    }
}

function addPricing(data = {})
{

    let uniqId = uniqueId(),
        html = getTemplate('pricing-row', [
            ['selectProductOptions', selectProductOptions],
            ['selectConfectionOptions', selectConfectionOptions],
            ['uniqId', uniqId]
        ]);
    data.type = data.type ?? 0;

    if (data.product != null) {
        data.product = data.product.toString()
    }

    if (data.confection != null) {
        data.confection = data.confection.toString()
    }

    //TODO Look why this happens
    // html.find('select').selectpicker('refresh');
    html.find('select.pricing_type').selectpicker('val', data.type);
    html.find('select.pricing_product').selectpicker('val', data.product ?? 0);
    html.find('select.pricing_confection').selectpicker('val', data.confection ?? 0);
    html.find('select.pricing_condition').selectpicker('val', data.condition ?? 0);
    html.find('select.pricing_unit').selectpicker('val', data.unit ?? 0);
    html.find('select.pricing_discount').selectpicker('val', data.discount ?? 0);
    html.find('select.pricing_direction').selectpicker('val', data.direction ?? 0);
    if ((data.condition ?? 0) != 0) {
        if (data.condition == 'minimal' || 
            data.condition == 'maximal' || 
            data.condition == 'range' || 
            data.condition == 'dimensions') {
            html.find('.pricing_start').val(data.start ?? 0);
        }
        if (data.condition == 'range' ||
            data.condition == 'dimensions') {
            html.find('.pricing_stop').val(data.stop ?? 0);
        }
    }

    if ((data.discount ?? 0) != 0) {
        if (data.discount == 'percentage') {
            html.find('.pricing_percentage').val(data.value ?? 0);
            html.find('.pricing_percentage_back').val(data.value_back ?? 0);
        } else {
            html.find('.pricing_fixed').val(data.value ?? 0);
            html.find('.pricing_fixed_back').val(data.value_back ?? 0);
        }
    }

    $('.pricing-container').append(html);

    let row = $('.pricing-container .row-pricing[data-id="'+uniqId+'"]');

    if (data.type != 0) {
        
        if ((data.type == 'substrate' || data.type == 'substrate_confection') && (data.confection ?? 0) != 0) {
            row = setConfectionsOptions(row);
            row.find('select.pricing_confection').selectpicker('val', data.confection.toString());
        }
        if ((data.type == 'confection' || data.type == 'confection_substrate') && (data.substrate ?? 0) != 0) {
            row = setSubstratesOptions(row);
            row.find('select.pricing_product').selectpicker('val', data.product.toString());
        }
    }

    setVisibility(row);
    checkPricingVisibility();
    return row;
}

function setConfectionsOptions(rowPricing) 
{
    let list = [],
        val = rowPricing.find('select.pricing_product').selectpicker('val');

    products.forEach(function(p) {
        if (p.id.toString() == val) {
            p.confections.forEach(function(c) {
                list.push(`<option value="${c.id}">${c.systemname}</option>`);
            });
            return;
        }
    });
//    rowPricing.find('select.pricing_confection option[value!=0]').remove();
//    rowPricing.find('select.pricing_confection').append(list);
//    rowPricing.find('select.pricing_confection').selectpicker('refresh');

   return rowPricing;
}

function setSubstratesOptions(rowPricing) 
{
    let list = [],
        val = rowPricing.find('select.pricing_confection').selectpicker('val');

    confections.forEach(function(p) {
        if (p.id.toString() == val) {
            p.substrates.forEach(function(c) {
                list.push(`<option value="${c.id}">${c.systemname}</option>`);
            });
            return;
        }
    });

    // rowPricing.find('select.pricing_product option[value!=0]').remove();
    // rowPricing.find('select.pricing_product').append(list);
    // rowPricing.find('select.pricing_product').selectpicker('refresh');

    return rowPricing;
}

function setOptions(rowPricing, switched = false, changed = false)
{
    let type = rowPricing.find('select#pricing_type').selectpicker('val');
    if (changed && (type == 'substrate' || type == 'substrate_confection')) {
        // console.log(selectConfectionOptions);
        // rowPricing.find('select.pricing_confection option[value!=0]').remove();
        // rowPricing.find('select.pricing_confection').append(selectConfectionOptions);
        // rowPricing.find('select.pricing_confection').selectpicker('val', '0');
        // rowPricing.find('select.pricing_confection').selectpicker('refresh');
    }
    if (changed && (type == 'confection' || type == 'confection_substrate')) {
        // rowPricing.find('select.pricing_product option[value!=0]').remove();
        // rowPricing.find('select.pricing_product').append(selectProductOptions);
        // rowPricing.find('select.pricing_product').selectpicker('val', '0');
        // rowPricing.find('select.pricing_product').selectpicker('refresh');
    }    

    if (switched) {
        if (type == 'substrate' || type == 'substrate_confection') {
            rowPricing.find('select.pricing_confection option[value!=0]').remove();
            rowPricing.find('select.pricing_confection').selectpicker('val', '0');
            rowPricing.find('select.pricing_confection').selectpicker('refresh');

            rowPricing.find('select.pricing_product option[value!=0]').remove();
            rowPricing.find('select.pricing_product').append(selectProductOptions);
            rowPricing.find('select.pricing_product').selectpicker('val', '0');
            rowPricing.find('select.pricing_product').selectpicker('refresh');
        }

        if (type == 'confection' || type == 'confection_substrate') {
            // rowPricing.find('select.pricing_product option[value!=0]').remove();
            // rowPricing.find('select.pricing_product').selectpicker('val', '0');
            // rowPricing.find('select.pricing_product').selectpicker('refresh');
            
            // rowPricing.find('select.pricing_confection option[value!=0]').remove();
            // rowPricing.find('select.pricing_confection').append(selectConfectionOptions);
            // rowPricing.find('select.pricing_confection').selectpicker('val', '0');
            // rowPricing.find('select.pricing_confection').selectpicker('refresh');
        }
    }

    if (type == 'substrate' || type == 'substrate_confection') {
        setConfectionsOptions(rowPricing);
    }
    if (type == 'confection' || type == 'confection_substrate') {
        setSubstratesOptions(rowPricing);
    }
}
        
function setVisibility(rowPricing)
{
    let type = rowPricing.find('select.pricing_type').selectpicker('val'),
        substrate = rowPricing.find('select.pricing_product').selectpicker('val'),
        confection = rowPricing.find('select.pricing_confection').selectpicker('val'),
        condition = rowPricing.find('select.pricing_condition').selectpicker('val'),
        unit = rowPricing.find('select.pricing_unit').selectpicker('val'),
        discount = rowPricing.find('select.pricing_discount').selectpicker('val');
    
    rowPricing.find('.type-dependant').hide();

    // rowPricing.find('.pricing_substrate_container').hide();
    // rowPricing.find('.pricing_confection_container').hide();
    rowPricing.find('.pricing_unit_container').hide();
    rowPricing.find('.pricing_unit').hide();
    rowPricing.find('.pricing_direction_container').hide();
    
    rowPricing.find('.pricing_confection_container').hide();
    rowPricing.find('.pricing_product_container').hide();
    rowPricing.find('.pricing_input').hide();    
    rowPricing.find('.input_start').hide();
    rowPricing.find('.input_stop').hide();
    rowPricing.find('.input_start .input-group-text').hide();
    rowPricing.find('.input_stop .input-group-text').hide();
    rowPricing.find('.input_start #'+unit).show();
    rowPricing.find('.input_stop #'+unit).show();
    rowPricing.find('.pricing_percentage_group').hide();
    rowPricing.find('.pricing_fixed_group').hide();
    rowPricing.find('.pricing_percentage_group_back').hide();
    rowPricing.find('.pricing_fixed_group_back').hide();

    if (type != '0') {
        rowPricing.find('.type-dependant').show();

        if (type == 'substrate' || type == 'substrate_confection') {
            rowPricing.find('.pricing_product_container').removeClass('order-2').addClass('order-1').show();
            rowPricing.find('.pricing_confection_container').removeClass('order-1').addClass('order-2');   

            if (substrate != '0') {
                rowPricing.find('.pricing_confection_container').show();
            }
        } 

        if (type == 'confection' || type == 'confection_substrate') {
            rowPricing.find('.pricing_product_container').removeClass('order-1').addClass('order-2');
            rowPricing.find('.pricing_confection_container').removeClass('order-2').addClass('order-1').show(); 
            
            if (confection != '0') {
                rowPricing.find('.pricing_product_container').show();
            }
        }
    }

    if (condition.toString() != '0' && condition != 'always') {

        rowPricing.find('.pricing_unit_container').show();

        if (condition != 'dimensions') {
            rowPricing.find('.pricing_unit_container .pricing_unit').show();
        } else {
            rowPricing.find('.pricing_unit_container .pricing_unit').hide();
            rowPricing.find('.input_start #DIMENSIONS').show();
            rowPricing.find('.input_stop #DIMENSIONS').show();
        }
    
        if (unit != '0' || condition == 'dimensions') {
            if (condition == 'minimal' || 
                condition == 'maximal' || 
                condition == 'range' ||
                condition == 'dimensions') {
                rowPricing.find('.input_start').show();
            }
       
            if (condition == 'range' ||
                condition == 'dimensions') {
                rowPricing.find('.input_stop').show();
            }
        }
    }

    if (discount == 'percentage') {
        rowPricing.find('.pricing_percentage_group').show();
        rowPricing.find('.pricing_percentage_group_back').show();
        rowPricing.find('.pricing_direction_container').show();
    }
    if (discount == 'fixed') {
        rowPricing.find('.pricing_fixed_group').show();
        rowPricing.find('.pricing_fixed_group_back').show();
    }
}


$(function() {

    $(document).on('click', prefix+'.btn-pricing-add', function() {
        addPricing();
    });

    $(document).on('changed.bs.select', prefix+'select#pricing_product', function(e, clickedIndex) {
        if (typeof clickedIndex != 'number') {
            return;
        }

        if ($(this).closest('.row-pricing').find('select#pricing_type').selectpicker('val') == 'substrate' ||
            $(this).closest('.row-pricing').find('select#pricing_type').selectpicker('val') == 'substrate_confection') {
            setOptions($(this).closest('.row-pricing'), false, true); 
        }

        setVisibility($(this).closest('.row-pricing'));
        
    });

    $(document).on('changed.bs.select', prefix+'select#pricing_confection', function(e, clickedIndex) {
        if (typeof clickedIndex != 'number') {
            return;
        }

        if ($(this).closest('.row-pricing').find('select#pricing_type').selectpicker('val') == 'confection' ||
            $(this).closest('.row-pricing').find('select#pricing_type').selectpicker('val') == 'confection_substrate') {
            setOptions($(this).closest('.row-pricing'), false, true); 
        }

        setVisibility($(this).closest('.row-pricing'));
    });

    $(document).on('changed.bs.select', prefix+'select#pricing_type', function(e, clickedIndex) {

        let switched = false;
        if ($(this).val() != '0') {
            if (($(this).val() == 'substrate' || $(this).val() == 'substrate_confection') && 
                $(this).closest('.row-pricing').find('.pricing_product_container').hasClass('order-2')) {
                switched = true;
            }
            if (($(this).val() == 'confection' || $(this).val() == 'confection_substrate') && 
                $(this).closest('.row-pricing').find('.pricing_confection_container').hasClass('order-2')) {
                switched = true;
            }
        }

        setOptions($(this).closest('.row-pricing'), switched, false);
        setVisibility($(this).closest('.row-pricing'));
    });

    $(document).on('changed.bs.select', prefix+'select', function(e, clickedIndex) {
        setVisibility($(this).closest('.row-pricing'));
    });

    $(document).on('click', prefix+'.btn-pricing-delete', function() {
        $(this).closest('.row-pricing').remove();
        checkPricingVisibility();
    });

    if (pricings.length > 0) {
        pricings.forEach(function(p) { 
            addPricing(p);
        });
    }
    

});