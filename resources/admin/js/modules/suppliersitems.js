function getItemRelatedById(relatedItemId) 
{
    if (relatedItemId == '') return null;

    let relatedItem = _.find(_data.set('itemsRelated').get(), function(item) {
        return item.id == relatedItemId;
    });

    return relatedItem === undefined 
        ? null 
        : relatedItem
    ;
}

function setWarehouseUnitSelect(overrideDefault = false) {

    let relatedItem;
    
    if (!overrideDefault) return;

    if ((relatedItem = getItemRelatedById($('select#item_related_id').val())) === null) {
        return;
    }

    let unitGroupUnitId = _.find(_.get(relatedItem, 'units', []), function(unit) {
        return unit.unit_group_id == 1;
    });

    if (_.get(unitGroupUnitId, 'unit_group_unit_id') === null) {
        return;
    }

    $('.row-unit-group[data-id=1] select.unit-select').selectpicker(
        'val', 
        unitGroupUnitId.unit_group_unit_id.toString()
    );
}

function createCompositionals(unitGroupElement)
{
    let unitGroupId = parseInt(unitGroupElement.data('id')),
        unitGroupUnitId = parseInt(unitGroupElement.find("select.unit-select option:selected").val()),
        unitGroup,
        unitGroupUnit,
        unitgroupUnitCompositionals,
        relatedItem,
        relatedItemUnitGroup = {},
        item = _data.set('item').getFirst(),
        itemUnit
    ;

    if ((relatedItem = getItemRelatedById($('select#item_related_id').val())) !== null) {

        relatedItemUnitGroup = _.find(_.get(relatedItem, 'units', []), function(relatedItemUnit) {
            return relatedItemUnit.unit_group_id == unitGroupId;
        });
    }

    if ((unitGroup = _data
        .set('unit_groups')
        .where('id', '=', parseInt(unitGroupId))
        .getFirst()) === null) {
        return;
    }

    unitGroupUnit = _.find(_.get(unitGroup, 'units', []), function(unitGroupUnit) {
        return unitGroupUnit.id == unitGroupUnitId;
    });

    unitgroupUnitCompositionals = _.get(unitGroupUnit, 'unit.compositionals', []);

    //set current selected compositional values
    unitGroupElement.find('.relations input.unit_compositional_id').each(function() {

        //find unitgroupUnitCompositionals and set selected key value
        let compositionalId = parseInt($(this).val());
        
        let compositional = _.find(unitgroupUnitCompositionals, function(compositional) {
            return compositional.id == compositionalId;
        });

        if (compositional !== undefined) {
            let selectedValue = $(this).closest('.row-item-unit-relation').find('select').selectpicker('val');
            compositional.selectedValue = selectedValue == ''
                ? null
                : selectedValue
            ;
        }
    });

    itemUnit = _.find(_.get(item, 'units', []), function(itemUnit) {
        return itemUnit.unit_group_id == unitGroupId && itemUnit.unit_group_unit_id == unitGroupUnitId;
    });

    unitGroupElement.find('.relations').empty().append(
        unitgroupUnitCompositionals.map(function(compositional) {

            let property = _data
                .set('properties')
                .where('system_context_unit_compositional_id', '=', compositional.id)
                .getFirst()
            ;

            let selected = null;
            let isSetAtRelatedItem = false;

            let propertyValueOptions = _.get(property, 'values', []).map(function(value) {

                selected = _.find(relatedItemUnitGroup.relations, function(relation) {

                    let hasValue = relation.relatable_type === 'PropertyValue' && relation.relatable_id === value.id;

                    isSetAtRelatedItem = isSetAtRelatedItem === false
                        ? hasValue
                        : isSetAtRelatedItem
                    ;

                    return hasValue;

                }) ?? selected;
  
                if (compositional.selectedValue !== null ) {
                    selected = 'PropertyValue::'+value.id === compositional.selectedValue
                        ? {
                            relatable_type: 'PropertyValue',
                            relatable_id: value.id
                        }
                        : selected
                    ;
                }

                return getTemplate('item_unit_relation_option', {
                    'value': 'PropertyValue::'+value.id,
                    'title': value.systemlabel,
                }, false)
            });

            let existingRelation = _.find(itemUnit?.relations, function(relation) {
                return (
                    parseInt(relation.relatable_id) == parseInt(selected?.relatable_id) && 
                    relation.relatable_type == selected?.relatable_type
                );
            });

            console.log('existingRelation', existingRelation);

            let relationElement = getTemplate('item_unit_relation', {
                'id': existingRelation?.id ?? uniqueId(),
                'unit_group_id': unitGroupId,
                'unit_compositional_id': compositional.id,
                'systemlabel': compositional.systemlabel,
                'item_unit_relation_options': propertyValueOptions.join(''),
                'model': selected
                    ? selected.relatable_type + '::' + selected.relatable_id 
                    : ''
            });

            if (selected !== null) {
                relationElement.find('select')
                    .selectpicker('val', selected.relatable_type + '::' + selected.relatable_id)
                ;
            }

            if (isSetAtRelatedItem) {
                relationElement.find('select')
                    .addClass('disabled')
                    .set('tabindex', '-1')
                ;
            }

            relationElement.find('select')
                .selectpicker('destroy')
                .selectpicker('render')
            ;

            return relationElement;
        })
    );
}

function canCreateVariants()
{
    if ($('select#item_related_id').selectpicker('val') == '') {
        return false;
    }

    if ($('.row-unit-group[data-id=1] .relations select').length == 0) {
        return false
    }

    return true;
}

function setVariantsPanelState() 
{
    let panelState = true,
        showErrors = false
    ;

    if ($('select#item_related_id').selectpicker('val') == '') {
        panelState = false;
    }

    if (panelState && $('.row-unit-group[data-id=1] .relations select').length == 0) {
        panelState = false;
    }

    let hiddenVariant = true;
    if (panelState) {
        $('.row-unit-group[data-id=1] .relations select:not(.disabled)').each(function() {
            if ($(this).val() == '') {
                hiddenVariant = false;
            }
        });
    }

    if (panelState && !hiddenVariant && $('.row-unit-group[data-id=3] select.unit-select').selectpicker('val') == '') {
        showErrors = true;
        panelState = false;
    }

    panelState = panelState === true && hiddenVariant === true
        ? false
        : panelState
    ;

    $('.item-variants .overview .overview-header')[panelState ? 'slideDown' : 'slideUp']();
    $('.item-variants .overview .overview-body')[panelState ? 'slideDown' : 'slideUp']();

    $('.item-variants .overview .overview-empty')[!panelState && !showErrors ? 'slideDown' : 'slideUp']();
    $('.item-variants .overview .overview-errors')[showErrors ? 'slideDown' : 'slideUp']();
}

function getCurrentVariants()
{
    return $('.item-variants-container .row-item').map(function() {

        return {
            id: $(this).find('input.variant-id').val(),
            uniq_id: $(this).find('input[name$="[uniq_id]"]').val(),
            unit_pricing_id: $(this).find('input.unit-pricing-id').val(),
            code: $(this).find('input[name$="[code]"]').val(),
            name: $(this).find('input[name$="[name]"]').val(),
            value: convertFromMoneyToStorage($(this).find('input[name$="[value]"]').val()),
            is_active: $(this).find('.variant-toggle-active .form-check-input').is(':checked'),
            units: _.sortBy($(this).find('.composable').map(function() {

                let model = $(this).find('input[name$="[model]"]').val().split('::');

                return {
                    id: $(this).find('input.id').val(),
                    unit_id: $(this).find('input.unit-id').val(),
                    relatable_id: parseInt(model[1]),
                    relatable_type: model[0],
                };
            }), 'relatable_id')
        };
    }).get();
}

function generateVariants()
{
    if (!canCreateVariants()) {
        setVariantsPanelState();
        console.log('Cannot create variants, missing related item or compositional values');
        return;
    }

    let unitGroupElement = $('.row-unit-group[data-id=1]'),
        currentyValues = getCurrentVariants(),
        units = [],
        unitNames = [],
        unitCompositionals,
        options,
        variantCombinations,
        unitComposable,
        existingVariant,
        variantElement,
        variantId
    ;

    unitCompositionals = unitGroupElement.find('.relations .row-item-unit-relation').map(function() {

        options = $(this).find('select').hasClass('disabled')
            ? $(this).find('select option:selected')
            : $(this).find('select option[value!=""]:selected')
        ;

        if (options.length == 0) {
            options = $(this).find('select option[value!=""]');
        }

        let property = _data.set('properties')
            .where('system_context', '=', 'unit')
            .where('system_context_unit_compositional_id', '=', $(this).find('input.unit_compositional_id').val())
            .getFirst()
        ;
   
        return {
            id: $(this).data('id'),
            name: property.systemname,
            unit_compositional_id: parseInt($(this).find('input[type=hidden].unit_compositional_id').val()),
            nameable: !$(this).find('select').hasClass('disabled'),
            values: options.map(function() {
                return {
                    id: $(this).val(),
                    name: $(this).text()
                };
            }).get()
        };

    }).get();

    variantCombinations = _.reduce(
        unitCompositionals,
        function(acc, unit) {
            return _.flatMap(acc, function(combo) {
                return unit.values.map(function(value) {
                    return _.sortBy(combo.concat({
                        composable_id: unit.id,
                        unit_compositional_id: unit.unit_compositional_id,
                        relatable_id: parseInt(value.id.split('::')[1]),
                        relatable_type: value.id.split('::')[0],
                        name: value.name,
                    }), 'unit_compositional_id');
                });
            });
        },
        [[]]
    ).map(function(combo) {
        return {
            uniq_id: combo.map(function(unit) {
                return unit.relatable_type.match(/[A-Z]/g).join('').toLowerCase()+'::'+unit.relatable_id;
            }).join('-'),
            composables: combo
        };
    });

    //create variants panel header
    $('.item-variants .overview-header')
        .empty()
        .append(getTemplate('item-variant-header', {
            'pricing_unit': $('.row-unit-group[data-id=3] select.unit-select option:selected').text(),
            'units-header': unitCompositionals.map(function(unit) {
                return getTemplate('unit-relations-header', {
                    name: unit.name
                }, false);
            }).join('')
        }))
    ;

    //create variants panel body rows
    $('.item-variants .overview-body')
        .empty()
        .append(variantCombinations.map(function(variant) {

            unitNames = [];
           
            //check if variant exists by uniq_id in current variants
            existingVariant = currentyValues.find(function(currentVariant) {
                return currentVariant.uniq_id == variant.uniq_id;
            });

            variantId = existingVariant?.id ?? uniqueId();

            units = variant.composables.map(function(composable) {
            
                unitComposable = unitCompositionals.find(function(unit) {
                    return unit.id == composable.composable_id;
                });

                if (unitComposable === undefined) {
                    return;
                }

                if (unitComposable.nameable) {
                    unitNames.push(composable.name);
                }

                let variantUnit = _.find(existingVariant?.units ?? [], function(unit) {
                    return unit.relatable_id == composable.relatable_id;
                });

                return getTemplate('unit-relation', {
                    name: composable.name,
                    id: variantUnit?.id ?? uniqueId(),
                    variant_id: variantId,
                    unit_id: variantUnit?.unit_id ?? uniqueId(),
                    unit_compositional_id: composable.unit_compositional_id,
                    model: composable.relatable_type+'::'+composable.relatable_id,
                    visibility: '' //visibility: unitComposable.nameable ? ' ' : 'd-none'
                }, false);
            });

            variantElement = getTemplate('item-variant', {
                id: variantId,
                uniq_id: variant.uniq_id,
                code: existingVariant?.code ?? `${unitNames.join(' - ')}`,
                name: existingVariant?.name ?? `${unitNames.join(' - ')}`,
                value: existingVariant?.value ?? (variantCombinations.length > 1
                    ? ''
                    : $('#units_3_value').val() ?? ''
                ),
                value_formatted: existingVariant?.value 
                    ? convertFromStorageToMoney(existingVariant.value, 2) 
                    : (variantCombinations.length > 1
                        ? ''
                        : $('#money_units_3_value').val() ?? ''
                    ),
                unit_pricing_id: existingVariant?.unit_pricing_id ?? uniqueId(),
                is_active: existingVariant?.is_active ?? true,
                relations: units.join(''),
            });

            variantElement.find('.form-check-input').set('checked', existingVariant?.is_active ?? true);
            variantElement.find('.form-check-input-hidden').val((existingVariant?.is_active ?? true) ? 1 : 0);

            return variantElement;
        }))
    ;
}

function resetVariants()
{
    $('.item-variants .overview-body .row-item').remove();
}



function setRuleActiveState(ruleElement, state = null)
{
    if (state === null) {
        state = ruleElement.find('.form-check-input').is(':checked');
    }

    ruleElement[state ? 'removeClass' : 'addClass']('row-item-disabled');
    ruleElement.find('.form-check-input').set('checked', state);
    ruleElement.find('.form-check-input-hidden').val(state ? 1 : 0);

    ruleElement.find('.rule-toggle-active input').set('disabled', false);
    if ($('.item-variants-container .row-item.row-item-disabled[data-id="'+ruleElement.find('select.rule-apply-to').selectpicker('val').replace('ItemVariant::', '')+'"]').length > 0) {
        ruleElement.find('.rule-toggle-active input').set('disabled', true);
    }
}

function updateRulesUnitGroup()
{
    $('select.unit-select').each(function() {

        let unitGroupId = parseInt($(this).closest('.row-unit-group').data('id'));
        let unitGroupUnitValue = $(this).find('option:selected').text();

        $('.rules-container .row-rule').each(function() {
            if (parseInt($(this).find('select.unit-group-id').selectpicker('val')) == unitGroupId) {
                $(this).find('.unit-group-value').html(unitGroupUnitValue);
            }
        });
    });
}

function getCurrentRules()
{
    let rules = [],
        type, 
        unitSpec
    ;

    return $('.rules-container .row-rule').map(function() {

        const type = $(this).find('select.rule-type').val();
        const unitSpec = $(this).find('select.unit-specification').val();

        return {
            id: $(this).data('id'),
            is_active: $(this).find('.form-check-input').is(':checked'),
            type: type,
            apply_to: $(this).find('select.rule-apply-to').val(),

            unit_group: $(this).find('select.unit-group').val(),
            unit_specification: unitSpec,
            unit_value_min: ['min', 'range'].indexOf(unitSpec) !== -1
                ? $(this).find('input.unit-specification-min').val()
                : null,
            unit_value_max: ['max', 'range'].indexOf(unitSpec) !== -1
                ? $(this).find('input.unit-specification-max').val()
                : null, 
            pricing_type: type == 'pricing'
                ? $(this).find('select.pricing-type').val()
                : null,
            pricing_value: type == 'pricing'
                ? $(this).find('input.pricing-amount-value').val()
                : null
        };
    }).get();
}

function updateRulesApplyToOptions(variants = null)
{
    let applyToElements = $('.rules-container .row-rule select.rule-apply-to');

    variants ??= getCurrentVariants();

    //no variants, only item
    if (!canCreateVariants() ||
        (variants?.length ?? 0) <= 1) {
        applyToElements.selectpicker('destroy');
        applyToElements.find('option[value!="item"]').set('selected', false);
        applyToElements.find('option[value="item"]').set('selected', true);
        applyToElements.find('option[class^="option-optgroup"]').remove();
        applyToElements
            .addClass('disabled')
            .set('tabindex', '-1')
            .selectpicker('render')
        ;
        return;
    }
    
    applyToElements.selectpicker('destroy');

    applyToElements.each(function() {

        let selectElement = $(this),
            selectedValue = $(this).find('option:selected')?.val()?.replace('ItemVariant::', '') || ''
        ;

        selectElement.find('option[class^="option-optgroup"]').remove();
        selectElement.append(`
            <option value="optgroup-variants" class="option-optgroup" data-depth="0" disabled>Variants</option>
            ${variants.map(function(variant) {
                return `
                    <option 
                        value="ItemVariant::${variant.id}" 
                        data-parent="optgroup-variants" 
                        class="option-optgroup-child"
                        ${String(variant.id) == String(selectedValue) ? 'selected' : ''}
                    >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${variant.name}</option> 
                `;
            }).join('')}
        `);

        if (selectedValue == 'item') {
            selectElement.find('option[value="item"]').set('selected', true);
        }

    });

    applyToElements
        .removeClass('disabled')
        .set('tabindex', '')
        .selectpicker('render')
    ;
}

function updateRulesApplyToOptionsTitles(variantId, variantName)
{
    let rulesApplyToOptions = $('.rules-container .row-rule select.rule-apply-to option[value="ItemVariant::'+variantId+'"]');
    
    rulesApplyToOptions.each(function() {
        $(this).text(variantName);
    });

    if (rulesApplyToOptions.length > 0) {
        $('.rules-container .row-rule select.rule-apply-to')
            .selectpicker('destroy')
            .selectpicker('render')
        ;
    }
}

function updateRules()
{
    let variants = getCurrentVariants(),
        variantIds = variants?.map(function(variant) {
            return variant.id;
        }) || [],
        rules
    ;

    updateRulesApplyToOptions(variants);

    rules = getCurrentRules();

    //filter rules by apply_to where variants dont exists anymore
    rules = rules?.forEach(function(rule) {

        if (rule.apply_to == 'item' ||
            rule.apply_to == '' ||
            variantIds.indexOf(rule.apply_to.replace('ItemVariant::', '')) !== -1
        ) return;

        //remove rule if apply_to is not item and variant does not exist
        $('.rules-container .row-rule[data-id="'+rule.id+'"]').fadeOut(function() {
            $(this).remove();
        });
    });
}

$(function() {
      
    //VARIANTS-ALIKE
    $(document).on('change keyup', '#units_3_value', function() {
        
        setTimeout(() => {
            if (!canCreateVariants() ||
                (getCurrentVariants()?.length ?? 0) <= 1) {

                $('.item-variants-container .row-item input[data-type=money]')
                    .set('value', $('#units_3_value').val())
                    .trigger('change')
                ;
            }
        }, 100);
    });

    $(document).on('change keyup', '.item-variants-container .row-item input.name', function() {
        updateRulesApplyToOptionsTitles(
            $(this).closest('.row-item').find('.variant-id').val(), 
            $(this).val()
        );
    });

    $(document).on('change', '.variant-toggle-active .form-check-input', function() {
        
        $(this).closest('.row-item')[$(this).is(':checked') 
            ? 'removeClass' 
            : 'addClass'
        ]('row-item-disabled');
        
        let checked = $(this).is(':checked');

        $('.rules-container select.rule-apply-to option[value="ItemVariant::'+$(this).closest('.row-item').find('.variant-id').val()+'"]:selected').each(function() {
            setRuleActiveState(
                $(this).closest('.row-rule'), 
                checked === false 
                    ? false
                    : null
            );
        });
    });

    $('.row-unit-group select.unit-select').on('changed.bs.select', function(e, clickedIndex, isSelected, previousValue) {
 
        updateRulesUnitGroup();

        if (typeof clickedIndex != 'number') return;

        if ($(this).closest('.row-unit-group').data('id') == 1) {
            createCompositionals($(this).closest('.row-unit-group'));
        }
        
        generateVariants();
        setVariantsPanelState();
        updateRules();
    });

    $(document).on('changed.bs.select', '.row-unit-group[data-id=1] .relations select', function(e, clickedIndex) {

        if (typeof clickedIndex != 'number') return;
        
        generateVariants();
        setVariantsPanelState();
        updateRules();
    });

    $('select#item_related_id').on('changed.bs.select', function(e, clickedIndex, isSelected, previousValue) {   

        setWarehouseUnitSelect(true);
        updateRulesUnitGroup();
        createCompositionals($('.row-unit-group[data-id=1]'));
        
        if (!isNaN(previousValue) && parseInt(previousValue) !== parseInt($(this).selectpicker('val'))) {
            resetVariants();
        }

        generateVariants();
        setVariantsPanelState();
        updateRules();
    });

    $('select#item_related_id').trigger('changed.bs.select');



    // RULES //
    $(document).on('changed.bs.select', '.rules-container select.unit-specification', function() {

        let row = $(this).closest('.row-rule');

        if ($(this).selectpicker('val') == 'range') {

            row.find('input.unit-specification-min')
                .set('data-range', true)
                .trigger('change')
            ;

            row.find('input.unit-specification-max')
                .set('data-range', true)
                .trigger('change')
            ;
        }
    });

    $(document).on('changed.bs.select', '.rules-container select.unit-group-id', function() {

        let row = $(this).closest('.row-rule');

        if ($(this).val() == '') {
            row.find('.unit-group-value').html('');
        } else {
            row.find('.unit-group-value').html($('select#units_'+$(this).val()+'_unit_group_unit_id option:selected').text());
        }
    });

    $(document).on('change', '.rules-container .form-check-input', function() {
        setRuleActiveState($(this).closest('.row-rule'));
    });

    $(document).on('click', '.btn-rule-delete-confirm', function() {
        $('.overview-rules .row-rule[data-id="'+$(this).data('id')+'"]').fadeOut(function() {
            $(this).remove();
        });

        bootstrap.Modal.getInstance($('.modal')).hide();
    });

    $('.btn-rule-add').on('click', function() {

        let row,
            variants = getCurrentVariants(),
            canVariants = true
        ;

        if (!canCreateVariants() ||
            (variants?.length ?? 0) <= 1) {
            canVariants = false;
        }

        row = getTemplate('row-rule', {
            'id': uniqueId(),
            'is_active': true,
            'is_locked': false,
            'apply_to': canVariants ? '' : 'item',
        });

        row.find('select.rule-apply-to option[class^="option-optgroup"]').remove();
        row.find('.form-check-input').set('checked', true);
        row.find('.form-check-input-hidden').val(1);
        if (!canVariants) {
            row.find('select.rule-apply-to option[value="item"]').set('selected', true);
        }
        row.find('.selectpicker').selectpicker();

        $('.rules-container').append(row);

        updateRulesApplyToOptions();
    });
});