


// import { Attachments } from '../widgets/attachments.js';
// import { GraduatedValues } from '../widgets/graduatedvalues.js';
// import { getHTML, uniqueId, setLoading, getTemplate   } from '../functions.js';

// let pricingGraduatedList = [],
//     variants,
//     variantCreator,
//     variantBuilder,
//     variantOptionTree,
//     machines,
//     suppliers;


function setMainCategory() 
{
    let selectedValues = $('select#categories').selectpicker('val'),
        lastCats = [],
        childIds = [];

    if (selectedValues.length == 0) {
        $('.row-category-id').slideUp();
        return false;
    }

    $('select#categories option').each(function() {

        if (selectedValues.indexOf($(this).val()) == -1) {
            return;
        }

        if ($(this).data('last') == '1') {
            lastCats.push($(this).val());
        } else {

            childIds = getChildsByParent('select#categories', $(this).val());
            
            let filtered = selectedValues.filter(function(n) {
                return childIds.indexOf(parseInt(n)) !== -1;
            });

            if (filtered.length == 0) {
                lastCats.push($(this).val());
            }
        }
    });

    if (lastCats.length < 2) {
        $('.row-category-id').slideUp();
        $('input[name=category_id]').val(lastCats[0]);    
    } else {
        
        $('.row-category-id').slideDown();

        $('select#category_main').selectpicker('destroy');
        $('select#category_main option').remove();
        $('select#category_main').append($('<option/>', { 
            value: 0,
            text : 'Nothing selected yet'
        }).attr('selected', true));
        
        lastCats.forEach(function(catId) {
            let cat = $('#categories option[value='+catId+']');
            
            $('select#category_main').append($('<option/>', { 
                value: cat.val(),
                'data-content': cat.data('content').trim()
            }));
            
            if ($('input[name=category_id]').val() == cat.val()) {
                $('select#category_main option[value='+cat.val()+']').attr('selected', true);
            }
        }); 
       $('select#category_main').selectpicker('render'); 
    }

    
}

function setCategoryProperties(selector) {

    let propertiesSelected = [],
        option;

    $('.property:not(.property-global)').hide();

    $(selector).selectpicker('val').forEach(function(i) {

        option = $(selector+' option[value='+i+']');

        if (option.data('properties-ids') == undefined) {
            return;
        }

        option.data('properties-ids').forEach(function(id) {
            $('.property[data-id='+id+']').show();
            propertiesSelected.push(id);
        });
    });


    $('.property.property-global').each(function() {
        propertiesSelected.push($(this).data('id'));
    });

    if (propertiesSelected.length == 0) {
        $('.no-properties').fadeIn();
    } else {
        $('.no-properties').fadeOut();
    }
}




function setSelected(selector, value) {
    let vals = $(selector).selectpicker('val');
    vals.push(value);
    $(selector).selectpicker('val', vals);
}

function setNotSelected(selector, value) {
    let vals = $(selector).selectpicker('val');
    if (vals.indexOf(value) != -1) {
        vals.splice(vals.indexOf(value), 1);
    }
    $(selector).selectpicker('val', vals);
}

function setParentSelected(selector, parentId) {
    $(selector).find("option").each(function () {
        if (this.value == parentId) {
            setSelected(selector, this.value);
            setParentSelected(selector, $(this).attr("data-parent"));
        }
    });
}

function setChildsNotSelected(selector, valueId) {
    $(selector).find("option").each(function () {
        if ($(this).data("parent") == valueId) {
            setNotSelected(selector, $(this).val());
            setChildsNotSelected(selector, $(this).val());
        }
    });
}

function getChildsByParent(selector, parentId) {
    let childs = [];
    $(selector).find("option").each(function() {
        if (parseInt($(this).data("parent")) == parseInt(parentId)) {
            childs.push(parseInt($(this).val()));
            childs = childs.concat(getChildsByParent(parseInt($(this).val())));
        }
    });
    return childs;
}





$(function() {

    // new Attachments({
    //     element: '.widget-attachments'
    // });

    $('select#category_main').on('changed.bs.select', function (e, clickedIndex) {

        if (typeof clickedIndex != 'number') {
            return;
        }

        $('input[name=category_id]').val($(this).find('option').eq(clickedIndex).val());    
    });

    $('.form-products select#categories').on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {

        if (typeof clickedIndex != 'number') {
            return;
        }

        if ($(this).find('option').eq(clickedIndex).is(':selected')) {
            setParentSelected($(this).find('option').eq(clickedIndex).attr("data-parent"));
        }
        else {
            setChildsNotSelected($(this).find('option').eq(clickedIndex).val());
        }

        setMainCategory();
        setCategoryProperties('form.products #categories');
    });

    if ($('.form-products select#categories').length > 0) {
        setMainCategory();
        setCategoryProperties('.form-products select#categories');
    }
});


