$(function() {
  
    $('.row-unit-group:not([data-id=4]) select.unit-select').on('changed.bs.select', function() {

        let selected = $(this).find("option:selected"),
            unitGroupElement = $(this).closest('.row-unit-group'),
            elements = []
        ;

        let unitGroup = _data
            .set('unit_groups')
            .where('id', '=', parseInt($(this).closest('.row-unit-group').data('id')))
            .getFirst()
        ;

        let unitGroupUnit = _.find(unitGroup.units, function(unit) {
            return unit.id == parseInt(selected.val());
        });

        _.get(unitGroupUnit, 'unit.compositionals', []).forEach(function(data) {

            let property = _data
                .set('properties')
                .where('system_context_unit_compositional_id', '=', data.id)
                .getFirst()
            ;

            if (property === null) return;

            let propertyValueOptions = (property.values ?? []).map(function(value) {
                return getTemplate('item_unit_relation_option', {
                    'value': 'PropertyValue::'+value.id,
                    'title': value.systemlabel
                }, false)
            });

            elements.push(
                getTemplate('item_unit_relation', {
                    'id': uniqueId(),
                    'unit_group_id': unitGroupElement.data('id'),
                    'unit_compositional_id': data.id,
                    'systemlabel': data.systemlabel,
                    'item_unit_relation_options': propertyValueOptions.join('')
                })
            );
        });

        unitGroupElement.find('.relations').html(``).html(elements);
        unitGroupElement.find('.relations select').selectpicker('render');
    });
});