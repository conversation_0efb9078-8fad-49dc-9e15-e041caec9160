import PurchaseOrder from '../widgets/purchaseorder';

window.PurchaseOrder = PurchaseOrder;

let purchaseOrder;

function setQuantityUnitType()
{
    let element = $('.modal .input #quantity'),
        val = $('.modal .input select#unittype_sales').selectpicker('val'),
        hasWrapper = element.closest('.input').find('.input-group').length > 0;

    if (element.parent().hasClass('input-group')) {
        element.closest('.input').find('.input-group-text').remove();
        element.unwrap();
    }

    if (val != '' && unitTypeSuffixes[val] !== undefined) {
        element.wrap('<div class="input-group input-group-sm mb-3"></div>');
        $(`<span class="input-group-text">${unitTypeSuffixes[val]}</span>`).insertAfter(element.closest('.input').find('#quantity'));
    }
}

function getPropertyValueBySystemUsage(systemUsage) 
{
    if (supplierItem["related_item"] === undefined ||
        supplierItem.related_item["property_values"] === undefined
    ) {
        return false;
    }

    let item = false;
    supplierItem.related_item.property_values.forEach(function(propertyValue) {

        console.log(propertyValue);

        if (propertyValue["property"] !== undefined &&
            propertyValue.property["system_usage"] !== undefined &&
            propertyValue.property.system_usage == systemUsage) {
            item = propertyValue;
            return;
        }

    });

    return item;
}


$(function() {

    purchaseOrder = new PurchaseOrder();

    console.log(purchaseOrder);
});