//============STRINGS============
function camelCase2SnakeCase(str)
{
    return str.replace(/[A-Z]/g, function(letter, index) {
        return index == 0 
            ? letter.toLowerCase() 
            : '_'+ letter.toLowerCase();
    });
}

function ucFirst(value) 
{
    return value.charAt(0).toUpperCase() + value.slice(1);
}



//============TEMPLATING============
function getHTML(element)
{
    return $("<div />").append($(element).clone()).html();
}

function replaceInHTML(item, replacements)
{
    let html = typeof item === 'object'
        ? getHTML(item)
        : item
    ;

    Object.keys(replacements).forEach(function(k) {
        html = html.replace(
            new RegExp('(\\[\\[|\\{\\{)(TEMPLATE=)?' + k.toUpperCase() + '(\\]\\]|\\}\\})', 'gmi'),
            replacements[k]
        );
    });

    html = html.replace(
        /(?:\{\{|\[\[)([A-Z0-9_]+)(?:=([^}\]]+))?(?:\}\}|\]\])/gmi,
        (match, name, value) => value ? value : ''
    );

    return typeof item === 'object'
        ? $(html)
        : html
    ;
}

function getTemplate(
    templateName, 
    replacements = {},
    returnObject = true
)
{  
    let element = $('template#template-'+templateName).html();

    if (element == null) {
        return false;
    }

    element = replaceInHTML(
        element, 
        replacements
    );

    return !returnObject
        ? element
        : $(element)
    ;
}



//============PRICING============
function normalizePrice(price) {

    if ((price ?? null) === null) return null;

    //remove all non digit chars except . and ,
    price = price.toString().replace(/\s|[a-zA-Z]+/gi, '');

    //number contains more then 1 .
    if( (price.match(/\./gi) || []).length > 1 && 
        (price.match(/\,/gi) || []).length == 0) {
        price = price.replace(/\./gi, '');
    }

    //number contains more then 1 ,
    if( (price.match(/\,/gi) || []).length > 1 && 
        (price.match(/\./gi) || []).length == 0) {
        price = price.replace(/\,/gi, '');
    }

    //number contains atleast 1 , and 1 .
    if( (price.match(/\./gi) || []).length > 0 && 
        (price.match(/\,/gi) || []).length > 0) {

        if (price.replace(/\d/gi, '').charAt(0) == '.') {
            price = price.replace(/\./gi, '');
        } else {
            price = price.replace(/\,/gi, '');
        }
    }

    //replace , by .
    return parseFloat(price.replace(/\,/gi, '.'));
}

function formatPrice(price, decimals) {

    return normalizePrice(price).toFixed(decimals).replace('.', ',');
}

function getLocalePrice(price, decimals = 2) {
    return "&euro; "+formatPrice(price, decimals);
}



//============CONVERTERS============
function convertFromMoneyToStorage(valueFrom) {
    valueFrom = normalizePrice(valueFrom);
    return Math.round(valueFrom * Math.pow(10, 5));
}

function convertFromStorageToMoney(valueFrom, valueToDecimals) {
    const valueTo = valueFrom / Math.pow(10, 5);
    return formatPrice(valueTo, valueToDecimals);
}



//============LOSSLESS============
function uniqueId() 
{
    //return Math.random().toString(36).substr(2, 16);

    return (
        Math.random().toString(36).toString().substring(2, 11) + 
        Math.random().toString(36).toString().substring(2, 11) +
        Math.random().toString().substring(1, 10)
    ).toString().replace('.', '-');
}

function setLoading(sSelector, state = 'add') {
    
    if (state == 'add' && $(sSelector).find('.loading').length == 0) {
        var sLoading = '<div class="loading"><i class="fa fa-cog fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span></div>'
        $(sSelector).append(sLoading);
    }
    else if (state == 'remove') {
        $(sSelector).find('.loading').remove();
    }
}




//============COMPONENT FUNCTIONS============
function setOverviewVisibility(overviewElement) {

    let itemContainer = overviewElement.find(overviewElement.data('container') ?? '.overview-body'),
        noItemsSelector = overviewElement.data('no-items') ?? '.overview-empty'
    ;

    if (itemContainer.find(overviewElement.data('item') ?? '.row-item').length == 0) {
        overviewElement.find('.overview-header').hide();
        overviewElement.find('.overview-body').hide();
        overviewElement.find('.overview-footer').hide();
        overviewElement.find(noItemsSelector).show();
    } else {
        overviewElement.find('.overview-header').show();
        overviewElement.find('.overview-body').show();
        overviewElement.find('.overview-footer').show();
        overviewElement.find(noItemsSelector).hide();
    }
}

function removeModal()
{
    bootstrap.Modal.getInstance($('body > .modal')).hide();
}


//============DEPECATED============
function inputDecimals(v, decimals = 2)
{
    console.log('Function inputDecimals (should be) DEPRECATED.');
    v = v.toString().replace(/[^0-9.,]/g, '').replace(/(\..*?)\..*/g, '$1');
    if (v != '') {
        return parseFloat(normalizePrice(v).toFixed(decimals)).toString().replace(',', '.');
    }
    
    return v;
}


function updateSortOrder(element) {
    console.log('Function updateSortOrder is DEPRECATED.');
    var list = [];
    let sortcol = 'sort';
    
    if (typeof element.data('sort') !== 'undefined') {
        sortcol = element.data('sort');
    }


    element.find('.row-item').each(function () {
        list.push({
            id: $(this).data('id'),
            parent_id: $(this).data('parent-id'),
        });
    });

    $.ajax({
        url: config.domain+'/'+config.app+"/functions/update_sort",
        type: 'post',
        data: {
            'type': element.closest('.card-overview').data('type'),
            'sortcol': sortcol,
            'sort': JSON.stringify(list)
        },
        success: function (data) {
            console.log(data);
        }
    });
}
function isMoney(inputValue)
{
    console.log('Function isMoney is (probably) DEPRECATED.');
    if (inputValue == '' ||
        isNaN(formatPrice(inputValue))) {
        return false;
    }

    return true;
}


function getCookie(name) {
    const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
    return match ? match[2] : null;
}

function setSelectData(selectElementOrSelector) {
    
    let selectElement = typeof obj === 'string'
        ? $(selectElementOrSelector)
        : selectElementOrSelector
    ;

    if (selectElement.length == 0) return;

    if (selectElement.is('select') === false) return;

    let val = $(this).hasClass('selectpicker')
        ? $(this).selectpicker('val')
        : $(this).val()
    ;

    val = Array.isArray(val)    
        ? val
        : (val || null)
    ;

    $(this).set('data-value-id', val);
    $(this).set('data-value-text', $(this).find('option:selected').map(function() {
        return $(this).text();
    }).get().join(', '));
}   



export default function() { };

export {
    ucFirst,
    camelCase2SnakeCase,
    getHTML,
    uniqueId,
    replaceInHTML,
    getTemplate,
    setLoading,
    normalizePrice,
    formatPrice,
    updateSortOrder,
    inputDecimals,
    isMoney,
    getLocalePrice,
    convertFromMoneyToStorage,
    convertFromStorageToMoney,

    setOverviewVisibility,
    removeModal,
    getCookie,

    setSelectData

};