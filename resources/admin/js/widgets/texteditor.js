let setReadOnly = function(editors) {
    editors.forEach(function(editor) {
        if ($('#'+editor.id).hasClass('texteditor-readonly')) {
            editor.setMode('readonly'); 
            editor.getBody().style.backgroundColor = '#e9ecef';
        }
    });
};

let filePickerCallback = function (cb, value, meta) {
    var input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', 'image/*');
    input.onchange = function () {
        var file = this.files[0];

        // var sFilename = file.name.split('.');
        // sFilename.pop();
        // sFilename = sFilename.join('.');


        var reader = new FileReader();
        reader.onload = function () {
            var id = 'blobid' + (new Date()).getTime();
            // var id = sFilename;
            var blobCache = tinymce.activeEditor.editorUpload.blobCache;
            var base64 = reader.result.split(',')[1];
            var blobInfo = blobCache.create(id, file, base64);
            blobCache.add(blobInfo);
            cb(blobInfo.blobUri(), { title: file.name });
        };
        reader.readAsDataURL(file);
    };
    input.click();
};

// let initInstanceCallback = function(editor) {
//     editor.on('Change', function(e) {
//         if (typeof formObserver !== 'undefined') {
//             formObserver.changed = true;
//         }
//     });
// };

let configs = {
    license_key: 'gpl',
    theme: 'silver',
    skin: 'oxide',
    plugins: [],
   // content_css : config.domain+"/build/assets/app-53283964.css,"+config.domain+"/css/tinymce.css?s="+new Date().getTime(),
    //link_class_list: buttons,
   // link_list: links,    // forced_root_block : '',
    // force_p_newlines : false,
    force_br_newlines : true,
    valid_elements: 'br',
    body_id: 'page',
    width: '100%',
    min_height: 300,
    max_height: 600,
    min_width: 1161,
    max_width: 1161,
    paste_as_text: true,
    relative_urls: false,
    paste_data_images: true,
    menubar: false,
    automatic_uploads: true,
    file_picker_types: 'image',
    resize: 'both',
    plugins: 'autoresize media quickbars link lists wordcount code table autosave fullscreen',
    autosave_retention : '30m',
    autosave_restore_when_empty : true,
    file_picker_callback: filePickerCallback,
    //init_instance_callback: initInstanceCallback,
};

function setTextEditor(uniqId, type = 'default') 
{
    let config = Object.assign({}, { ...configs });
    
    config.selector = `.texteditor[data-editor-uniq-id="${uniqId}"]`;
  //  config.body_class = 'style style-'+type;

    if (type == 'default') {

        config = Object.assign(config, {
            quickbars_selection_toolbar: 'bold italic | h1 h2 h3 h4 h5 h6 | link',
            toolbar:
                'undo redo | ' +
                'bold italic underline strikethrough | ' +
                'numlist bullist | ' +
                'outdent indent | ' +
                'insertfile image media template  anchor table link | ' +
                'removeformat | code | restoredraft fullscreen ',            
        });
    }

    if (type == 'mini') {
      
        config = Object.assign(config, {
            plugins: 'link lists wordcount autoresize code autosave fullscreen',
            toolbar:
                'undo redo | ' +
                'bold italic underline strikethrough | ' +
                'numlist bullist | ' +
                'link | ' +
                'removeformat | code | restoredraft fullscreen',
        });
    }

    if (type == 'plaintext') {
      
        config = Object.assign(config, {
            plugins: 'link lists wordcount autoresize code autosave fullscreen',
            toolbar:
                'undo redo | ' +
                'bold italic underline strikethrough | ' +
                'link | ' +
                'removeformat | code | restoredraft fullscreen',
        });
    }

    window.tinymce.init(config).then(setReadOnly);
}

export default function() { };

export {
    setTextEditor
};