export default class ItemUnitConverter {

    unitGroups = [
        {
            id: 1,
            group: 'warehouse',
            unit: {
                id: 7,              //roll
                value: 6,           //amount in rolls
                price: null,        //price per roll
                relations: [
                    {
                        type: 'PropertyValue',
                        attribute: 'dimensional.width',
                        value: 320, //total amount in cm1
                        unit_id: 56 //cm1
                    },
                    {
                        type: 'PropertyValue',
                        attribute: 'dimensional.length',
                        value: 75,  //total amount in m1
                        unit_id: 53,//m1
                    },
                    {
                        type: 'PropertyValue',
                        attribute: 'dimensional.height',
                        value: 1,   //total amount in mm1
                        unit_id: 61,//mm1
                    },
                ]
            }
        },
        {
            id: 2,
            group: 'sales',
            unit: {
                id: 53,             //m1
                value: null,        //amount in m1
                price: null         //price per m1
            }
        },
        {
            id: 3,
            group: 'pricing',
            unit: {
                id: 53,             //m2
                value: null,        //amount in m2
                price: 25           //price per m2,
            }
        },
        {
            id: 4,
            group: 'weight',
            unit: {
                id: 4,              //KG
                value: 15,          //amount in KG (per m2)
                price: null,        //price per KG
                relations: [
                    {
                        type: 'Unit',
                        attribute: null,
                        value: null,
                        unit_id: 4  //m2
                    }
                ]
            }
        },
    ];

    constructor(data = {}) {

        this.load(data);
    }

    events() {
        let self = this;    
    }

    load(data) 
    {
        console.log(data);
    }

    setUnitGroup(unitGroupId, unitId)
    {

    }


}