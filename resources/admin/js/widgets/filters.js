$(function () {

    $('select.filter-input').on('changed.bs.select', function (e, clickedIndex, isSelected) {

        e.preventDefault();
        e.stopPropagation();

        if (clickedIndex == 0 &&
            isSelected) {
            $(this).selectpicker('val', [ '' ]);
            return;
        }

        if (clickedIndex != 0 &&
            isSelected) {

            let val = $(this).selectpicker('val');
            if (!Array.isArray(val)) {
                val = [ val ];
            }

            $(this).selectpicker('val', val.filter(function (e) {
                return e != '';
            }));

            return;
        }
    });


});