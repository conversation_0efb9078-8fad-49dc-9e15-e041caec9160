
import {uniqueId , getTemplate} from '../functions.js';

export class ShipmentPricingRules {

    constructor(args = {}) 
    {
        this.args = args;

        this.events();

        this.init();
    }

    init()
    {
        let self = this;

        let data = $('.panel-shipment-rules').data('pricings');

        if (data != '' &&
            data != '[]') {

            data.forEach(function(rule) {
                self.addRule(rule);
            });
        }
    }

    events()
    {
        let self = this,
            selector = '.panel-shipment-rules ';
            
        $(document).on('click', selector+'.btn-rule-add', function() {
            self.addRule();
        });

        $(document).on('click', selector+'.btn-rule-delete', function() {
            self.deleteRule($(this).closest('.row-pricing-rule'));
        });

        $(document).on('changed.bs.select', selector+'.row-pricing-rule .rule_method', function(e, clickedIndex) {
            self.setVisibility($(this).closest('.row-pricing-rule'));
        });
        
        $(document).on('changed.bs.select', selector+'.row-pricing-rule .conditions', function(e, clickedIndex) {
            self.setVisibility($(this).closest('.row-pricing-rule'));
        });
        
        $(document).on('changed.bs.select', selector+'.row-pricing-rule .pricing_discount', function(e, clickedIndex) {
            self.setVisibility($(this).closest('.row-pricing-rule'));
        });

        $(document).on('changed.bs.select', selector+'.row-pricing-rule .pricing_direction', function(e, clickedIndex) {
            self.setVisibility($(this).closest('.row-pricing-rule'));
        });        
    }


    addRule(data = [])
    {

        let self = this,
            uniqId = uniqueId(),
            html = getTemplate('pricing-rule-row', [
                ['uniqId', uniqId]
            ]);
        
        data.method = data.method ?? [];
        data.days = data.days ?? [];
        data.condition = data.condition ?? 0;
      
        $('.rules-container').append(html);

        html.find('select').selectpicker();

        html.find('select.rule_method').selectpicker('val', data.method );
        html.find('select.days').selectpicker('val', data.days);
        html.find('select.conditions').selectpicker('val', data.condition);
        
        html.find('select.pricing_discount').selectpicker('val', data.discount ?? 0);
        html.find('select.pricing_direction').selectpicker('val', data.direction ?? 0);       
    
        if (data.condition != 0) {
            if (data.condition == 'minimal' || 
                data.condition == 'range') {
                html.find('.pricing_start').val(data.start ?? 0);
            }
            if (data.condition == 'maximal' || 
                data.condition == 'range') {
                html.find('.pricing_stop').val(data.stop ?? 0);
            }
        }
    
        if ((data.discount ?? 0) != 0) {
            if (data.discount == 'percentage') {
                html.find('.pricing_percentage').val(data.value ?? 0);
            } else {
                html.find('.pricing_fixed').val(data.value ?? 0);
            }
        }
    
         
    
         let row = $('.rules-container .pricing-rule-row[data-id="'+uniqId+'"]');
    
        self.setVisibility(row);
        self.checkRulesVisibility();
    }

    deleteRule(row)
    {
        let self = this;

        row.slideUp(function() {
            $(this).remove();
            self.checkRulesVisibility();
        });
    }

    setVisibility(row)
    {
        let methods = row.find('select.rule_method').selectpicker('val'),
            condition = row.find('select.conditions').selectpicker('val'),
            discount = row.find('select.pricing_discount').selectpicker('val');

        if (methods.length == 0) {
            row.find('.method-dependant').slideUp();
        } else {
            row.find('.method-dependant').slideDown();
        }

        if (condition == 'minimal' || 
            condition == 'range') {
            row.find('.input_start').show();
        } else {
            row.find('.input_start').hide();
        }

        if (condition == 'maximal' ||
            condition == 'range') {
            row.find('.input_stop').show();
        } else {
            row.find('.input_stop').hide();
        }

        row.find('.pricing_percentage_group').hide();
        row.find('.pricing_fixed_group').hide();
        row.find('.pricing_direction_container').hide();

        if (discount == 'percentage') {
            row.find('.pricing_percentage_group').show();
            row.find('.pricing_direction_container').show();
        }

        if (discount == 'fixed') {
            row.find('.pricing_fixed_group').show();
        }

        this.checkRulesVisibility();
    }

    checkRulesVisibility()
    {
        if ($('.panel-shipment-rules .rules-container .row-pricing-rule').length == 0) {
            $('.panel-shipment-rules .rules-container .no-rules').show();
        } else {
            $('.panel-shipment-rules .rules-container .no-rules').hide();
        }
    }


    setAttribute(elementOrSelector, attribute, value)
    {
        if (typeof elementOrSelector == 'string') {
            elementOrSelector = $(elementOrSelector);
        }

        if (typeof value == 'object') {
            value = JSON.stringify(value);
        }

        elementOrSelector
            .attr(attribute, value)
            .prop(attribute, value);

        return elementOrSelector;
    }

    setProperty(elementOrSelector, property, value)
    {
        if (typeof elementOrSelector == 'string') {
            elementOrSelector = $(elementOrSelector);
        }

        if (typeof value == 'object') {
            elementOrSelector
                .attr('data-'+property, JSON.stringify(value))
                .prop('data-'+property, JSON.stringify(value))
                .data(property, value);
        } else {
            elementOrSelector
                .attr('data-'+property, value)
                .prop('data-'+property, value)
                .data(property, value);
        }

        return elementOrSelector;
    }

    setProperties(elementOrSelector, propertyListObject)
    {
        if (typeof elementOrSelector == 'string') {
            elementOrSelector = $(elementOrSelector);
        }

        Object.keys(propertyListObject).forEach(function(k) {
            elementOrSelector
                .data(k, propertyListObject[k])
                .attr('data-'+k, propertyListObject[k])
                .prop('data-'+k, propertyListObject[k]);
        });

        return elementOrSelector;
    }

    setLoading(element, isRemove = false, isRelative = true)
    {
        let self = this;

        if (!isRemove) {

            if (isRelative) { 
                element.addClass('position-relative');
            }

            element.append($(`
                <div class="loading-box">
                    <div class="h-25 w-100 d-center"><p><i class="fas fa-cog fa-spin float-left mr-3"></i>${self.translations.loading}</p></div>
                </div>
            `));
        } else {
            element.find('.loading-box').remove();
        }
    }
}