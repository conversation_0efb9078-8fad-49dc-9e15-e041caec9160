
export class Attachments {

    constructor(element) {

        if (typeof element == 'undefined') {
            return;
        }

        if (typeof element == 'string') {
            this.element = $(element);
        } else {
            if (typeof element.element != 'undefined') {
                this.element = $(element.element);
            } else {
                return;
            }
        }

        this.hash = Math.floor(Math.random() * 10000);

        this.element.data('hash', this.hash);

        this.events();
    }

    events() {

        let self = this,
            selector = `[data-hash=${self.hash}]`;

        self.element.find('.btn-add-link').on('click', function() {


            let linkObject = $(this).closest('.link-objects').find('.template').clone();
            let html = $('<div>').append($(this).closest('.link-objects').find('.template').clone()).html();
            
            html = $(html);
            html.find('select').addClass('selectpicker-fix');
    
            $(this).closest('.link-objects').find('.link-objects-container').append(html);
            $(this).closest('.link-objects').find('.no-attachments').addClass('d-none')
            
            // $(this).closest('.link-objects').find('.link-objects-container .link-object .popins').each(function() {
            //     new Popin($(this));
            // });
    
            $(this).closest('.link-objects').find('.link-objects-container').find('.link-object').removeClass('template');

            self.updateObjects($(this).closest('.link-objects'));
        });

        self.element.find('.link-objects-container.sortable').sortable({
            stop: function(e, ui) {
                self.updateObjects($(this).closest('.link-objects'));
            }
        });

        self.element.find('.link-objects[data-placement]').each(function(){
            self.updateObjects($('.link-objects[data-placement='+$(this).data('placement')+']'));
        });

        $(document).on('click', '.remove-link-object', function() {
            var linkObjects = $(this).closest('.link-objects');
            if (linkObjects.find('.link-object').length > 1) {
                $(this).closest('.link-object').remove();
                self.updateObjects(linkObjects);
                if (linkObjects.find('.link-object').length < 2) {
                    linkObjects.find('.no-attachments').removeClass('d-none')
                }
            }
        });
        
        $(document).on('change', '.link-objects-container .link-object select', function() {
            self.updateObjects($(this).closest('.link-objects'));
        });
    }



    setLinkObjectSortingHandle(linksObject) 
    {
        var container = linksObject.find('.link-objects-container');
        container.find('.link-object .sorting-handle').addClass('d-none');
        if (container.find('.link-object').length > 1) {
            container.find('.link-object .sorting-handle').removeClass('d-none');
        }
    }
    
    setLinkObjectValues(linksObject) 
    {
    
        var links = [];
        var container = linksObject.find('.link-objects-container'); 
        container.find('.link-object select').each(function() {
            let val = $(this).selectpicker('val');
            if (val != '0') {
                links.push(val);
            }
        });
    
        linksObject.find('.link-object-values').val(links.join(','));
        linksObject.find('.link-object-values').prop('value', links.join(','));
    }
    
    correctMaxLinks(linksObject) 
    {
    
        var maxObjects = linksObject.data('max-items');
        var container = linksObject.find('.link-objects-container');
    
        linksObject.find('.btn-add-link').attr('disabled', false);
        if (container.find('.link-object').length >= maxObjects) {
            linksObject.find('.btn-add-link').attr('disabled', true);
        }
    
        if (container.find('.link-object').length > maxObjects) {
    
            var deleteCnt = container.find('.link-object').length - maxObjects;
            container.find('.link-object').each(function() {
                if (deleteCnt > 0) {
                    if ($(this).find('select').val() == 0) {
                        $(this).remove();
                        --deleteCnt;
                    }
                }
            });
    
            var i= 1;
            container.find('.link-object').each(function() {
                if (i > maxObjects) {
                    $(this).remove();
                }
                ++i;
            });
        }
    }
    
    updateLinkObjectValues(linksObject)
    {
        var values = [];
        var container = linksObject.find('.link-objects-container');
    
        container.find('.link-object select').each(function() {
            let val = $(this).selectpicker('val');
            if (val != '0') {
                values.push(val);
            }
        });
    
        container.find('.link-object select').each(function() {
    
            var selected = $(this).val();
            $(this).find("option").each(function() {
                $(this).removeAttr('disabled');
                $(this).prop('disabled', false);
                if (selected != $(this).val() && values.indexOf($(this).val()) != -1) {
                    $(this).attr('disabled', 'disabled');
                    $(this).prop('disabled', true);
                }
            });
    
            $(this).selectpicker('destroy');
            $(this).selectpicker('render');
    
        });
    }
    
    updateObjects(linksObject) 
    {
        this.correctMaxLinks(linksObject);
        this.setLinkObjectValues(linksObject);
        this.updateLinkObjectValues(linksObject);
        this.setLinkObjectSortingHandle(linksObject);
    }
}