export class Modal {

    constructor(oParams) {

        this.params = oParams;

        this.id = typeof this.params.id == 'undefined'
            ? this.getRandomId()
            : this.params.id;
        this.title = this.params.title;
        this.content = this.params.content;
        this.size = this.params.size ?? '';
        this.class = this.params.class ?? '';
        this.class_footer = this.params.class_footer ?? '';
        this.static = this.params.static ?? false;
        this.callBack = this.params.callBack ?? null;
        this.centered = (this.params.centered ?? true) ? ' modal-dialog-centered' : '';

        this.buttons = [];
        // this.buttons.push('<button type="button" class="btn btn-primary" data-bs-dismiss="modal">Cancel</button>');
        if (this.params.buttons) {
            for (var i = 0; i < this.params.buttons.length; ++i) {
                this.buttons.push(this.params.buttons[i]);
            }
        }

        if (this.content instanceof jQuery === false) {
            this.content = $(this.content);
        }

        this.html;

        this.create();

        this.eventRemove();
    }

    create() {

        this.html = $(`
            <div class="modal fade" id="${this.id}" role="dialog"${this.static ? ' data-bs-backdrop="static" data-bs-keyboard="false"' : ''}>
                <div class="modal-dialog ${this.class+' '+this.size}" role="document">
                    <div class="modal-content"> 

                        <div class="modal-header">
                            <h5 class="modal-title">${this.title}</h5>
                            <button type="button" class="close" data-bs-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>

                        <div class="modal-body">${getHTML(this.content)}</div>
                
                        ${this.buttons.length > 0 ? `<div class="modal-footer" class="${this.class_footer}">${this.buttons.join('')}</div>` : ``}
        
                    </div>
                </div>
            </div>
        `);


        $("body").append(this.html);

        $('body > .modal select:not(.no-selectpicker)')
            .selectpicker('destroy')
            .selectpicker('render');

        if (this.callBack) {
            let callBack = this.callBack;
            $('#' + this.id).on('shown.bs.modal', function () {
                callBack();
            });
        }

        new bootstrap.Modal($('#' + this.id)).show();

        $('#' + this.id).toggle();
    }

    eventRemove() {
        $('#' + this.id).on('hidden.bs.modal', function (e) {
            $('#' + this.id).remove();
        });
    }

    getRandomId() {
        return Math.random().toString(36).substring(2, 15);
    }

}