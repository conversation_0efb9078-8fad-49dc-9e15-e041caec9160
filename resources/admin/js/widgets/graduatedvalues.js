export class GraduatedValues {

    constructor(elementSelector, params)
    {
        this.element = $(elementSelector);

        this.params = params;

        this.hash = 'graduatedvalues-'+Math.floor(Math.random() * 1000000);

        this.element
            .data('hash', this.hash)
            .addClass(this.hash);

        this.data = {
            'steps': []
        };
        
        if (this.element.data('values')) {
            this.data.steps = this.element.data('values').value;
        }

        this.init();
        
        if (this.data.steps.length > 0) {
            this.load();
        }

        this.events();

        this.setData();

    }

    events()
    {
        let self = this,
            selector = '.'+this.hash;

        $(document).on('click', selector+' .container-steps .btn-row-add', function() {

            self.addRow($(this).closest('.step').index() + 1);
            self.setButtonAvailability();
            self.setData();
        });

        $(document).on('click', selector+' .container-steps .btn-row-delete', function() {

            let previousStep = false,
                nextStep = false,
                min = false,
                max = false;

            if ($(this).closest('.step').index() > 0) {
                previousStep = self.getStepByIndex($(this).closest('.step').index() - 1);
            }

            if ($(this).closest('.step').index() < $(this).closest('.container-steps').find('.step').length) {
                nextStep = self.getStepByIndex($(this).closest('.step').index() + 1);
            }

            if (previousStep === false || nextStep === false) {
                $(this).closest('.step').remove();
                self.setButtonAvailability();
                self.setData();
                return;
            }

            previousStep.data('maximal', $(this).closest('.step').data('maximal'));
            previousStep.find('.maximal').html($(this).closest('.step').data('maximal')+previousStep.find('.maximal').data('unit'));

            $(this).closest('.step').remove();

            self.setButtonAvailability();
            self.setData();
        });
        
        $(document).on('change', selector+' .container-steps .minimal input', function() {

            let min = -1, 
                max = 99999999999,
                previousStep = false,
                changed = false;

            //is there a step before 
            if ($(this).closest('.step').index() > 0) {
                min = parseInt(self.getStepByIndex($(this).closest('.step').index() - 1).data('minimal')) + 2;
                previousStep = self.getStepByIndex($(this).closest('.step').index() - 1);
            }

            if ($(this).closest('.step').data('maximal') != '-') {
                max = $(this).closest('.step').data('maximal');
            }

            if ($(this).val() >= max) {
                $(this).val(max - 1);
                $(this).closest('.step').data('minimal', max - 1);
                changed = true;
            }

            if ($(this).val() <= min) {
                $(this).val(min);
                $(this).closest('.step').data('minimal', min);
                changed = true;
            }
            
            if (!changed) {
                $(this).closest('.step').data('minimal', $(this).val());
            }

            if (previousStep !== false) {
                previousStep.data('maximal', parseInt($(this).val()) - 1);
                previousStep.find('.maximal').html((parseInt($(this).val()) - 1)+previousStep.find('.maximal').data('unit'));
            }

            self.setButtonAvailability();
            self.setData();
        });

        $(document).on('change', selector+' .container-steps input.value', function() {
            self.setData();
        });
    }

    init()
    {
        this.element.html(`
            <div class="row">
                <div class="col-1">&nbsp;</div>
                <div class="col-3 align-self-center font-weight-bold">Minimal</div> 
                <div class="col-2 align-self-center font-weight-bold">Maximal</div> 
                <div class="col-4 align-self-center font-weight-bold">${this.typeToText(this.params.data_type)}</div> 
                <div class="col-2">&nbsp;</div>
            </div>
            <div class="container-steps">
            
            </div>
            <input type="hidden" class="input-value" name="graduated_values_${this.params.element_id}" value="" />`
        );

        if (typeof this.data.steps == 'undefined' ||
            this.data.steps.length == 0) {
            this.addRow(0);
        }

        this.setButtonAvailability();
    }

    load()
    {
        let self = this;

        this.data.steps.forEach(function(step, index) {

            self.addRow(
                index, 
                step.minimal, 
                step.value
            );
        });

        this.setData();
    }

    addRow(position = 0, min = null, val = '')
    {
        let stepMinimalBefore = 0,
            isLast = true, 
            lastVal = '-',
            minimal = 1,
            maximal = '-',
            stepPrevious = false,
            stepAfter = false,
            valueHTML = ``;

      
        if (position < this.element.find('.container-steps .step').length) {
            stepAfter = this.getStepByIndex(position);
        }

        if (position > 0) {
            stepPrevious = this.getStepByIndex(position - 1);
        }

        if (stepPrevious !== false) {

            if (stepPrevious.data('maximal') != '-') {
                minimal = parseInt(stepPrevious.data('maximal')) + 1;
            } else {
                
                minimal = parseInt(stepPrevious.data('minimal')) + 2;
                //update previous maximal
                stepPrevious.data('maximal', parseInt(stepPrevious.data('minimal')) + 1);
                stepPrevious.find('.maximal').html((parseInt(stepPrevious.data('minimal')) + 1)+stepPrevious.find('.maximal').data('unit'));
            }
        }

        if (min !== null) {
            
            minimal = min;

            if (stepPrevious !== false) {
                stepPrevious.data('maximal', parseInt(minimal) - 1);
                stepPrevious.find('.maximal').html((parseInt(minimal) - 1) + stepPrevious.find('.maximal').data('unit'));
            }
        }

        if (stepAfter !== false) {
            maximal = parseInt(stepAfter.data('minimal')) - 1;
        }

        if (this.typeToText(this.params.data_type) != '') {

            let unit = this.unitTotext(this.params.data_unit);
            let pos = ['',''],
                inputClass = ''; 

            if (this.params.data_type == 'PRICE') {
                
                pos[0] = `<span class="input-group-text">&euro;</span>`;

                if (unit != '') {
                    pos[1] = `<span class="input-group-text">${unit}</span>`;
                }

                inputClass = ' price-normalize';
                
            } else {
                pos[1] = `<span class="input-group-text">${unit}</span>`;
            }

            valueHTML = `
                <div class="input-group input-group-sm"> 
                    ${pos[0]}
                    <input type="text" class="form-control value${inputClass}" value="${val}">
                    ${pos[1]}
                </div>`;
        } else {
            valueHTML = `<input type="text" class="form-control form-control-sm value${inputClass}" value="${val}" />`;
        }

        let stepUnit = `<input type="text" class="form-control form-control-sm" value="${minimal}" />`;
        if (this.stepUnitTotext(this.params.step_unit) != '') {
            stepUnit = `
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control" value="${minimal}" />
                    <span class="input-group-text">${this.stepUnitTotext(this.params.step_unit)}</span>
                </div>`;
        }

        let max = maximal+this.stepUnitTotext(this.params.step_unit);
        if (maximal == '-') {
            max = maximal;
        }

        let elm = $(`
            <div class="row step mb-2" data-minimal="${minimal}" data-maximal="${maximal}">
                <div class="col-1">
                    <button type="button" class="btn btn-sm btn-success btn-row-add btn-icon">
                        <div>
                            <i class="fas fa-plus"></i>
                        </div>
                    </button>
                </div>
                <div class="col-3 minimal align-self-center">${stepUnit}</div> 
                <div class="col-2 maximal align-self-center" data-unit="${this.stepUnitTotext(this.params.step_unit)}">${max}</div> 
                <div class="col-4 align-self-center">${valueHTML}</div> 
                <div class="col-2 text-right">
                    <button type="button" class="btn btn-sm btn-danger btn-row-delete btn-icon">
                        <div>
                            <i class="fas fa-times"></i>
                        </div>
                    </button>
                </div>
            </div>`
        );

        if (position == 0) {
            this.element.find('.container-steps').append(elm);
            return;
        }

        elm.insertAfter(this.element.find('.container-steps .step:eq('+(position-1)+')'));
    }

    getStepByIndex(position)
    {    
        return this.element.find('.container-steps .step:eq('+position+')');
    }

    setButtonAvailability()
    {
        let self = this;

        this.element.find('.container-steps .step').each(function() {

            $(this).find('.btn-row-delete')
                .attr('disabled', false)
                .prop('disabled', false);
            
            $(this).find('.btn-row-add')
                .attr('disabled', false)
                .prop('disabled', false);

            if ($(this).index() == self.element.find('.container-steps .step').length ||
                self.element.find('.container-steps .step').length == 1) {
                //last
                $(this).find('.btn-row-delete')
                    .attr('disabled', true)
                    .prop('disabled', true);
            }

            if ($(this).index() >= 0 && $(this).index() < self.element.find('.container-steps .step').length - 1) {
                // inbetween
                $(this).find('.btn-row-add')
                    .attr('disabled', true)
                    .prop('disabled', true);
            }
        });
    }

    typeToText(type)
    {
        return type == 'PRICE'
            ? 'Price'
            : 'Value';
    }
    

    stepUnitTotext(unit)
    {
        if (unit == 'MM1')
            return 'mm';

        if (unit == 'MM2')
            return 'mm<sup>2</sup>';

        return '';
    }

    unitTotext(unit)
    {
        if (unit == 'MM1')
            return 'mm';

        if (unit == 'MM2')
            return 'mm<sup>2</sup>';

        if (unit == 'M1')
            return 'Per strekkende meter';

        if (unit == 'M2')
            return 'Per vierkante meter';

        if (unit == 'PIECE')
            return 'Per stuk';
    }

    getData()
    {
        let data = [];
        this.element.find('.container-steps .step').each(function() {
            data.push({
                'minimal': $(this).find('.minimal input').val(),
                'value': $(this).find('input.value').val()
            });
        });

        this.data.steps = data;

        return data;
    }

    setData()
    {
        this.element.find('input.input-value').val(JSON.stringify(this.getData()));
    }

}