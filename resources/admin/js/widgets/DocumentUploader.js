export default class DocumentUploader {

    constructor() {

        this.xhr;

        this.type = 'single';

        this.modal;

        this.events();
    }

    events() {

        let self = this;

        $(document).on('dragover dragenter', '.modal .uploader .dropzone', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).addClass('dragover');
        });
    
        $(document).on('dragleave blur', '.modal .uploader .dropzone', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('dragover');
        });
    
        $(document).on('drop', '.modal .uploader .dropzone', function(e) {
    
            if (e.originalEvent.dataTransfer && 
                e.originalEvent.dataTransfer.files.length) {
    
                e.preventDefault();
                e.stopPropagation();
    
          
                self.upload(e.originalEvent.dataTransfer.files[0]);

                //upload(e.originalEvent.dataTransfer.files);
            }
        });

        $(document).on('change', '.modal .uploader input[type="file"]', function(e) {

            if (!e.target.files.length) {
                return;
            }

            self.upload(e.target.files[0]);
        });

        $(document).on('click', '.modal .uploader .btn-browse-file', function(e) {
            $(this).closest('.uploader').find('input[type="file"]').trigger('click');
        });

        $('.btn-document-upload').on('click', function() {

            let documentable = null;

            if ($(this).data('documentable-type') !== null) {
                documentable = {
                    type: $(this).data('documentable-type'),
                    id: $(this).data('documentable-id') ?? null,
                    description: $(this).data('documentable-description') ?? null
                };
            }

            self.createModal(
                $(this).data('type') ?? 'single',
                $(this).data('document-type') ?? null,
                documentable
            );
        }); 
    }

    createModal(
        type = 'single',
        documentType = null,
        documentable = null
    ) {

        if (typeof documentable === 'string') {
            documentable = documentable.split(':');
            documentable = {
                type: documentable[0] ?? null,
                id: documentable[1] ?? null,
                description: documentable[2] ?? null
            };
        }

        let self = this,
            modal = getTemplate('document-upload', [
                ['type', type],
                ['documentable', documentable],
                ['documentable_type', documentable !== null ? documentable.type : null],
                ['documentable_id', documentable !== null ? documentable.id : null]
            ])
        ;

        if (documentable === null) {
            modal.find('.row-wrapper[data-name="documentable"]').remove();
        }
        else {

            let description = documentable.description === null 
                ? ''
                : ': ' + documentable.description
            ;

            modal.find('.row-wrapper[data-name="documentable"] div.documentable').html(
                '<span>' + documentable.type + description + '</span>'
            );
        }

        if (type != 'single') {
            modal.find('.row-wrapper[data-name="document-type"]').remove();
        }
        else if (documentType !== null) {
            modal.find('select#document_type option[value="'+documentType+'"]').attr('selected', true);
        }

        this.modal = new Modal({
            "title": type == 'single'
                ? 'Upload document'
                : 'Upload documents',
            "class": '',
            "static": true,
            "centered": false,
            "content": modal,
            "buttons": [
                '<button type="button" class="btn btn-outline-primary" data-bs-dismiss="modal">Cancel</button>',
                `<button type="button" class="btn btn-primary btn-document-upload-confirm">Upload</button>`
            ],
            "callBack": function() {}
        });
    }   


    handleUploadProgress(e, dateStart) {

        let progress = Math.round((e.loaded / e.total) * 100),
            circle, circumference
        ;

        this.modal.html.find(".progress-box .percentage").html(progress+"%");
        this.modal.html.find(".progress-box .speed").html(((((e.loaded || e.position) * 8) / (((new Date()).getTime() - dateStart) / 1000)) / 1024 / 1024).toFixed(2) + ' Mbps');

        circle = this.modal.html.find('.progress-box .ring-circle')[0];
        circumference = circle.r.baseVal.value * 2 * 3.1415;
        circle.style.strokeDashoffset = circumference - progress / 100 * circumference;        
    }

    upload(file)
    {
        if (this.xhr) {
            this.xhr.abort();
        }

        let self = this,
            formData = new FormData(),
            dateStart = (new Date()).getTime()
        ;

        this.modal.html.find('.progress-filename').html(file.name);

        this.modal.html.find('.file-uploader').show();
        
        formData.append("file", file);
        formData.append(
            "classification", 
            this.modal.html.find('select#document_type').length == 1
                ? this.modal.html.find('select#document_type').selectpicker('val')
                : null
        );
        formData.append(
            "documentable_type", 
            this.modal.html.find('input[name=documentable_type]').val() ?? null
        );
        formData.append(
            "documentable_id", 
            this.modal.html.find('input[name=documentable_id]').val() ?? null
        );
        
        self.xhr = $.ajax({
            url: '/actions/document/upload',
            method: 'POST',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            xhr: function () {

                let xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener(
                    "progress",
                    function (e) {
                        if (e.lengthComputable) {
                            self.handleUploadProgress(e, dateStart);
                        }
                    },
                    false
                );
                // xhr.addEventListener("load", loadHandler, false);
                // xhr.addEventListener("error", errorHandler, false);
                // xhr.addEventListener("abort", abortHandler, false);

                return xhr;
            },
            success: function (response) {

                if (response.success) {
                    self.modal.close();
                    window.location.reload();
                }
                else {
                    self.modal.html.find('.file-uploader').hide();
                    self.modal.html.find('.alert').html(response.message).show();
                }
            },
        });
    }

}