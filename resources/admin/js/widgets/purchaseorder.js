import _ from "lodash";
import ItemUnitCalculator from "./ItemUnitCalculator";

export default class PurchaseOrder {

    constructor() {

        this.supplier = null;
        this.supplierId = null;

        this.item = null;
        this.itemVariant = null;
        this.itemInternal = null;

        this.items = [];

        this.xhr;

        this.mode = 'default';
   
        let converter = new ItemUnitCalculator({
            groups: [
                {
                    name: 'warehouse',
                    value: 1
                },
            ]
        });


        this.load();

        this.events();
    }

    events() {

        let self = this;

        $('select#company_id').on('changed.bs.select', function () {
            
            if ($(this).selectpicker('val') == '') {
                self.unsetSupplier();
                return;
            }

            self.selectSupplier($(this).selectpicker('val'));
        });

        $('.btn-item-add').on('click', function() {
            self.createItemModal();    
        });

        $(document).on('changed.bs.select', '.modal select#item_id', function() {
    
            let id = $(this).selectpicker('val');

            if (id == '' || 
                id == 'custom') {
                self.unsetItem();
                return;
            }

            self.getItem(
                id, 
                (fn) => self.setItem(fn),
                () => self.unsetItem(true)
            );
        });

        $(document).on('changed.bs.select', '.modal select#item_variant_id', function() {
            self.setItemVariant($(this).selectpicker('val'));
        });

        $(document).on('changed.bs.select', '.modal select#item_related_id', function(e, clickedIndex, isSelected) {
    
            //check if event is triggered by selectpicker
            if (!e.originalEvent) {
                return;
            }

            let id = $(this).selectpicker('val');

            if (id == '') {
                self.unsetItemInternal();
                return;
            }

            self.getItem(
                id, 
                (fn) => self.setItemInternal(fn),
                () => self.unsetItemInternal(true)
            );
        });

        
        
        // $(document).on('click', '.modal .btn-item-add-confirm', function() {
            
        //     self.verifyItem(
        //         self.getItemData(),
        //         function(responseData) {
        //             self.addItem(responseData);
        //             bootstrap.Modal.getInstance($('body > .modal')).hide();
        //         }
        //     );
            
        // });

        // $(document).on('click', '.items-container .row-item .btn-item-edit', function() {
        //     self.editItem($(this).closest('.row-item').data('uniq-id'));
        // });

        // $(document).on('click', '.modal .btn-item-delete-confirm', function() {
        //     self.deleteItem($(this).data('uniq-id'));
        //     bootstrap.Modal.getInstance($('body > .modal')).hide();
        // });

        // $(document).on('click', '.items-container .row-item .btn-item-delete', function() {

        //     new Modal({
        //         "title": 'Delete item',
        //         "centered": true,
        //         "content": `
        //             <p>Are you sure to delete this item?</p>
        //         `,
        //         "buttons": [
        //             '<button type="button" class="btn btn-outline-primary" data-bs-dismiss="modal">Cancel</button>',
        //             `<button type="button" class="btn btn-danger btn-item-delete-confirm" data-uniq-id="${$(this).closest('.row-item').data('uniq-id')}">Delete</button>`
        //         ]
        //     });

        // });

        // $(document).on('changed.bs.select', '.modal select#unittype_sales', function() {

        //     if ($(this).selectpicker('val') == '') {
        //         $('input#quantity_sales').set('disabled', true);
        //         $('input#quantity_sales').val('');
        //         $('input#pricing_unittype_sales').val('');
        //         self.calculatePricing('pricing_unittype_warehouse');
        //     } else {
        //         $('input#quantity_sales').set('disabled', false);
        //     }
        // });

        // $(document).on('change keyup blur', [
        //     '#pricing_unittype_sales',
        //     '#pricing_unittype_warehouse',
        //     '#pricing_total',
        //     '#quantity_warehouse',
        //     '#quantity_sales'
        // ].join(', '), function(e) {
        //     if (!e.which) return; 
        //     self.calculatePricing(e.currentTarget.id);
        // });
    
        // $(document).on('changed.bs.select', '.modal select#unittype_sales', function() {
        //     self.calculatePricing();
        // });
    
        // $(document).on('changed.bs.select', '.modal select#unittype_warehouse', function() {
        //     self.calculatePricing();
        // });
    
        
    }

    load() 
    {
        let self = this;

        this.mode = 'loading';

        //get & set supplier
        if ($('select#company_id').selectpicker('val') != '') {
            self.selectSupplier($('select#company_id').selectpicker('val'));
        }

        //load & add items
        // if ($('form #items').val() != '') {
            
        //     JSON.parse($('form #items').val()).forEach(function(item) {

        //         if (item.code === undefined) {
        //             item['code'] = '';

        //             if (item.item !== undefined) {
        //                 item['code'] = item.item.code;
        //             }
        //         }

        //         item.uniq_id ??= uniqueId();

        //         self.addItem(item);
        //     });
        // }

        // //load related items (warehouse items)
        // if ($('form #related_items').val() != '') {
        //     JSON.parse($('form #related_items').val()).forEach(function(item) {
        //         self.relatedItems.push(item);
        //     });
        // }

        this.mode = 'default';
    }

    selectSupplier(supplierId)
    {
        let self = this;

        self.unsetSupplier();

        _data.set('suppliers')
            .url('/actions/companies/supplier')
            .where('id', '=', parseInt(supplierId))
            .getOrFetchFirst()
            .done(function(supplier) {
                self.setSupplier(supplier);      
            })
            .emptyOrCatch(function() {
                self.unsetSupplier(true);
            })
        ;
    }

    setSupplier(supplier)
    {
        let self = this,
            hasAddress = null,
            mail, phone
        ;

        console.log('setSupplier', supplier);

        if (supplier === null){
            self.unsetSupplier(true);
            return;
        }

        this.supplierId = supplier.id;
        this.supplier = supplier;

        ['invoice', 'delivery'].forEach(function(addressType) {

            if (hasAddress !== null) return;
            
            supplier.addresses.forEach(function(address) {

                if (address.is_default &&
                    address.type == addressType) {
                    $('.panel-supplier address').html(address.label);
                    hasAddress = address;
                }
            });
        });
    
        mail = 
            hasAddress.email ?? 
            supplier.email ?? 
            supplier.email_financial ?? 
            undefined
        ;
  
        phone = 
            hasAddress.phone ?? 
            supplier.phone ?? 
            undefined
        ;
        
        $('.panel-supplier .email').hide();
        if (mail !== undefined) {
            $('.panel-supplier .email .value').html(mail);
            $('.panel-supplier .email').show();
        }

        $('.panel-supplier .phone').hide();
        if (phone !== undefined) {
            $('.panel-supplier .phone .value').html(phone);
            $('.panel-supplier .phone').show();
        }
    }

    unsetSupplier(resetSelect = false)
    {
        this.supplier = {};
        this.supplierId = null;
        this.supplierItem = null;

        if (resetSelect === false) {
            return;
        }
        
        $('select.company_id').selectpicker('val', '');
    }


    getSupplierById(supplierId = null)
    {
        supplierId = supplierId === null 
            ? this.supplierId
            : supplierId
        ;

        let supplier = _data
            .set('suppliers')
            .where('id', '=', supplierId)
            .getFirst()
        ;

        if (supplier === null) return false;

        return supplier;
    }

    getSupplierItemById(itemId, supplierId = null)
    {
        let supplier,
            supplierItem = false
        ;

        if ((supplier = this.getSupplierById(supplierId)) === false) {
            return false;
        }

        return _.find(supplier.items ?? [], { 
            id: parseInt(itemId) 
        });
    }


    getItem(
        itemId, 
        callBack = null, 
        failCallBack = null
    ) {
        let self = this;

        console.log('getItem', itemId, callBack, failCallBack);

        _data.set('items')
            .url('/actions/companies/item')
            .where('id', '=', parseInt(itemId))
            .getOrFetchFirst()
            .done(function(data) {
                callBack(data);    
            })
            .emptyOrCatch(function() {
                failCallBack();
            })
        ;
    }

    unsetItem(resetSelect = false)
    {
        console.log('unsetItem', resetSelect);

        this.item = null;

        $('.modal select#item_variant_id')
            .selectpicker('val', '')
            .closest('.col')
            .hide()
        ;

        if (resetSelect === false) {
            return;
        }

        $('.modal select#item_id').selectpicker('val', '');
    }

    setItem(item)
    {
        console.log('setItem', item);

        let self = this,
            variantOptions = [],
            setSelected = false
        ;

        this.item = item;
      
        variantOptions.push(`<option value="">Nothing selected</option>`);
        variantOptions.push(`<option value="custom">Free entry</option>`);

        if ((item.variants ?? []).length > 0) {

            setSelected = item.variants.length == 1;

            item.variants.forEach(function(variant) {
                variantOptions.push(`<option value="${variant.id}"${setSelected ? ' selected' : ''}>${variant.systemname}</option>`);
            });
        }

        $('.modal select#item_variant_id')
            .selectpicker('destroy')
            .removeClass('disabled')
            .html(variantOptions.join(''))
        ;

        if (setSelected) {
            $('.modal select#item_variant_id')
                .addClass('disabled')
                .selectpicker('render')
                .closest('.col')
                .hide()
            ;

            this.setItemVariant(item.variants[0].id);

        } else {
            $('.modal select#item_variant_id')
                .selectpicker('render')
                .closest('.col')
                .fadeIn()
            ;
        }
        
        this.getItem(
            item.item_related_id,
            (fn) => self.setItemInternal(fn, true),
            () => self.unsetItemInternal(true)
        );
    }

    setItemVariant(itemVariantId)
    {
        console.log('setItemVariant', itemVariantId);

        this.itemVariant = _.find(this.item?.variants, { id: parseInt(itemVariantId) }) ?? null;

        if (this.itemVariant === null) {
            console.log('itemVariant is null', itemVariantId);
            this.unsetItemVariant(itemVariantId == 'custom' || itemVariantId == ''
                ? false
                : true
            );
        }

        this.setItemData();
    } 

    unsetItemVariant(resetSelect = false)
    {
        console.log('unsetItemVariant', resetSelect);
        this.itemVariant = null;

        if (resetSelect === false) return;

        $('.modal select#item_variant_id').selectpicker('val', '');
    }

    setItemDataVisibility()
    {
        console.log('setItemDataVisibility');

        let itemId = $('.modal select#item_id').selectpicker('val'),
            itemVariantId = $('.modal select#item_variant_id').selectpicker('val'),
            itemRelatedId = $('.modal select#item_related_id').selectpicker('val'),
            b = true
        ;

        if (itemId === '' || 
            itemRelatedId === '' || (
            ['', 'custom'].includes(itemId) === false && itemVariantId === ''
        )) {
            b = false;
        }

        console.log(
            'visibility', 
            'itemId = '+itemId, 
            'itemRelatedId = '+itemRelatedId, 
            'itemVariantId = '+itemVariantId, 
            (['', 'custom'].includes(itemId) === false && itemVariantId === ''), 
            b);

        $('.modal .item-wrapper')[b ? 'slideDown' : 'slideUp']();
        
        return;
    }

    setItemData()
    {
        console.log('setItemData');

        let self = this,
            itemData = self.item ?? self.itemInternal ?? {}
        ;

        this.setItemDataVisibility();

        //get itemdata.units where unit_group_id = 1 using lodash
        let unitWarehouse = _.find(itemData.units ?? [], { unit_group_id: 1 }) ?? null;
        let unitSales = _.find(itemData.units ?? [], { unit_group_id: 2 }) ?? null;
        let unitPricing = _.find(itemData.units ?? [], { unit_group_id: 3 }) ?? null;
        let unitWeight = _.find(itemData.units ?? [], { unit_group_id: 4 }) ?? null;

        $('.modal .row-unit-group[data-id=1] select.unit-select')
            .selectpicker('destroy')
            .addClass('disabled')
            .selectpicker('render')
            .selectpicker('val', String(unitWarehouse?.unit_group_unit_id))
        ;

        //get relations
        let relations = (_.find((this.itemVariant ?? itemData).units ?? [], { unit_group_id: 1 }) ?? {}).relations ?? [];
        
        let relatableIds = _.map(unitWarehouse.relations, 'relatable_id');

        let relationProperties = unitWarehouse.unit_group_unit.unit.compositionals.map(function(compositional) {

            let property = _data.set('properties')
                .where('system_context_unit_id', '=', compositional.unit_id)
                .where('system_context_unit_compositional_id', '=', compositional.id)
                .getFirst()
            ;

            if (property === null) return;

            let relation = _.find(property.values, v => relatableIds.includes(v.id)) ?? null;

            if (relation !== null) {
                property.relation_is_null = true;
            }

            property.relation = _.find(relations, function(relation) {
                return _.map(property.values, 'id').includes(relation.relatable_id);
            }) ?? null;

            return property;

        }).filter(v => v !== undefined);

        //get current relation elements values
        let relationsValues = $('.modal .row-unit-group[data-id=1] .relations select').map(function() {
            return {
                id: parseInt($(this).closest('.row-relation').find('input.unit_compositional_id').val()),
                value: $(this).selectpicker('val')
            };
        }).get();

        console.log('relationProperties', relationProperties);

        //create the relation elements
        $('.modal .row-unit-group[data-id=1] .relations').empty().append(
            relationProperties.map(function(relationProperty) {

                let options = relationProperty.values.map(function(value) {
                    return getTemplate('item_unit_relation_option', {
                        'value': 'PropertyValue::'+value.id,
                        'title': value.systemlabel,
                    }, false);
                });

                let selected = _.find(relationsValues, { id: relationProperty.system_context_unit_compositional_id })?.value ?? null;

                if (relationProperty.relation) {
                    selected = relationProperty.relation.relatable_type + '::' + relationProperty.relation.relatable_id;
                }

                let relationElement = getTemplate('item_unit_relation', {
                    'id': uniqueId(),
                    'unit_group_id': 1,
                    'unit_compositional_id': relationProperty.system_context_unit_compositional_id,
                    'systemlabel': relationProperty.systemlabel,
                    'item_unit_relation_options': options.join(''),
                    'model': selected ?? ''
                });

                if (relationProperty.relation_is_null) {
                    relationElement.closest('.row-relation').hide();
                }

                if (selected) {
                    relationElement.find('select')
                        .addClass('disabled')
                        .set('tabindex', '-1')
                        .find('option[value="'+selected+'"]')
                        .set('selected', true)
                    ;
                }

                relationElement.find('select')
                    .selectpicker('destroy')
                    .selectpicker('render')
                ;

                return relationElement;
            })
        );

        //set pricing labels
        $('.modal input.pricing_unit_warehouse').closest('.row').find('label').html(`Price per ${unitWarehouse?.unit_group_unit?.unit?.value ?? null}`);
        $('.modal input.pricing_unit_sales').closest('.row').find('label').html(`Price per ${unitSales?.unit_group_unit?.unit?.value ?? null}`);
        $('.modal input.pricing_unit_pricing').closest('.row').find('label').html(`Price per ${unitPricing?.unit_group_unit?.unit?.value ?? null}`);
        $('.modal input.pricing_unit_pricing').val(convertFromStorageToMoney(unitPricing?.value ?? '', 2));


        //get active variant data
        let variant = this.itemVariant ?? itemData;


        let variantRelations = _.find((this.itemVariant ?? itemData).units ?? null, { unit_group_id: 1 })?.relations;

        console.log(self.item, self.itemInternal, this.itemVariant, variantRelations);

        //get warehouse unit data
        let warehouseUnitGroupUnitId = $('.modal select.unit-select').selectpicker('val');


        let unitGroupWarehouseData = _data.set('unitGroups')
            .where('value', '=', 'warehouse')
            .getFirst()
        ;

        let warehouseUnit = _.find(unitGroupWarehouseData.units ?? [], { id: parseInt(warehouseUnitGroupUnitId) })?.unit ?? null;

      

        console.log(warehouseUnitGroupUnitId, unitGroupWarehouseData, warehouseUnit, warehouseUnitData);

        //calculate all prices and amounts
        let itemUnitConverter = new ItemUnitCalculator({
            warehouse: {
                id: warehouseUnit?.id,
                quantity: $('.modal input#units_1_value').val(),
                price: $('.modal input#pricing_unit_warehouse').val(),
                relations: [
                    {
                        type: 'PropertyValue',
                        attribute: 'dimensional.width',
                        value: 320, //total amount in cm1
                        unit_id: 56 //cm1
                    },
                    {
                        type: 'PropertyValue',
                        attribute: 'dimensional.length',
                        value: 75,  //total amount in m1
                        unit_id: 53,//m1
                    },
                    {
                        type: 'PropertyValue',
                        attribute: 'dimensional.height',
                        value: 1,   //total amount in mm1
                        unit_id: 61,//mm1
                    },
                ]
            }
        });
    }

    setItemInternal(item, setSelect = false)
    {
        this.itemInternal = item;

        console.log('setItemInternal', item, setSelect);

        if (setSelect === false) {
            this.setItemData();
            return;
        }

        $('.modal select#item_related_id')
            .selectpicker('destroy')
            .addClass('disabled')
           // .set('disabled', true)
            .find('option[value="'+(item.id ?? '')+'"]')
            .set('selected', true)
            .parent()
            .selectpicker('render')
        ;    

        this.setItemData();
    }

    unsetItemInternal(resetSelect = false)
    {
        this.itemRelated = null;
        console.log('unsetItemInternal', resetSelect);
        $('.modal .item-wrapper').slideUp();

        if (resetSelect === false) {
            return;
        }

        $('.modal select#item_related_id')
            .selectpicker('destroy')
            .removeClass('disabled')
            //.set('disabled', false)
            .find('option:selected')
            .set('selected', false)
            .parent()
            .selectpicker('render')
    }



    // getRelatedItemById(relatedItemId)
    // {
    //     let relatedItem = false;

    //     this.relatedItems.forEach(function(item) {
    //         if (parseInt(item.id) == parseInt(relatedItemId)) {
    //             relatedItem = item;
    //             return;
    //         }
    //     });

    //     return relatedItem;
    // }

   
    selectItem(item = null)
    {
        this.supplierItem = item;

        return;

        item ??= {};

        let relatedItemId = $('.modal select#item_related_id').selectpicker('val');

      //  let relatedItem = this.getRelatedItemById(relatedItemId) ?? {};

        let relatedItem = _data.set('relatedItems')
            .url('/actions/companies/related_item')
            .where('id', '=', parseInt(relatedItemId))
            .getOrFetchFirst(false)
        ;

        console.log('relatedItem', relatedItem);

        if (relatedItem === null) {
            $('.modal select#item_related_id')
                .selectpicker('val', '')[0]
                .dispatchEvent(new Event('change', { 
                    bubbles: true 
                }))
            ;
            return;
        }

        console.log('relatedItem', relatedItem);

        return;

        console.log('item', item);
        console.log('relatedItem', relatedItem);

        // $('.modal input#code').val(item.code ?? '');
        // $('.modal input#name').val(item.name ?? '');

        const unitWarehouseUnit = _.filter(item.units, { unit_group_id: 1 })[0] ?? null;

        let unitProperties = _data.set('properties')
            .where('system_context_unit_id', '=', unitWarehouseUnit.unit_group_unit_id)
            .get()
        ;

        let relatedItemWarehouseUnit = _.filter(relatedItem.units, { 
            unit_group_unit_id: unitWarehouseUnit.unit_group_unit_id 
        })[0] ?? null;

        let unitPropertiesFiltered = _.filter(unitProperties, function(property) {
            return _.filter(property.values, function(value) {
                return _.filter(relatedItemWarehouseUnit.relations, function(relation) {
                    return relation.relatable_id == value.id;
                }).length > 0;
            }).length == 0;
        });

        if (unitPropertiesFiltered.length > 0) {

            let variants = _.map(item.variants, function(variant) {

                if (variant.is_active == 0) return null;

                let variantWarehouseUnit = _.filter(variant.units, { 
                    unit_group_unit_id: unitWarehouseUnit.unit_group_unit_id 
                })[0] ?? null;

                let relations = _.filter(variantWarehouseUnit.relations, function(relation) {
                    return _.filter(unitPropertiesFiltered, function(property) {
                        return _.filter(property.values, function(value) {
                            return relation.relatable_id == value.id;
                        }).length > 0;
                    }).length > 0
                });

                if (relations.length == 0) return null;

                variant.unit_relatables = relations;

                return variant;

            });

            let variantPropertyValues = _.flatten(_.map(variants, function(variant) {
                return _.map(variant.unit_relatables, function(relation) {
                    return relation.relatable_id;
                })
            }));

            console.log('dsad', unitPropertiesFiltered, variantPropertyValues);

            $('.modal .unit-warehouse-compositionals').empty().html(
                unitPropertiesFiltered.map(function(property) {

                    let row = getTemplate('compositional-row', {
                        id: property.id,
                        label: property.systemname
                    });
    
                    row.find('select').append(
                        property.values.map(function(value) {
                            if (variantPropertyValues.indexOf(value.id) === -1) return;
                            return `<option value="${value.id}">${value.systemname}</option>`;
                        }
                    ));    

                    return getHTML(row);
    
                }).join('')
            );

            $('.modal .unit-warehouse-compositionals select').selectpicker();
        }



       

        if (unitWarehouseUnit) {        
            $('.modal .unit-warehouse-select').hide();
            $('.modal .unit-warehouse-text')
                .html(unitWarehouseUnit.group_unit.systemname)
                .show()
            ;
        } else {
            $('.modal .unit-warehouse-select').show();
            $('.modal .unit-warehouse-text').hide();
        }



        $('.modal select#unittype_warehouse').selectpicker('val', item.unit_type_warehouse ?? relatedItem.unit_type_warehouse ?? '');
        $('.modal select#unittype_sales').selectpicker('val', item.unit_type_sales ?? relatedItem.unit_type_sales ?? '');

        this.calculatePricing();

        //setQuantityUnitType();
    }




    createItemModal(itemData = null)
    {
        let self = this,
            mode = itemData === null ? 'create' : 'edit',
            modal,
            supplier, 
            optionGroups,
            options,
            title,
            selected
        ;

        if ((supplier = self.getSupplierById()) === false) {
            return;
        }

        itemData = itemData === null 
            ? {} 
            : itemData
        ;

        console.log('createItemModal', itemData);

        modal = getTemplate('item-create', [
            ['uniq_id', itemData.uniq_id ?? ''],
            ['id', itemData.id ?? ''],
            ['code', itemData.code ?? ''],
            ['name', itemData.name ?? ''],
            ['warehouse_quantity', itemData.quantity_warehouse ?? ''],
            ['sales_quantity', itemData.quantity_sales ?? ''],
            ['pricing_sales', typeof itemData.price_unit_type_sales_excl == 'undefined'
                ? ''
                : formatPrice(itemData.price_unit_type_sales_excl, 2)],
            ['pricing_warehouse', typeof itemData.price_unit_type_warehouse_excl == 'undefined'
                ? ''
                : formatPrice(itemData.price_unit_type_warehouse_excl, 2)],
            ['pricing_total', typeof itemData.price_total == 'undefined'
                ? ''
                : formatPrice(itemData.price_total, 2)]         
        ]);

        // modal.find('select#related_item_id option[value="'+((itemData.item ?? {}).related_item_id ?? '')+'"]').set('selected', true);
        // modal.find('select#unittype_warehouse option[value="'+(itemData.unit_type_warehouse ?? '')+'"]').set('selected', true);
        // modal.find('select#unittype_sales option[value="'+(itemData.unit_type_sales ?? '')+'"]').set('selected', true);
    
        options = [ 
            `<option value="">Nothing selected</option>`, 
            `<option value="custom"${(itemData['item_id'] ?? -1) == 'custom' ? ' selected' : ''}>Free entry</option>`
        ];

       // optionGroups = groupBy(supplier.items, v=> v.unit_type_warehouse);
        optionGroups = _.groupBy(supplier.items, function(item) {
            return item.type; 
        });

        Object.keys(optionGroups).forEach(function(group) {

            options.push(`<optgroup label="${group}">`);

            optionGroups[group].forEach(function(item) {

                title = [];
                if (item.code && item.code != '') {
                    title.push(item.code);
                }
                if (item.name && item.name != '') {
                    title.push(item.name);
                }

                selected = (itemData['item_id'] ?? -1) == item.id 
                    ? ' selected'
                    : ''
                ;

                options.push(`<option value="${item.id}" ${selected}>${title.join(' - ')}</option>`);
            });

            options.push(`</optgroup>`);
        });
        
        modal.find('select#item_variant_id').closest('.col').hide();
        modal.find('select#item_id').html(options.join(''));

        // if ((itemData['item_id'] ?? -1) === 'custom') {
        //     modal.find('.row-wrapper[data-column="related_item_id"]').switchClass('collapsed', 'show');
        // }
    
        new Modal({
            "title": 'Add item',
            "class": 'modal-xl',
            "static": true,
            "centered": false,
            "content": modal,
            "buttons": [
                '<button type="button" class="btn btn-outline-primary" data-bs-dismiss="modal">Cancel</button>',
                `<button type="button" class="btn btn-primary btn-item-add-confirm">${mode == 'new' ? 'Add' : 'Save'}</button>`
            ],
            "callBack": function() {
               
                // if (mode == 'edit') {
                //     self.canCalculatePricing();
                // }
                // $('.modal select#unittype_sales').trigger('change');
                // $('.modal select#unittype_warehouse').trigger('change');

                $('.modal select:not(.no-selectpicker)').selectpicker();
                
                //self.canCalculatePricing();
            }
        });
    }

    

    

    // getItemData()
    // {
    //     let pricingSales = normalizePrice($('.modal #pricing_unittype_sales').val()),
    //         pricingWareHouse = normalizePrice($('.modal #pricing_unittype_warehouse').val()),
    //         pricingTotal = normalizePrice($('.modal #pricing_total').val()),
    //         itemId = $('.modal select#item_id').selectpicker('val'),
    //         relatedItemId = $('.modal select#related_item_id').selectpicker('val'),
    //         relatedItem = this.getRelatedItemById(relatedItemId) ?? null
    //     ;

    //     pricingSales = isNaN(pricingSales) ? '' : pricingSales;
    //     pricingWareHouse = isNaN(pricingWareHouse) ? '' : pricingWareHouse;
    //     pricingTotal = isNaN(pricingTotal) ? '' : pricingTotal;

    //     return {
    //         id: $('.modal input[name=id]').val() == '' ? null : $('.modal input[name=id]').val(),
    //         uniq_id: typeof $('.modal .item-create').data('uniq-id') === 'undefined'
    //             ? null
    //             : $('.modal .item-create').data('uniq-id'),
    //         item_id: itemId = $('.modal select#item_id').selectpicker('val'),
    //         related_item_id: relatedItemId == null
    //             ? null
    //             : parseInt(relatedItemId),
    //         related_item: relatedItem,
    //         code: $('.modal input#code').val(),
    //         name: $('.modal input#name').val(),

    //         quantity_warehouse: $('.modal input#quantity_warehouse').val(),
    //         unit_type_warehouse: $('.modal select#unittype_warehouse').selectpicker('val'),
    //         price_unit_type_warehouse_excl: pricingWareHouse,

    //         quantity_sales: $('.modal input#quantity_sales').val(),
    //         unit_type_sales: $('.modal select#unittype_sales').selectpicker('val'),
    //         price_unit_type_sales_excl: pricingSales,
            
    //         price_total: pricingTotal
    //     };
    // }

    

    // addItem(data)
    // {
    //     let unit = `${data.unit_type_warehouse}`,
    //         html, idx
    //     ;

    //     if (data.unit_type_sales !== null) {
    //         unit = `${unit} (${data.quantity_sales} ${data.unit_type_sales})`;
    //     }

    //     if (data.price_total === undefined &&
    //         (data.price_unit_type_warehouse_excl ?? null) !== null) {
    //         data.price_total = parseFloat(data.price_unit_type_warehouse_excl) * parseFloat(data.quantity_warehouse);
    //     }

    //     html = getTemplate('item-row', [
    //         ['uniq_id', data.uniq_id ?? null],
    //         ['code', data.code ?? '-'],
    //         ['name', data.name ?? '-'],
    //         ['unit', unit],
    //         ['quantity', data.quantity_warehouse ?? '-'],
    //         ['price_unit', (data.price_unit_type_warehouse_excl ?? null) === null
    //             ? '-'
    //             : getLocalePrice(data.price_unit_type_warehouse_excl)
    //         ],
    //         ['price_total', (data.price_total ?? null) === null
    //             ? '-'
    //             : getLocalePrice(data.price_total)
    //         ],
    //     ]);
    
    //     if ((idx = this.getItemIndexByKeyValue('uniq_id', data.uniq_id)) === false) {

    //         console.log('items new');
    //         this.items.push(data);
    //         $('.items-container').append(html);
    //     } else {

    //         console.log('items edit');
    //         this.items[idx] = data;
    //         $('.items-container .row-item[data-uniq-id="'+data.uniq_id+'"]').replaceWith(html);
    //     }

    //     this.setTotals();

    //    // if (this.mode == 'default') {
    //         this.setData();
    //    // }
    // }

    // editItem(itemUniqId)
    // {
    //     let self = this,
    //         item;

    //     if ((item = self.getItemByKeyValue('uniq_id', itemUniqId)) === false) {
    //         return false;
    //     }

    //     self.createItemModal(item);
    // }

    // verifyItem(itemData, callBack = null)
    // {
    //     if (self.xhr) self.xhr.abort();
    
    //     $('.modal .message-container').slideUp(function() {
    //         $(this).html(``);
    //     });

    //     self.xhr = $.ajax({
    //         url: '/actions/purchaseorder/add_item',
    //         type: 'POST',
    //         dataType: "json",
    //         data: itemData,
    //         success: function(response) {
    //             if (typeof callBack === 'function') {
    //                 callBack(response.data);
    //             }                
    //         },
    //         error: function (request) {

    //             let messages = [];
    //             if (request.responseJSON &&
    //                 request.responseJSON.data &&
    //                 request.responseJSON.data.errors &&
    //                 Object.keys(request.responseJSON.data.errors).length > 0
    //             ) {
    //                 Object.keys(request.responseJSON.data.errors).forEach(function(k) {
    //                     messages.push(request.responseJSON.data.errors[k]);
    //                 })
    //             }

    //             $('.modal .message-container').html(`
    //                 <div class="alert alert-danger">
    //                     ${messages.join("<br />")}
    //                 </div>
    //             `);

    //             $('.modal .message-container').slideDown();
    //         }
    //     });
    // }

    // deleteItem(uniqId)
    // {
    //     let idx = this.getItemIndexByKeyValue('uniq_id', uniqId);

    //     if (idx === false) return false;

    //     this.items.splice(idx, 1);

    //     this.setData();

    //     $('.items-container .row-item[data-uniq-id="'+uniqId+'"]').slideUp(function() {
    //         $(this).remove();
    //     });
    // }

    

    // setTotals()
    // {
    //     let self = this,
    //         html,
    //         totalItems = 0,
    //         totalShipping = 0,
    //         totalExcl = 0,
    //         totalTax = 0,
    //         totalIncl = 0,
    //         hasPricing = true
    //     ;

    //     this.items.forEach(function(item) {

    //         if (item.price_total === null) {
    //             hasPricing = false;
    //         }
            
    //         totalItems += item.price_total === null ? 0 : parseFloat(item.price_total);
    //     });

    //     totalShipping = 0; //TODO: shippingcosts module

    //     totalExcl = totalItems + totalShipping;
    //     totalTax = parseFloat((totalExcl * 0.21).toFixed(2));
    //     totalIncl = totalExcl + totalTax;
        
    //     html = getTemplate('item-totals', [
    //         ['total_excl', getLocalePrice(totalExcl, 2)],
    //         ['total_shipping', getLocalePrice(totalShipping, 2)],
    //         ['total_tax', getLocalePrice(totalTax, 2)],
    //         ['total_incl', getLocalePrice(totalIncl, 2)],
    //         ['tax_rate', '21%'],
    //     ]);
     
    //     html.find('.row-item div:eq(1)')[!hasPricing ? 'addClass' : 'removeClass']('text-danger');

    //     $('.overview-footer').html(html);
    // }

    // canCalculatePricing()
    // {

    //     if ($('.modal select#item_id').selectpicker('val') == '') {
    //         $('body > .modal .item-wrapper').slideUp();
    //         return;
    //     }

    //     $('body > .modal .item-wrapper').slideDown();

    //     let warehouseQty = $('.modal input#quantity_warehouse').val(),
    //         warehouseUnitType = $('.modal select#unittype_warehouse').selectpicker('val'),
    //         salesQty = $('.modal input#quantity_sales').val(),
    //         salesUnitType = $('.modal select#unittype_sales').selectpicker('val'),
    //         valid = true,
    //         hasSales = false;
    //     ;

    //     if (this.supplierId === null ||
    //         warehouseQty == '' ||
    //         warehouseUnitType == '' ||
    //         normalizePrice(warehouseQty) <= 0 ||
    //         (salesQty != '' && salesUnitType == '') ||
    //         (salesQty == '' && salesUnitType != '') ||
    //         (salesQty != '' && normalizePrice(salesQty) <= 0)         
    //     ) {
    //         valid = false;
    //     }

    //     if (salesQty != '' && 
    //         salesUnitType != '' && 
    //         normalizePrice(salesQty) > 0) {
    //         hasSales = true;
    //     }

    //     $('.modal .row-wrapper.pricing-sales')[hasSales ? 'slideDown' : 'slideUp']();
    //     $('.modal #pricing_unittype_sales').set('disabled', !hasSales || !valid ? true : false);
    //     $('.modal #pricing_unittype_warehouse').set('disabled', valid ? false : true);
    //     $('.modal #pricing_total').set('disabled', valid ? false : true);

    //     return valid;
    // }


    // getRelatedItemPropertyBySystemUsage(relatedItem, systemUsage)
    // {
    //     //check data
    //     if (relatedItem === undefined ||
    //         relatedItem.property_values === undefined
    //     ) {
    //         return false;
    //     }

    //     let item = false;

    //     relatedItem.property_values.forEach(function(propertyValue) {

    //         if (propertyValue.property !== undefined &&
    //             propertyValue.property.system_usage !== undefined &&
    //             propertyValue.property.system_usage == systemUsage) {
    //             item = propertyValue;
    //             return;
    //         }

    //     });

    //     return item;
    // }

    // calculatePricing(inputElement = null)
    // {




    //     console.log(
    //         this.getItemData(),
    //         $('.modal select#related_item_id').selectpicker('val'),
    //         this.supplierId,
    //         this.supplierItem,
    //         this.items
    //     );

    //     if (!this.canCalculatePricing()) return;

    //     inputElement = [
    //         'quantity_warehouse', 
    //         'quantity_sales'
    //     ].indexOf(inputElement) == -1
    //         ? inputElement
    //         : null
    //     ;

    //     inputElement = inputElement == 'pricing_unittype_sales' && $('.modal input#pricing_unittype_sales').is(':disabled')
    //         ? 'pricing_unittype_warehouse'
    //         : inputElement
    //     ;

    //     let warehouseQty = $('.modal input#quantity_warehouse').val(),
    //         warehouseUnitType = $('.modal select#unittype_warehouse').selectpicker('val'),
    //         salesQty = $('.modal input#quantity_sales').val(),
    //         salesUnitType = $('.modal select#unittype_sales').selectpicker('val'),
    //         pricing,
    //         hasElement = inputElement !== null,
    //         itemData = this.getItemData()
    //     ;

    //     if (!hasElement) {
    //         [
    //             'pricing_unittype_sales',
    //             'pricing_unittype_warehouse',
    //             'pricing_total'
    //         ].forEach(function(element) {
                
    //             if (hasElement) return;
            
    //             if (!isNaN(pricing = normalizePrice($('.modal input#'+element).val()))) {
    //                 inputElement = element;
    //                 hasElement = true;
    //             }   
    //         });        
    //     }

    //     if (!hasElement) return;

    //     if (isNaN(pricing = normalizePrice($('.modal input#'+inputElement).val()))) {
    //         return;
    //     }

    //   //  console.log(salesUnitType, salesQty, warehouseUnitType, warehouseQty);

    //     //calculate unit price 
    //     if (warehouseUnitType == 'roll') {

    //         let itemQuantity = 0,
    //             rollWidth = parseInt(this.getRelatedItemPropertyBySystemUsage(
    //             itemData.related_item, 
    //             'item.unittype.roll.width'
    //         )['value']);

            

    

    //         if (salesUnitType == 'm1') {
    //             itemQuantity = salesQty * rollWidth;
    //         }

    //         if (salesUnitType == 'm2') {
    //             itemQuantity = salesQty * rollWidth;
    //         }

    //     }



    //     if (inputElement == 'pricing_unittype_sales') {
    //         $('.modal input#pricing_unittype_warehouse').val(formatPrice(pricing * salesQty, 2));
    //         $('.modal input#pricing_total').val(formatPrice((pricing * salesQty).toFixed(2) * warehouseQty, 2));
    //         return;
    //     }
        
    //     if (inputElement == 'pricing_unittype_warehouse') {
    //         if ($('.modal select#unittype_sales').selectpicker('val') != '') {
    //             $('.modal input#pricing_unittype_sales').val(formatPrice(pricing / salesQty, 2));
    //         }
    //         $('.modal input#pricing_total').val(formatPrice(pricing * warehouseQty, 2));
    //         return;
    //     }
        
    //     if (inputElement == 'pricing_total') {
    //         if ($('.modal select#unittype_sales').selectpicker('val') != '') {
    //             $('.modal input#pricing_unittype_sales').val(formatPrice((pricing / warehouseQty).toFixed(2) / salesQty, 2));
    //         }
    //         $('.modal input#pricing_unittype_warehouse').val(formatPrice(pricing / warehouseQty, 2));
    //         return;
    //     }
    // }
    


    // getItemIndexByKeyValue(key, value)
    // {
    //     let index = false;
    //     this.items.forEach(function(i, idx) {
    //         if ((i[key] ?? null) == value) {
    //             index = idx;
    //             return;
    //         }
    //     });

    //     return index;
    // }

    // getItemByKeyValue(key, value)
    // {   
    //     let idx = this.getItemIndexByKeyValue(key, value);

    //     return idx === false
    //         ? false
    //         : this.items[idx]
    //     ;
    // }



    // setData()
    // {
    //     $('form #items').val(JSON.stringify(this.items));
    // }
}