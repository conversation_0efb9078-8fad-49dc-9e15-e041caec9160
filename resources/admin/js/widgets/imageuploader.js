import { Modal } from './modal.js';
// import { uniqueId } from './functions.js';
// import { getTemplate } from '../functions';

export class ImageUploader {

    constructor(element) {

        //check if element is a string or an object
        if (typeof element === 'string') {
            this.element = $(element);
        } else {
            this.element = element;
        }

        //validate if element exists
        if (this.element.length === 0) return;
        
        this.container = this.element.find('.media-container');

        this.type = this.element.data('type');

        this.queue = [];
        this.processing = [];
        this.processingMaxItems = 5;
       
        this.media = [];

        this.events();

        this.load();
    }

    events() {

        let self = this;

        this.element.find('.btn-upload').on('click', function() {
            self.element.find('input[name=upload]').trigger('click');
        });

        self.element.find('input[name=upload]').on('change', function(e) {
            e.preventDefault(); // Stop default browser behavior
            e.stopPropagation(); // Stop event bubbling

            let files = e.target.files;
            if (files.length > 0) {
                self.addToQueue(files);
            }
        });

        self.element.find('.image-block-uploader').on('drop', function(e) {

            e.preventDefault(); // Stop default browser behavior
            e.stopPropagation(); // Stop event bubbling

            let files = e.originalEvent.dataTransfer.files; // Use dataTransfer.files directly

            if (files.length > 0) {
                self.addToQueue(files);
            }
            
            self.element.find('.image-block-uploader').removeClass('dragover');
        });

        self.element.find('.image-block-uploader').on('dragover', function(e) {
            e.preventDefault(); // Prevent browser default behavior (like opening files)
            e.stopPropagation();
            self.element.find('.image-block-uploader').addClass('dragover');
        });

        self.element.find('.image-block-uploader').on('dragleave', function(e) {
            e.preventDefault(); // Prevent browser default behavior (like opening files)
            e.stopPropagation();
            self.element.find('.image-block-uploader').removeClass('dragover');
        });

        

    }

    load() { 
        this.setBlockUploaderVisiblity();

        this.addBlock();
    }

    addToQueue(files)
    {
        let self = this;

        console.log(files);

        //loop files and create image-block-media element from templates and add to this.queue for uploading   
        $.each(files, function(index, file) {

            let fileUniqId = uniqueId(),
                block = self.addBlock(fileUniqId),
                reader = new FileReader()
            ;

            reader.onload = function(e) {
                block.element.find('.media').attr('src', e.target.result);
            };

            self.queue.push({
                file: file,
                uniq_id: block.uniq_id,
            });

            block.element.insertBefore(self.container.find('.image-block-uploader'));

            self.runQueue();
        });
    }

    runQueue()
    {
        if (this.processingMaxItems <= this.processing.length) {
            return;
        }

        let self = this;

        let item = this.queue.shift();

        if (item) {

            this.processing.push(item);

            self.upload(item);
        }
    }

    upload(item)
    {
        console.log(item);
        let self = this;

        let formData = new FormData();

        //loop files and append to formdata
        formData.append('file', item.file);
        formData.append('uniq_id', item.uniq_id);

        $.ajax({
            url: '/actions/imageuploader/upload',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {

                console.log(response);

                if (response.success) {
                    self.addMedia(response.media);
                }
            }
        });

    }

    addBlock(uniqId = null)
    {
        let self = this;
        
        uniqId = uniqId || uniqueId();

        let element = getTemplate('image-block-media', {
            module_uniq_id: self.element.data('uniq-id'),
            uniq_id: uniqId
        });

       // element.insertBefore(self.container.find('.image-block-uploader'));

        return {
            element: element,
            uniq_id: uniqId
        };
    }

    setBlockUploaderVisiblity() 
    {
        this.container.find('.block-uploader')[this.container.find('.image-block-media').length > 0
            ? 'fadeOut'
            : 'fadeIn'
        ]();
    }

}