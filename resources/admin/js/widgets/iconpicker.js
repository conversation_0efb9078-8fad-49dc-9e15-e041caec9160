
import { Modal } from './modal.js';

export class IconPicker {

    constructor(element) {

        if (typeof element == 'undefined') {
            return;
        }

        this.callback = false;
        if (typeof element.element == 'undefined') {
            this.element = element;
        } else {

            this.element = element.element;
            if (typeof element.callback != 'undefined') {
                this.callback = element.callback;
            }
        }

        this.hash = Math.floor(Math.random() * 10000);
        // this.iconsUploaded = [];

        this.element.closest('.input-group').find('.input-delete').data('hash', this.hash);
       
        // this.getUploadedIcons();

        this.events();

        this.update();
    }

    events() {

        let self = this;

        $(document).on('click', '.modal .icon-container .icon', function () {

            if ($(this).data('hash') == self.hash) {

                self.element.val($(this).data('icon'));
                self.element.closest('.input-group').find('.input-group-append')
            
                self.update();

                bootstrap.Modal.getInstance($('.modal')).hide();
            }
        });

        $(document).on('click', '.modal .icon-container-uploaded .icon', function (e) {

            if ($(this).data('hash') == self.hash) {
            
                let iconTmp;
                iconTmp = $(this).data('icon').split('/'); 
                iconTmp = iconTmp.pop();

                self.element.val(iconTmp);
    
                self.update();

                bootstrap.Modal.getInstance($('.modal')).hide();
            }
        });

        $(document).on('click', '.input-delete', function () {
            if ($(this).data('hash') == self.hash) {
                self.element.val('');
                self.update();
            }
        });

        $(document).on('keyup', '.modal .icon-search .btn-search', function() {

            let val = $(this).val();

            $('.modal .icon-container .icon').removeClass('d-none');

            $('.modal .icon-container .icon').each(function() {
                if ($(this).data('icon').indexOf(val) == -1) {
                    $(this).addClass('d-none');
                }
            });
        });

        $(document).on('keyup', '.modal .icon-search input', function() {

            let val = $(this).val();

            $('.modal .icon-container .icon').removeClass('d-none');

            $('.modal .icon-container .icon').each(function() {
                if ($(this).data('icon').indexOf(val) == -1) {
                    $(this).addClass('d-none');
                }
            });
        });

        $(document).on('click', '.modal .btn-upload', function() {

            $('#fileid').click();
        });

        // $(document).on('change', '.modal #fileid', function() {

        //     let formData = new FormData();
        //     formData.append('file', $(this)[0].files[0]);

        //     $.ajax({
        //         url: config.domain+'/'+config.app+'/functions/module_iconpicker_upload.php',
        //         type: 'post',
        //         data: formData,
        //         contentType: false,
        //         processData: false,
        //         dataType: 'json',
        //         success: function(response) {
                
        //             if (response.error == 0){
        //                 self.addImage({
        //                     'icon': response.data.image_src_uploader,
        //                     'used': false
        //                 });
        //             }
        //         }
        //     });
        // });

        $(document).on('click', '.modal .icon-container-uploaded .icon .icon-uploaded-delete', function(e) {

            e.stopPropagation();

            self.delete($(this).closest('.icon').data('icon'));
        });
        

        self.element.on('click keydown', function (e) {

            let icons = self.icons(),
                i = 0,
                l = icons.length,
                str = [],
                selected,
                selectedStr = '',
                strUploaded = [],
                uploadedSelected = '',
                uploadedVisible = 'd-none',
                iconTmp,
                del;

            for (i = 0; i < l; ++i) {

                selected = '';
                if (self.element.val() != '' &&
                    self.element.val() == icons[i].name) {
                    selected = 'text-success';

                    selectedStr = `
                        <div class="col-2 col-lg-1 border border-success text-center p-3 icon `+ selected + `" data-hash="` + self.hash + `" data-icon="` + icons[i].name + `">
                            <i class="fa-2x fa`+ icons[i].type + ` fa-` + icons[i].name + ` ` + selected + `"></i>
                        </div>`;
                } else {

                    str.push(`
                        <div class="col-auto col-lg-1 border text-center p-3 icon `+ selected + `" data-hash="` + self.hash + `" data-icon="` + icons[i].name + `">
                            <i class="fa-2x fa`+ icons[i].type + ` fa-` + icons[i].name + ` ` + selected + `"></i>
                        </div>`);
                }

            }

            uploadedVisible = strUploaded.length > 0
                ? ''
                : 'd-none';

            str = `
                <div class="container icon-search mb-3">
                    <div class="row">
                        <div class="col ml-n3">
                            <input type="text" class="form-control form-control-sm" /> 
                        </div>
                    </div>
                </div>

                <div class="container icon-uploaded mb-3 `+uploadedVisible+`">
                    <div class="row icon-container-uploaded" style="overflow-y: scroll;">
                        `+strUploaded.join('')+`
                    </div>
                </div>

                <div class="container icon-container" style="height:450px;overflow-y: scroll;">
                    <div class="row">
                        `+selectedStr+`
                        `+ str.join('') + `
                    </div>
                </div>`;

            new Modal({
                "title": 'Icon picker',
                "class": 'modal-xl',
                "content": str,
                "buttons": [
                    '<button type="button" class="btn btn-danger" data-bs-dismiss="modal">Cancel</button>'
                ],
                'callBack': function() {
                    $('.modal-content').css('height', $('.modal-content').height());
                }
            });

        });

    }

    update() 
    {
        let self = this;

        this.element.closest('.input-group').find('.input-delete').addClass('d-none');
        this.element.closest('.input-group').find('.input-upload-preview').addClass('d-none');
        this.element.closest('.input-group').find('.input-icon-preview').addClass('d-none');
       
        if (this.element.val() != '') {
            this.element.closest('.input-group').find('.input-delete').removeClass('d-none');
        }

        if (this.element.val() != '') {

            
            if (this.element.val().indexOf('.') == -1) {

                this.element.closest('.input-group').find('.input-icon-preview').removeClass('d-none');            

                let i = 0,
                    icons = this.icons(),
                    l = icons.length,
                    icon = false;

                while (i < l) {
                    if (icons[i].name == this.element.val()) {
                        icon = icons[i];
                    }
                    ++i;
                }


                if (icon !== false) {
                    this.element.closest('.input-group').find('.input-icon-preview i').remove();
                    this.element.closest('.input-group').find('.input-icon-preview').append($('<i class="fa'+icon.type+' fa-'+icon.name+'" />'));
                }
            }
            else {
                
                this.element.closest('.input-group').find('.input-upload-preview')
                    .removeClass('d-none')
                    .html('<img style="width:15px;max-height:15px;" src="'+this.element.closest('.input-group').data('dir')+this.element.val()+'" />');
            }
        }

        if (this.callback !== false) {
            this.callback();
        }
    }

    getUploadedIcons() 
    {
        let self = this;

        // $.ajax({
        //     url: config.domain+'/'+config.app+'/functions/module_iconpicker_get_uploaded.php',
        //     type: 'get',
        //     contentType: false,
        //     processData: false,
        //     dataType: 'json',
        //     success: function(response) {
            
        //         if (response.error == 0){
        //             response.icons.forEach(function(item, key) {
        //                 self.addImage(item);
        //             });   
        //         }
        //     }
        // });
    }

    delete(icon) 
    {
        let self = this,
            origin = icon;

        icon = icon.split('/');
        icon = icon.pop();

        // $.ajax({
        //     url: config.domain+'/'+config.app+'/functions/module_iconpicker_delete_uploaded.php',
        //     type: 'post',
        //     data: {
        //         'icon': icon
        //     },
        //     dataType: 'json',
        //     success: function(response) {

        //         if (response.error == 0){

        //             let list = [];
        //             self.iconsUploaded.forEach(function(item) {
        //                 if (item.icon != origin) {
        //                     list.push(item);
        //                 }
        //             });

        //             self.iconsUploaded = list;

        //             $('.modal .icon-container-uploaded .icon').each(function() {

        //                 if ($(this).data('icon') == origin) {
        //                     $(this).remove();
        //                 }

        //             });

        //             if ($('.modal .icon-container-uploaded .icon').length == 0) {
        //                 $('modal .icon-uploaded').addClass('d-none');
        //             }

        //             console.log(self.iconsUploaded);

        //             // response.icons.forEach(function(item, key) {
        //             //     self.addImage(item);
        //             // });   
        //         }
        //     }
        // });
    }

    addImage(icon) {

        this.iconsUploaded.push(icon);
        
        if ($('.modal .icon-container-uploaded').length > 0) {


            let selected = icon.icon == this.element.val()
                ? ' selected'
                : '';

            let del = icon.used == false
                ? '<span class="icon-uploaded-delete" style="z-index:999999;position:absolute;top:-4px;right:3px;"><i class="fas fa-times"></i></span>'
                : '';

            let str = `
                <div class="col-auto col-lg-1 border text-center p-3 icon `+selected+`" data-icon="` + icon.icon + `">
                    <img src="`+icon.icon+`" style="wisdth:100%;max-width:40px; max-height:40px;" />
                    `+del+`
                </div>
            `;

            $('.icon-container-uploaded').append(str);

            $('.icon-uploaded').removeClass('d-none');
        }
    }

    icons() {

        return JSON.parse('[{"name":"500px","type":"b"},{"name":"accessible-icon","type":"b"},{"name":"accusoft","type":"b"},{"name":"acquisitions-incorporated","type":"b"},{"name":"ad","type":"s"},{"name":"address-book","type":"s"},{"name":"address-book","type":"r"},{"name":"address-card","type":"s"},{"name":"address-card","type":"r"},{"name":"adjust","type":"s"},{"name":"adn","type":"b"},{"name":"adversal","type":"b"},{"name":"affiliatetheme","type":"b"},{"name":"air-freshener","type":"s"},{"name":"airbnb","type":"b"},{"name":"algolia","type":"b"},{"name":"align-center","type":"s"},{"name":"align-justify","type":"s"},{"name":"align-left","type":"s"},{"name":"align-right","type":"s"},{"name":"alipay","type":"b"},{"name":"allergies","type":"s"},{"name":"amazon","type":"b"},{"name":"amazon-pay","type":"b"},{"name":"ambulance","type":"s"},{"name":"american-sign-language-interpreting","type":"s"},{"name":"amilia","type":"b"},{"name":"anchor","type":"s"},{"name":"android","type":"b"},{"name":"angellist","type":"b"},{"name":"angle-double-down","type":"s"},{"name":"angle-double-left","type":"s"},{"name":"angle-double-right","type":"s"},{"name":"angle-double-up","type":"s"},{"name":"angle-down","type":"s"},{"name":"angle-left","type":"s"},{"name":"angle-right","type":"s"},{"name":"angle-up","type":"s"},{"name":"angry","type":"s"},{"name":"angry","type":"r"},{"name":"angrycreative","type":"b"},{"name":"angular","type":"b"},{"name":"ankh","type":"s"},{"name":"app-store","type":"b"},{"name":"app-store-ios","type":"b"},{"name":"apper","type":"b"},{"name":"apple","type":"b"},{"name":"apple-alt","type":"s"},{"name":"apple-pay","type":"b"},{"name":"archive","type":"s"},{"name":"archway","type":"s"},{"name":"arrow-alt-circle-down","type":"s"},{"name":"arrow-alt-circle-down","type":"r"},{"name":"arrow-alt-circle-left","type":"s"},{"name":"arrow-alt-circle-left","type":"r"},{"name":"arrow-alt-circle-right","type":"s"},{"name":"arrow-alt-circle-right","type":"r"},{"name":"arrow-alt-circle-up","type":"s"},{"name":"arrow-alt-circle-up","type":"r"},{"name":"arrow-circle-down","type":"s"},{"name":"arrow-circle-left","type":"s"},{"name":"arrow-circle-right","type":"s"},{"name":"arrow-circle-up","type":"s"},{"name":"arrow-down","type":"s"},{"name":"arrow-left","type":"s"},{"name":"arrow-right","type":"s"},{"name":"arrow-up","type":"s"},{"name":"arrows-alt","type":"s"},{"name":"arrows-alt-h","type":"s"},{"name":"arrows-alt-v","type":"s"},{"name":"artstation","type":"b"},{"name":"assistive-listening-systems","type":"s"},{"name":"asterisk","type":"s"},{"name":"asymmetrik","type":"b"},{"name":"at","type":"s"},{"name":"atlas","type":"s"},{"name":"atlassian","type":"b"},{"name":"atom","type":"s"},{"name":"audible","type":"b"},{"name":"audio-description","type":"s"},{"name":"autoprefixer","type":"b"},{"name":"avianex","type":"b"},{"name":"aviato","type":"b"},{"name":"award","type":"s"},{"name":"aws","type":"b"},{"name":"baby","type":"s"},{"name":"baby-carriage","type":"s"},{"name":"backspace","type":"s"},{"name":"backward","type":"s"},{"name":"bacon","type":"s"},{"name":"bacteria","type":"s"},{"name":"bacterium","type":"s"},{"name":"bahai","type":"s"},{"name":"balance-scale","type":"s"},{"name":"balance-scale-left","type":"s"},{"name":"balance-scale-right","type":"s"},{"name":"ban","type":"s"},{"name":"band-aid","type":"s"},{"name":"bandcamp","type":"b"},{"name":"barcode","type":"s"},{"name":"bars","type":"s"},{"name":"baseball-ball","type":"s"},{"name":"basketball-ball","type":"s"},{"name":"bath","type":"s"},{"name":"battery-empty","type":"s"},{"name":"battery-full","type":"s"},{"name":"battery-half","type":"s"},{"name":"battery-quarter","type":"s"},{"name":"battery-three-quarters","type":"s"},{"name":"battle-net","type":"b"},{"name":"bed","type":"s"},{"name":"beer","type":"s"},{"name":"behance","type":"b"},{"name":"behance-square","type":"b"},{"name":"bell","type":"s"},{"name":"bell","type":"r"},{"name":"bell-slash","type":"s"},{"name":"bell-slash","type":"r"},{"name":"bezier-curve","type":"s"},{"name":"bible","type":"s"},{"name":"bicycle","type":"s"},{"name":"biking","type":"s"},{"name":"bimobject","type":"b"},{"name":"binoculars","type":"s"},{"name":"biohazard","type":"s"},{"name":"birthday-cake","type":"s"},{"name":"bitbucket","type":"b"},{"name":"bitcoin","type":"b"},{"name":"bity","type":"b"},{"name":"black-tie","type":"b"},{"name":"blackberry","type":"b"},{"name":"blender","type":"s"},{"name":"blender-phone","type":"s"},{"name":"blind","type":"s"},{"name":"blog","type":"s"},{"name":"blogger","type":"b"},{"name":"blogger-b","type":"b"},{"name":"bluetooth","type":"b"},{"name":"bluetooth-b","type":"b"},{"name":"bold","type":"s"},{"name":"bolt","type":"s"},{"name":"bomb","type":"s"},{"name":"bone","type":"s"},{"name":"bong","type":"s"},{"name":"book","type":"s"},{"name":"book-dead","type":"s"},{"name":"book-medical","type":"s"},{"name":"book-open","type":"s"},{"name":"book-reader","type":"s"},{"name":"bookmark","type":"s"},{"name":"bookmark","type":"r"},{"name":"bootstrap","type":"b"},{"name":"border-all","type":"s"},{"name":"border-none","type":"s"},{"name":"border-style","type":"s"},{"name":"bowling-ball","type":"s"},{"name":"box","type":"s"},{"name":"box-open","type":"s"},{"name":"box-tissue","type":"s"},{"name":"boxes","type":"s"},{"name":"braille","type":"s"},{"name":"brain","type":"s"},{"name":"bread-slice","type":"s"},{"name":"briefcase","type":"s"},{"name":"briefcase-medical","type":"s"},{"name":"broadcast-tower","type":"s"},{"name":"broom","type":"s"},{"name":"brush","type":"s"},{"name":"btc","type":"b"},{"name":"buffer","type":"b"},{"name":"bug","type":"s"},{"name":"building","type":"s"},{"name":"building","type":"r"},{"name":"bullhorn","type":"s"},{"name":"bullseye","type":"s"},{"name":"burn","type":"s"},{"name":"buromobelexperte","type":"b"},{"name":"bus","type":"s"},{"name":"bus-alt","type":"s"},{"name":"business-time","type":"s"},{"name":"buy-n-large","type":"b"},{"name":"buysellads","type":"b"},{"name":"calculator","type":"s"},{"name":"calendar","type":"s"},{"name":"calendar","type":"r"},{"name":"calendar-alt","type":"s"},{"name":"calendar-alt","type":"r"},{"name":"calendar-check","type":"s"},{"name":"calendar-check","type":"r"},{"name":"calendar-day","type":"s"},{"name":"calendar-minus","type":"s"},{"name":"calendar-minus","type":"r"},{"name":"calendar-plus","type":"s"},{"name":"calendar-plus","type":"r"},{"name":"calendar-times","type":"s"},{"name":"calendar-times","type":"r"},{"name":"calendar-week","type":"s"},{"name":"camera","type":"s"},{"name":"camera-retro","type":"s"},{"name":"campground","type":"s"},{"name":"canadian-maple-leaf","type":"b"},{"name":"candy-cane","type":"s"},{"name":"cannabis","type":"s"},{"name":"capsules","type":"s"},{"name":"car","type":"s"},{"name":"car-alt","type":"s"},{"name":"car-battery","type":"s"},{"name":"car-crash","type":"s"},{"name":"car-side","type":"s"},{"name":"caravan","type":"s"},{"name":"caret-down","type":"s"},{"name":"caret-left","type":"s"},{"name":"caret-right","type":"s"},{"name":"caret-square-down","type":"s"},{"name":"caret-square-down","type":"r"},{"name":"caret-square-left","type":"s"},{"name":"caret-square-left","type":"r"},{"name":"caret-square-right","type":"s"},{"name":"caret-square-right","type":"r"},{"name":"caret-square-up","type":"s"},{"name":"caret-square-up","type":"r"},{"name":"caret-up","type":"s"},{"name":"carrot","type":"s"},{"name":"cart-arrow-down","type":"s"},{"name":"cart-plus","type":"s"},{"name":"cash-register","type":"s"},{"name":"cat","type":"s"},{"name":"cc-amazon-pay","type":"b"},{"name":"cc-amex","type":"b"},{"name":"cc-apple-pay","type":"b"},{"name":"cc-diners-club","type":"b"},{"name":"cc-discover","type":"b"},{"name":"cc-jcb","type":"b"},{"name":"cc-mastercard","type":"b"},{"name":"cc-paypal","type":"b"},{"name":"cc-stripe","type":"b"},{"name":"cc-visa","type":"b"},{"name":"centercode","type":"b"},{"name":"centos","type":"b"},{"name":"certificate","type":"s"},{"name":"chair","type":"s"},{"name":"chalkboard","type":"s"},{"name":"chalkboard-teacher","type":"s"},{"name":"charging-station","type":"s"},{"name":"chart-area","type":"s"},{"name":"chart-bar","type":"s"},{"name":"chart-bar","type":"r"},{"name":"chart-line","type":"s"},{"name":"chart-pie","type":"s"},{"name":"check","type":"s"},{"name":"check-circle","type":"s"},{"name":"check-circle","type":"r"},{"name":"check-double","type":"s"},{"name":"check-square","type":"s"},{"name":"check-square","type":"r"},{"name":"cheese","type":"s"},{"name":"chess","type":"s"},{"name":"chess-bishop","type":"s"},{"name":"chess-board","type":"s"},{"name":"chess-king","type":"s"},{"name":"chess-knight","type":"s"},{"name":"chess-pawn","type":"s"},{"name":"chess-queen","type":"s"},{"name":"chess-rook","type":"s"},{"name":"chevron-circle-down","type":"s"},{"name":"chevron-circle-left","type":"s"},{"name":"chevron-circle-right","type":"s"},{"name":"chevron-circle-up","type":"s"},{"name":"chevron-down","type":"s"},{"name":"chevron-left","type":"s"},{"name":"chevron-right","type":"s"},{"name":"chevron-up","type":"s"},{"name":"child","type":"s"},{"name":"chrome","type":"b"},{"name":"chromecast","type":"b"},{"name":"church","type":"s"},{"name":"circle","type":"s"},{"name":"circle","type":"r"},{"name":"circle-notch","type":"s"},{"name":"city","type":"s"},{"name":"clinic-medical","type":"s"},{"name":"clipboard","type":"s"},{"name":"clipboard","type":"r"},{"name":"clipboard-check","type":"s"},{"name":"clipboard-list","type":"s"},{"name":"clock","type":"s"},{"name":"clock","type":"r"},{"name":"clone","type":"s"},{"name":"clone","type":"r"},{"name":"closed-captioning","type":"s"},{"name":"closed-captioning","type":"r"},{"name":"cloud","type":"s"},{"name":"cloud-download-alt","type":"s"},{"name":"cloud-meatball","type":"s"},{"name":"cloud-moon","type":"s"},{"name":"cloud-moon-rain","type":"s"},{"name":"cloud-rain","type":"s"},{"name":"cloud-showers-heavy","type":"s"},{"name":"cloud-sun","type":"s"},{"name":"cloud-sun-rain","type":"s"},{"name":"cloud-upload-alt","type":"s"},{"name":"cloudflare","type":"b"},{"name":"cloudscale","type":"b"},{"name":"cloudsmith","type":"b"},{"name":"cloudversify","type":"b"},{"name":"cocktail","type":"s"},{"name":"code","type":"s"},{"name":"code-branch","type":"s"},{"name":"codepen","type":"b"},{"name":"codiepie","type":"b"},{"name":"coffee","type":"s"},{"name":"cog","type":"s"},{"name":"cogs","type":"s"},{"name":"coins","type":"s"},{"name":"columns","type":"s"},{"name":"comment","type":"s"},{"name":"comment","type":"r"},{"name":"comment-alt","type":"s"},{"name":"comment-alt","type":"r"},{"name":"comment-dollar","type":"s"},{"name":"comment-dots","type":"s"},{"name":"comment-dots","type":"r"},{"name":"comment-medical","type":"s"},{"name":"comment-slash","type":"s"},{"name":"comments","type":"s"},{"name":"comments","type":"r"},{"name":"comments-dollar","type":"s"},{"name":"compact-disc","type":"s"},{"name":"compass","type":"s"},{"name":"compass","type":"r"},{"name":"compress","type":"s"},{"name":"compress-alt","type":"s"},{"name":"compress-arrows-alt","type":"s"},{"name":"concierge-bell","type":"s"},{"name":"confluence","type":"b"},{"name":"connectdevelop","type":"b"},{"name":"contao","type":"b"},{"name":"cookie","type":"s"},{"name":"cookie-bite","type":"s"},{"name":"copy","type":"s"},{"name":"copy","type":"r"},{"name":"copyright","type":"s"},{"name":"copyright","type":"r"},{"name":"cotton-bureau","type":"b"},{"name":"couch","type":"s"},{"name":"cpanel","type":"b"},{"name":"creative-commons","type":"b"},{"name":"creative-commons-by","type":"b"},{"name":"creative-commons-nc","type":"b"},{"name":"creative-commons-nc-eu","type":"b"},{"name":"creative-commons-nc-jp","type":"b"},{"name":"creative-commons-nd","type":"b"},{"name":"creative-commons-pd","type":"b"},{"name":"creative-commons-pd-alt","type":"b"},{"name":"creative-commons-remix","type":"b"},{"name":"creative-commons-sa","type":"b"},{"name":"creative-commons-sampling","type":"b"},{"name":"creative-commons-sampling-plus","type":"b"},{"name":"creative-commons-share","type":"b"},{"name":"creative-commons-zero","type":"b"},{"name":"credit-card","type":"s"},{"name":"credit-card","type":"r"},{"name":"critical-role","type":"b"},{"name":"crop","type":"s"},{"name":"crop-alt","type":"s"},{"name":"cross","type":"s"},{"name":"crosshairs","type":"s"},{"name":"crow","type":"s"},{"name":"crown","type":"s"},{"name":"crutch","type":"s"},{"name":"css3","type":"b"},{"name":"css3-alt","type":"b"},{"name":"cube","type":"s"},{"name":"cubes","type":"s"},{"name":"cut","type":"s"},{"name":"cuttlefish","type":"b"},{"name":"d-and-d","type":"b"},{"name":"d-and-d-beyond","type":"b"},{"name":"dailymotion","type":"b"},{"name":"dashcube","type":"b"},{"name":"database","type":"s"},{"name":"deaf","type":"s"},{"name":"deezer","type":"b"},{"name":"delicious","type":"b"},{"name":"democrat","type":"s"},{"name":"deploydog","type":"b"},{"name":"deskpro","type":"b"},{"name":"desktop","type":"s"},{"name":"dev","type":"b"},{"name":"deviantart","type":"b"},{"name":"dharmachakra","type":"s"},{"name":"dhl","type":"b"},{"name":"diagnoses","type":"s"},{"name":"diaspora","type":"b"},{"name":"dice","type":"s"},{"name":"dice-d20","type":"s"},{"name":"dice-d6","type":"s"},{"name":"dice-five","type":"s"},{"name":"dice-four","type":"s"},{"name":"dice-one","type":"s"},{"name":"dice-six","type":"s"},{"name":"dice-three","type":"s"},{"name":"dice-two","type":"s"},{"name":"digg","type":"b"},{"name":"digital-ocean","type":"b"},{"name":"digital-tachograph","type":"s"},{"name":"directions","type":"s"},{"name":"discord","type":"b"},{"name":"discourse","type":"b"},{"name":"disease","type":"s"},{"name":"divide","type":"s"},{"name":"dizzy","type":"s"},{"name":"dizzy","type":"r"},{"name":"dna","type":"s"},{"name":"dochub","type":"b"},{"name":"docker","type":"b"},{"name":"dog","type":"s"},{"name":"dollar-sign","type":"s"},{"name":"dolly","type":"s"},{"name":"dolly-flatbed","type":"s"},{"name":"donate","type":"s"},{"name":"door-closed","type":"s"},{"name":"door-open","type":"s"},{"name":"dot-circle","type":"s"},{"name":"dot-circle","type":"r"},{"name":"dove","type":"s"},{"name":"download","type":"s"},{"name":"draft2digital","type":"b"},{"name":"drafting-compass","type":"s"},{"name":"dragon","type":"s"},{"name":"draw-polygon","type":"s"},{"name":"dribbble","type":"b"},{"name":"dribbble-square","type":"b"},{"name":"dropbox","type":"b"},{"name":"drum","type":"s"},{"name":"drum-steelpan","type":"s"},{"name":"drumstick-bite","type":"s"},{"name":"drupal","type":"b"},{"name":"dumbbell","type":"s"},{"name":"dumpster","type":"s"},{"name":"dumpster-fire","type":"s"},{"name":"dungeon","type":"s"},{"name":"dyalog","type":"b"},{"name":"earlybirds","type":"b"},{"name":"ebay","type":"b"},{"name":"edge","type":"b"},{"name":"edge-legacy","type":"b"},{"name":"edit","type":"s"},{"name":"edit","type":"r"},{"name":"egg","type":"s"},{"name":"eject","type":"s"},{"name":"elementor","type":"b"},{"name":"ellipsis-h","type":"s"},{"name":"ellipsis-v","type":"s"},{"name":"ello","type":"b"},{"name":"ember","type":"b"},{"name":"empire","type":"b"},{"name":"envelope","type":"s"},{"name":"envelope","type":"r"},{"name":"envelope-open","type":"s"},{"name":"envelope-open","type":"r"},{"name":"envelope-open-text","type":"s"},{"name":"envelope-square","type":"s"},{"name":"envira","type":"b"},{"name":"equals","type":"s"},{"name":"eraser","type":"s"},{"name":"erlang","type":"b"},{"name":"ethereum","type":"b"},{"name":"ethernet","type":"s"},{"name":"etsy","type":"b"},{"name":"euro-sign","type":"s"},{"name":"evernote","type":"b"},{"name":"exchange-alt","type":"s"},{"name":"exclamation","type":"s"},{"name":"exclamation-circle","type":"s"},{"name":"exclamation-triangle","type":"s"},{"name":"expand","type":"s"},{"name":"expand-alt","type":"s"},{"name":"expand-arrows-alt","type":"s"},{"name":"expeditedssl","type":"b"},{"name":"external-link-alt","type":"s"},{"name":"external-link-square-alt","type":"s"},{"name":"eye","type":"s"},{"name":"eye","type":"r"},{"name":"eye-dropper","type":"s"},{"name":"eye-slash","type":"s"},{"name":"eye-slash","type":"r"},{"name":"facebook","type":"b"},{"name":"facebook-f","type":"b"},{"name":"facebook-messenger","type":"b"},{"name":"facebook-square","type":"b"},{"name":"fan","type":"s"},{"name":"fantasy-flight-games","type":"b"},{"name":"fast-backward","type":"s"},{"name":"fast-forward","type":"s"},{"name":"faucet","type":"s"},{"name":"fax","type":"s"},{"name":"feather","type":"s"},{"name":"feather-alt","type":"s"},{"name":"fedex","type":"b"},{"name":"fedora","type":"b"},{"name":"female","type":"s"},{"name":"fighter-jet","type":"s"},{"name":"figma","type":"b"},{"name":"file","type":"s"},{"name":"file","type":"r"},{"name":"file-alt","type":"s"},{"name":"file-alt","type":"r"},{"name":"file-archive","type":"s"},{"name":"file-archive","type":"r"},{"name":"file-audio","type":"s"},{"name":"file-audio","type":"r"},{"name":"file-code","type":"s"},{"name":"file-code","type":"r"},{"name":"file-contract","type":"s"},{"name":"file-csv","type":"s"},{"name":"file-download","type":"s"},{"name":"file-excel","type":"s"},{"name":"file-excel","type":"r"},{"name":"file-export","type":"s"},{"name":"file-image","type":"s"},{"name":"file-image","type":"r"},{"name":"file-import","type":"s"},{"name":"file-invoice","type":"s"},{"name":"file-invoice-dollar","type":"s"},{"name":"file-medical","type":"s"},{"name":"file-medical-alt","type":"s"},{"name":"file-pdf","type":"s"},{"name":"file-pdf","type":"r"},{"name":"file-powerpoint","type":"s"},{"name":"file-powerpoint","type":"r"},{"name":"file-prescription","type":"s"},{"name":"file-signature","type":"s"},{"name":"file-upload","type":"s"},{"name":"file-video","type":"s"},{"name":"file-video","type":"r"},{"name":"file-word","type":"s"},{"name":"file-word","type":"r"},{"name":"fill","type":"s"},{"name":"fill-drip","type":"s"},{"name":"film","type":"s"},{"name":"filter","type":"s"},{"name":"fingerprint","type":"s"},{"name":"fire","type":"s"},{"name":"fire-alt","type":"s"},{"name":"fire-extinguisher","type":"s"},{"name":"firefox","type":"b"},{"name":"firefox-browser","type":"b"},{"name":"first-aid","type":"s"},{"name":"first-order","type":"b"},{"name":"first-order-alt","type":"b"},{"name":"firstdraft","type":"b"},{"name":"fish","type":"s"},{"name":"fist-raised","type":"s"},{"name":"flag","type":"s"},{"name":"flag","type":"r"},{"name":"flag-checkered","type":"s"},{"name":"flag-usa","type":"s"},{"name":"flask","type":"s"},{"name":"flickr","type":"b"},{"name":"flipboard","type":"b"},{"name":"flushed","type":"s"},{"name":"flushed","type":"r"},{"name":"fly","type":"b"},{"name":"folder","type":"s"},{"name":"folder","type":"r"},{"name":"folder-minus","type":"s"},{"name":"folder-open","type":"s"},{"name":"folder-open","type":"r"},{"name":"folder-plus","type":"s"},{"name":"font","type":"s"},{"name":"font-awesome","type":"b"},{"name":"font-awesome-alt","type":"b"},{"name":"font-awesome-flag","type":"b"},{"name":"fonticons","type":"b"},{"name":"fonticons-fi","type":"b"},{"name":"football-ball","type":"s"},{"name":"fort-awesome","type":"b"},{"name":"fort-awesome-alt","type":"b"},{"name":"forumbee","type":"b"},{"name":"forward","type":"s"},{"name":"foursquare","type":"b"},{"name":"free-code-camp","type":"b"},{"name":"freebsd","type":"b"},{"name":"frog","type":"s"},{"name":"frown","type":"s"},{"name":"frown","type":"r"},{"name":"frown-open","type":"s"},{"name":"frown-open","type":"r"},{"name":"fulcrum","type":"b"},{"name":"funnel-dollar","type":"s"},{"name":"futbol","type":"s"},{"name":"futbol","type":"r"},{"name":"galactic-republic","type":"b"},{"name":"galactic-senate","type":"b"},{"name":"gamepad","type":"s"},{"name":"gas-pump","type":"s"},{"name":"gavel","type":"s"},{"name":"gem","type":"s"},{"name":"gem","type":"r"},{"name":"genderless","type":"s"},{"name":"get-pocket","type":"b"},{"name":"gg","type":"b"},{"name":"gg-circle","type":"b"},{"name":"ghost","type":"s"},{"name":"gift","type":"s"},{"name":"gifts","type":"s"},{"name":"git","type":"b"},{"name":"git-alt","type":"b"},{"name":"git-square","type":"b"},{"name":"github","type":"b"},{"name":"github-alt","type":"b"},{"name":"github-square","type":"b"},{"name":"gitkraken","type":"b"},{"name":"gitlab","type":"b"},{"name":"gitter","type":"b"},{"name":"glass-cheers","type":"s"},{"name":"glass-martini","type":"s"},{"name":"glass-martini-alt","type":"s"},{"name":"glass-whiskey","type":"s"},{"name":"glasses","type":"s"},{"name":"glide","type":"b"},{"name":"glide-g","type":"b"},{"name":"globe","type":"s"},{"name":"globe-africa","type":"s"},{"name":"globe-americas","type":"s"},{"name":"globe-asia","type":"s"},{"name":"globe-europe","type":"s"},{"name":"gofore","type":"b"},{"name":"golf-ball","type":"s"},{"name":"goodreads","type":"b"},{"name":"goodreads-g","type":"b"},{"name":"google","type":"b"},{"name":"google-drive","type":"b"},{"name":"google-pay","type":"b"},{"name":"google-play","type":"b"},{"name":"google-plus","type":"b"},{"name":"google-plus-g","type":"b"},{"name":"google-plus-square","type":"b"},{"name":"google-wallet","type":"b"},{"name":"gopuram","type":"s"},{"name":"graduation-cap","type":"s"},{"name":"gratipay","type":"b"},{"name":"grav","type":"b"},{"name":"greater-than","type":"s"},{"name":"greater-than-equal","type":"s"},{"name":"grimace","type":"s"},{"name":"grimace","type":"r"},{"name":"grin","type":"s"},{"name":"grin","type":"r"},{"name":"grin-alt","type":"s"},{"name":"grin-alt","type":"r"},{"name":"grin-beam","type":"s"},{"name":"grin-beam","type":"r"},{"name":"grin-beam-sweat","type":"s"},{"name":"grin-beam-sweat","type":"r"},{"name":"grin-hearts","type":"s"},{"name":"grin-hearts","type":"r"},{"name":"grin-squint","type":"s"},{"name":"grin-squint","type":"r"},{"name":"grin-squint-tears","type":"s"},{"name":"grin-squint-tears","type":"r"},{"name":"grin-stars","type":"s"},{"name":"grin-stars","type":"r"},{"name":"grin-tears","type":"s"},{"name":"grin-tears","type":"r"},{"name":"grin-tongue","type":"s"},{"name":"grin-tongue","type":"r"},{"name":"grin-tongue-squint","type":"s"},{"name":"grin-tongue-squint","type":"r"},{"name":"grin-tongue-wink","type":"s"},{"name":"grin-tongue-wink","type":"r"},{"name":"grin-wink","type":"s"},{"name":"grin-wink","type":"r"},{"name":"grip-horizontal","type":"s"},{"name":"grip-lines","type":"s"},{"name":"grip-lines-vertical","type":"s"},{"name":"grip-vertical","type":"s"},{"name":"gripfire","type":"b"},{"name":"grunt","type":"b"},{"name":"guilded","type":"b"},{"name":"guitar","type":"s"},{"name":"gulp","type":"b"},{"name":"h-square","type":"s"},{"name":"hacker-news","type":"b"},{"name":"hacker-news-square","type":"b"},{"name":"hackerrank","type":"b"},{"name":"hamburger","type":"s"},{"name":"hammer","type":"s"},{"name":"hamsa","type":"s"},{"name":"hand-holding","type":"s"},{"name":"hand-holding-heart","type":"s"},{"name":"hand-holding-medical","type":"s"},{"name":"hand-holding-usd","type":"s"},{"name":"hand-holding-water","type":"s"},{"name":"hand-lizard","type":"s"},{"name":"hand-lizard","type":"r"},{"name":"hand-middle-finger","type":"s"},{"name":"hand-paper","type":"s"},{"name":"hand-paper","type":"r"},{"name":"hand-peace","type":"s"},{"name":"hand-peace","type":"r"},{"name":"hand-point-down","type":"s"},{"name":"hand-point-down","type":"r"},{"name":"hand-point-left","type":"s"},{"name":"hand-point-left","type":"r"},{"name":"hand-point-right","type":"s"},{"name":"hand-point-right","type":"r"},{"name":"hand-point-up","type":"s"},{"name":"hand-point-up","type":"r"},{"name":"hand-pointer","type":"s"},{"name":"hand-pointer","type":"r"},{"name":"hand-rock","type":"s"},{"name":"hand-rock","type":"r"},{"name":"hand-scissors","type":"s"},{"name":"hand-scissors","type":"r"},{"name":"hand-sparkles","type":"s"},{"name":"hand-spock","type":"s"},{"name":"hand-spock","type":"r"},{"name":"hands","type":"s"},{"name":"hands-helping","type":"s"},{"name":"hands-wash","type":"s"},{"name":"handshake","type":"s"},{"name":"handshake","type":"r"},{"name":"handshake-alt-slash","type":"s"},{"name":"handshake-slash","type":"s"},{"name":"hanukiah","type":"s"},{"name":"hard-hat","type":"s"},{"name":"hashtag","type":"s"},{"name":"hat-cowboy","type":"s"},{"name":"hat-cowboy-side","type":"s"},{"name":"hat-wizard","type":"s"},{"name":"hdd","type":"s"},{"name":"hdd","type":"r"},{"name":"head-side-cough","type":"s"},{"name":"head-side-cough-slash","type":"s"},{"name":"head-side-mask","type":"s"},{"name":"head-side-virus","type":"s"},{"name":"heading","type":"s"},{"name":"headphones","type":"s"},{"name":"headphones-alt","type":"s"},{"name":"headset","type":"s"},{"name":"heart","type":"s"},{"name":"heart","type":"r"},{"name":"heart-broken","type":"s"},{"name":"heartbeat","type":"s"},{"name":"helicopter","type":"s"},{"name":"highlighter","type":"s"},{"name":"hiking","type":"s"},{"name":"hippo","type":"s"},{"name":"hips","type":"b"},{"name":"hire-a-helper","type":"b"},{"name":"history","type":"s"},{"name":"hive","type":"b"},{"name":"hockey-puck","type":"s"},{"name":"holly-berry","type":"s"},{"name":"home","type":"s"},{"name":"hooli","type":"b"},{"name":"hornbill","type":"b"},{"name":"horse","type":"s"},{"name":"horse-head","type":"s"},{"name":"hospital","type":"s"},{"name":"hospital","type":"r"},{"name":"hospital-alt","type":"s"},{"name":"hospital-symbol","type":"s"},{"name":"hospital-user","type":"s"},{"name":"hot-tub","type":"s"},{"name":"hotdog","type":"s"},{"name":"hotel","type":"s"},{"name":"hotjar","type":"b"},{"name":"hourglass","type":"s"},{"name":"hourglass","type":"r"},{"name":"hourglass-end","type":"s"},{"name":"hourglass-half","type":"s"},{"name":"hourglass-start","type":"s"},{"name":"house-damage","type":"s"},{"name":"house-user","type":"s"},{"name":"houzz","type":"b"},{"name":"hryvnia","type":"s"},{"name":"html5","type":"b"},{"name":"hubspot","type":"b"},{"name":"i-cursor","type":"s"},{"name":"ice-cream","type":"s"},{"name":"icicles","type":"s"},{"name":"icons","type":"s"},{"name":"id-badge","type":"s"},{"name":"id-badge","type":"r"},{"name":"id-card","type":"s"},{"name":"id-card","type":"r"},{"name":"id-card-alt","type":"s"},{"name":"ideal","type":"b"},{"name":"igloo","type":"s"},{"name":"image","type":"s"},{"name":"image","type":"r"},{"name":"images","type":"s"},{"name":"images","type":"r"},{"name":"imdb","type":"b"},{"name":"inbox","type":"s"},{"name":"indent","type":"s"},{"name":"industry","type":"s"},{"name":"infinity","type":"s"},{"name":"info","type":"s"},{"name":"info-circle","type":"s"},{"name":"innosoft","type":"b"},{"name":"instagram","type":"b"},{"name":"instagram-square","type":"b"},{"name":"instalod","type":"b"},{"name":"intercom","type":"b"},{"name":"internet-explorer","type":"b"},{"name":"invision","type":"b"},{"name":"ioxhost","type":"b"},{"name":"italic","type":"s"},{"name":"itch-io","type":"b"},{"name":"itunes","type":"b"},{"name":"itunes-note","type":"b"},{"name":"java","type":"b"},{"name":"jedi","type":"s"},{"name":"jedi-order","type":"b"},{"name":"jenkins","type":"b"},{"name":"jira","type":"b"},{"name":"joget","type":"b"},{"name":"joint","type":"s"},{"name":"joomla","type":"b"},{"name":"journal-whills","type":"s"},{"name":"js","type":"b"},{"name":"js-square","type":"b"},{"name":"jsfiddle","type":"b"},{"name":"kaaba","type":"s"},{"name":"kaggle","type":"b"},{"name":"key","type":"s"},{"name":"keybase","type":"b"},{"name":"keyboard","type":"s"},{"name":"keyboard","type":"r"},{"name":"keycdn","type":"b"},{"name":"khanda","type":"s"},{"name":"kickstarter","type":"b"},{"name":"kickstarter-k","type":"b"},{"name":"kiss","type":"s"},{"name":"kiss","type":"r"},{"name":"kiss-beam","type":"s"},{"name":"kiss-beam","type":"r"},{"name":"kiss-wink-heart","type":"s"},{"name":"kiss-wink-heart","type":"r"},{"name":"kiwi-bird","type":"s"},{"name":"korvue","type":"b"},{"name":"landmark","type":"s"},{"name":"language","type":"s"},{"name":"laptop","type":"s"},{"name":"laptop-code","type":"s"},{"name":"laptop-house","type":"s"},{"name":"laptop-medical","type":"s"},{"name":"laravel","type":"b"},{"name":"lastfm","type":"b"},{"name":"lastfm-square","type":"b"},{"name":"laugh","type":"s"},{"name":"laugh","type":"r"},{"name":"laugh-beam","type":"s"},{"name":"laugh-beam","type":"r"},{"name":"laugh-squint","type":"s"},{"name":"laugh-squint","type":"r"},{"name":"laugh-wink","type":"s"},{"name":"laugh-wink","type":"r"},{"name":"layer-group","type":"s"},{"name":"leaf","type":"s"},{"name":"leanpub","type":"b"},{"name":"lemon","type":"s"},{"name":"lemon","type":"r"},{"name":"less","type":"b"},{"name":"less-than","type":"s"},{"name":"less-than-equal","type":"s"},{"name":"level-down-alt","type":"s"},{"name":"level-up-alt","type":"s"},{"name":"life-ring","type":"s"},{"name":"life-ring","type":"r"},{"name":"lightbulb","type":"s"},{"name":"lightbulb","type":"r"},{"name":"line","type":"b"},{"name":"link","type":"s"},{"name":"linkedin","type":"b"},{"name":"linkedin-in","type":"b"},{"name":"linode","type":"b"},{"name":"linux","type":"b"},{"name":"lira-sign","type":"s"},{"name":"list","type":"s"},{"name":"list-alt","type":"s"},{"name":"list-alt","type":"r"},{"name":"list-ol","type":"s"},{"name":"list-ul","type":"s"},{"name":"location-arrow","type":"s"},{"name":"lock","type":"s"},{"name":"lock-open","type":"s"},{"name":"long-arrow-alt-down","type":"s"},{"name":"long-arrow-alt-left","type":"s"},{"name":"long-arrow-alt-right","type":"s"},{"name":"long-arrow-alt-up","type":"s"},{"name":"low-vision","type":"s"},{"name":"luggage-cart","type":"s"},{"name":"lungs","type":"s"},{"name":"lungs-virus","type":"s"},{"name":"lyft","type":"b"},{"name":"magento","type":"b"},{"name":"magic","type":"s"},{"name":"magnet","type":"s"},{"name":"mail-bulk","type":"s"},{"name":"mailchimp","type":"b"},{"name":"male","type":"s"},{"name":"mandalorian","type":"b"},{"name":"map","type":"s"},{"name":"map","type":"r"},{"name":"map-marked","type":"s"},{"name":"map-marked-alt","type":"s"},{"name":"map-marker","type":"s"},{"name":"map-marker-alt","type":"s"},{"name":"map-pin","type":"s"},{"name":"map-signs","type":"s"},{"name":"markdown","type":"b"},{"name":"marker","type":"s"},{"name":"mars","type":"s"},{"name":"mars-double","type":"s"},{"name":"mars-stroke","type":"s"},{"name":"mars-stroke-h","type":"s"},{"name":"mars-stroke-v","type":"s"},{"name":"mask","type":"s"},{"name":"mastodon","type":"b"},{"name":"maxcdn","type":"b"},{"name":"mdb","type":"b"},{"name":"medal","type":"s"},{"name":"medapps","type":"b"},{"name":"medium","type":"b"},{"name":"medium-m","type":"b"},{"name":"medkit","type":"s"},{"name":"medrt","type":"b"},{"name":"meetup","type":"b"},{"name":"megaport","type":"b"},{"name":"meh","type":"s"},{"name":"meh","type":"r"},{"name":"meh-blank","type":"s"},{"name":"meh-blank","type":"r"},{"name":"meh-rolling-eyes","type":"s"},{"name":"meh-rolling-eyes","type":"r"},{"name":"memory","type":"s"},{"name":"mendeley","type":"b"},{"name":"menorah","type":"s"},{"name":"mercury","type":"s"},{"name":"meteor","type":"s"},{"name":"microblog","type":"b"},{"name":"microchip","type":"s"},{"name":"microphone","type":"s"},{"name":"microphone-alt","type":"s"},{"name":"microphone-alt-slash","type":"s"},{"name":"microphone-slash","type":"s"},{"name":"microscope","type":"s"},{"name":"microsoft","type":"b"},{"name":"minus","type":"s"},{"name":"minus-circle","type":"s"},{"name":"minus-square","type":"s"},{"name":"minus-square","type":"r"},{"name":"mitten","type":"s"},{"name":"mix","type":"b"},{"name":"mixcloud","type":"b"},{"name":"mixer","type":"b"},{"name":"mizuni","type":"b"},{"name":"mobile","type":"s"},{"name":"mobile-alt","type":"s"},{"name":"modx","type":"b"},{"name":"monero","type":"b"},{"name":"money-bill","type":"s"},{"name":"money-bill-alt","type":"s"},{"name":"money-bill-alt","type":"r"},{"name":"money-bill-wave","type":"s"},{"name":"money-bill-wave-alt","type":"s"},{"name":"money-check","type":"s"},{"name":"money-check-alt","type":"s"},{"name":"monument","type":"s"},{"name":"moon","type":"s"},{"name":"moon","type":"r"},{"name":"mortar-pestle","type":"s"},{"name":"mosque","type":"s"},{"name":"motorcycle","type":"s"},{"name":"mountain","type":"s"},{"name":"mouse","type":"s"},{"name":"mouse-pointer","type":"s"},{"name":"mug-hot","type":"s"},{"name":"music","type":"s"},{"name":"napster","type":"b"},{"name":"neos","type":"b"},{"name":"network-wired","type":"s"},{"name":"neuter","type":"s"},{"name":"newspaper","type":"s"},{"name":"newspaper","type":"r"},{"name":"nimblr","type":"b"},{"name":"node","type":"b"},{"name":"node-js","type":"b"},{"name":"not-equal","type":"s"},{"name":"notes-medical","type":"s"},{"name":"npm","type":"b"},{"name":"ns8","type":"b"},{"name":"nutritionix","type":"b"},{"name":"object-group","type":"s"},{"name":"object-group","type":"r"},{"name":"object-ungroup","type":"s"},{"name":"object-ungroup","type":"r"},{"name":"octopus-deploy","type":"b"},{"name":"odnoklassniki","type":"b"},{"name":"odnoklassniki-square","type":"b"},{"name":"oil-can","type":"s"},{"name":"old-republic","type":"b"},{"name":"om","type":"s"},{"name":"opencart","type":"b"},{"name":"openid","type":"b"},{"name":"opera","type":"b"},{"name":"optin-monster","type":"b"},{"name":"orcid","type":"b"},{"name":"osi","type":"b"},{"name":"otter","type":"s"},{"name":"outdent","type":"s"},{"name":"page4","type":"b"},{"name":"pagelines","type":"b"},{"name":"pager","type":"s"},{"name":"paint-brush","type":"s"},{"name":"paint-roller","type":"s"},{"name":"palette","type":"s"},{"name":"palfed","type":"b"},{"name":"pallet","type":"s"},{"name":"paper-plane","type":"s"},{"name":"paper-plane","type":"r"},{"name":"paperclip","type":"s"},{"name":"parachute-box","type":"s"},{"name":"paragraph","type":"s"},{"name":"parking","type":"s"},{"name":"passport","type":"s"},{"name":"pastafarianism","type":"s"},{"name":"paste","type":"s"},{"name":"patreon","type":"b"},{"name":"pause","type":"s"},{"name":"pause-circle","type":"s"},{"name":"pause-circle","type":"r"},{"name":"paw","type":"s"},{"name":"paypal","type":"b"},{"name":"peace","type":"s"},{"name":"pen","type":"s"},{"name":"pen-alt","type":"s"},{"name":"pen-fancy","type":"s"},{"name":"pen-nib","type":"s"},{"name":"pen-square","type":"s"},{"name":"pencil-alt","type":"s"},{"name":"pencil-ruler","type":"s"},{"name":"penny-arcade","type":"b"},{"name":"people-arrows","type":"s"},{"name":"people-carry","type":"s"},{"name":"pepper-hot","type":"s"},{"name":"perbyte","type":"b"},{"name":"percent","type":"s"},{"name":"percentage","type":"s"},{"name":"periscope","type":"b"},{"name":"person-booth","type":"s"},{"name":"phabricator","type":"b"},{"name":"phoenix-framework","type":"b"},{"name":"phoenix-squadron","type":"b"},{"name":"phone","type":"s"},{"name":"phone-alt","type":"s"},{"name":"phone-slash","type":"s"},{"name":"phone-square","type":"s"},{"name":"phone-square-alt","type":"s"},{"name":"phone-volume","type":"s"},{"name":"photo-video","type":"s"},{"name":"php","type":"b"},{"name":"pied-piper","type":"b"},{"name":"pied-piper-alt","type":"b"},{"name":"pied-piper-hat","type":"b"},{"name":"pied-piper-pp","type":"b"},{"name":"pied-piper-square","type":"b"},{"name":"piggy-bank","type":"s"},{"name":"pills","type":"s"},{"name":"pinterest","type":"b"},{"name":"pinterest-p","type":"b"},{"name":"pinterest-square","type":"b"},{"name":"pizza-slice","type":"s"},{"name":"place-of-worship","type":"s"},{"name":"plane","type":"s"},{"name":"plane-arrival","type":"s"},{"name":"plane-departure","type":"s"},{"name":"plane-slash","type":"s"},{"name":"play","type":"s"},{"name":"play-circle","type":"s"},{"name":"play-circle","type":"r"},{"name":"playstation","type":"b"},{"name":"plug","type":"s"},{"name":"plus","type":"s"},{"name":"plus-circle","type":"s"},{"name":"plus-square","type":"s"},{"name":"plus-square","type":"r"},{"name":"podcast","type":"s"},{"name":"poll","type":"s"},{"name":"poll-h","type":"s"},{"name":"poo","type":"s"},{"name":"poo-storm","type":"s"},{"name":"poop","type":"s"},{"name":"portrait","type":"s"},{"name":"pound-sign","type":"s"},{"name":"power-off","type":"s"},{"name":"pray","type":"s"},{"name":"praying-hands","type":"s"},{"name":"prescription","type":"s"},{"name":"prescription-bottle","type":"s"},{"name":"prescription-bottle-alt","type":"s"},{"name":"print","type":"s"},{"name":"procedures","type":"s"},{"name":"product-hunt","type":"b"},{"name":"project-diagram","type":"s"},{"name":"pump-medical","type":"s"},{"name":"pump-soap","type":"s"},{"name":"pushed","type":"b"},{"name":"puzzle-piece","type":"s"},{"name":"python","type":"b"},{"name":"qq","type":"b"},{"name":"qrcode","type":"s"},{"name":"question","type":"s"},{"name":"question-circle","type":"s"},{"name":"question-circle","type":"r"},{"name":"quidditch","type":"s"},{"name":"quinscape","type":"b"},{"name":"quora","type":"b"},{"name":"quote-left","type":"s"},{"name":"quote-right","type":"s"},{"name":"quran","type":"s"},{"name":"r-project","type":"b"},{"name":"radiation","type":"s"},{"name":"radiation-alt","type":"s"},{"name":"rainbow","type":"s"},{"name":"random","type":"s"},{"name":"raspberry-pi","type":"b"},{"name":"ravelry","type":"b"},{"name":"react","type":"b"},{"name":"reacteurope","type":"b"},{"name":"readme","type":"b"},{"name":"rebel","type":"b"},{"name":"receipt","type":"s"},{"name":"record-vinyl","type":"s"},{"name":"recycle","type":"s"},{"name":"red-river","type":"b"},{"name":"reddit","type":"b"},{"name":"reddit-alien","type":"b"},{"name":"reddit-square","type":"b"},{"name":"redhat","type":"b"},{"name":"redo","type":"s"},{"name":"redo-alt","type":"s"},{"name":"registered","type":"s"},{"name":"registered","type":"r"},{"name":"remove-format","type":"s"},{"name":"renren","type":"b"},{"name":"reply","type":"s"},{"name":"reply-all","type":"s"},{"name":"replyd","type":"b"},{"name":"republican","type":"s"},{"name":"researchgate","type":"b"},{"name":"resolving","type":"b"},{"name":"restroom","type":"s"},{"name":"retweet","type":"s"},{"name":"rev","type":"b"},{"name":"ribbon","type":"s"},{"name":"ring","type":"s"},{"name":"road","type":"s"},{"name":"robot","type":"s"},{"name":"rocket","type":"s"},{"name":"rocketchat","type":"b"},{"name":"rockrms","type":"b"},{"name":"route","type":"s"},{"name":"rss","type":"s"},{"name":"rss-square","type":"s"},{"name":"ruble-sign","type":"s"},{"name":"ruler","type":"s"},{"name":"ruler-combined","type":"s"},{"name":"ruler-horizontal","type":"s"},{"name":"ruler-vertical","type":"s"},{"name":"running","type":"s"},{"name":"rupee-sign","type":"s"},{"name":"rust","type":"b"},{"name":"sad-cry","type":"s"},{"name":"sad-cry","type":"r"},{"name":"sad-tear","type":"s"},{"name":"sad-tear","type":"r"},{"name":"safari","type":"b"},{"name":"salesforce","type":"b"},{"name":"sass","type":"b"},{"name":"satellite","type":"s"},{"name":"satellite-dish","type":"s"},{"name":"save","type":"s"},{"name":"save","type":"r"},{"name":"schlix","type":"b"},{"name":"school","type":"s"},{"name":"screwdriver","type":"s"},{"name":"scribd","type":"b"},{"name":"scroll","type":"s"},{"name":"sd-card","type":"s"},{"name":"search","type":"s"},{"name":"search-dollar","type":"s"},{"name":"search-location","type":"s"},{"name":"search-minus","type":"s"},{"name":"search-plus","type":"s"},{"name":"searchengin","type":"b"},{"name":"seedling","type":"s"},{"name":"sellcast","type":"b"},{"name":"sellsy","type":"b"},{"name":"server","type":"s"},{"name":"servicestack","type":"b"},{"name":"shapes","type":"s"},{"name":"share","type":"s"},{"name":"share-alt","type":"s"},{"name":"share-alt-square","type":"s"},{"name":"share-square","type":"s"},{"name":"share-square","type":"r"},{"name":"shekel-sign","type":"s"},{"name":"shield-alt","type":"s"},{"name":"shield-virus","type":"s"},{"name":"ship","type":"s"},{"name":"shipping-fast","type":"s"},{"name":"shirtsinbulk","type":"b"},{"name":"shoe-prints","type":"s"},{"name":"shopify","type":"b"},{"name":"shopping-bag","type":"s"},{"name":"shopping-basket","type":"s"},{"name":"shopping-cart","type":"s"},{"name":"shopware","type":"b"},{"name":"shower","type":"s"},{"name":"shuttle-van","type":"s"},{"name":"sign","type":"s"},{"name":"sign-in-alt","type":"s"},{"name":"sign-language","type":"s"},{"name":"sign-out-alt","type":"s"},{"name":"signal","type":"s"},{"name":"signature","type":"s"},{"name":"sim-card","type":"s"},{"name":"simplybuilt","type":"b"},{"name":"sink","type":"s"},{"name":"sistrix","type":"b"},{"name":"sitemap","type":"s"},{"name":"sith","type":"b"},{"name":"skating","type":"s"},{"name":"sketch","type":"b"},{"name":"skiing","type":"s"},{"name":"skiing-nordic","type":"s"},{"name":"skull","type":"s"},{"name":"skull-crossbones","type":"s"},{"name":"skyatlas","type":"b"},{"name":"skype","type":"b"},{"name":"slack","type":"b"},{"name":"slack-hash","type":"b"},{"name":"slash","type":"s"},{"name":"sleigh","type":"s"},{"name":"sliders-h","type":"s"},{"name":"slideshare","type":"b"},{"name":"smile","type":"s"},{"name":"smile","type":"r"},{"name":"smile-beam","type":"s"},{"name":"smile-beam","type":"r"},{"name":"smile-wink","type":"s"},{"name":"smile-wink","type":"r"},{"name":"smog","type":"s"},{"name":"smoking","type":"s"},{"name":"smoking-ban","type":"s"},{"name":"sms","type":"s"},{"name":"snapchat","type":"b"},{"name":"snapchat-ghost","type":"b"},{"name":"snapchat-square","type":"b"},{"name":"snowboarding","type":"s"},{"name":"snowflake","type":"s"},{"name":"snowflake","type":"r"},{"name":"snowman","type":"s"},{"name":"snowplow","type":"s"},{"name":"soap","type":"s"},{"name":"socks","type":"s"},{"name":"solar-panel","type":"s"},{"name":"sort","type":"s"},{"name":"sort-alpha-down","type":"s"},{"name":"sort-alpha-down-alt","type":"s"},{"name":"sort-alpha-up","type":"s"},{"name":"sort-alpha-up-alt","type":"s"},{"name":"sort-amount-down","type":"s"},{"name":"sort-amount-down-alt","type":"s"},{"name":"sort-amount-up","type":"s"},{"name":"sort-amount-up-alt","type":"s"},{"name":"sort-down","type":"s"},{"name":"sort-numeric-down","type":"s"},{"name":"sort-numeric-down-alt","type":"s"},{"name":"sort-numeric-up","type":"s"},{"name":"sort-numeric-up-alt","type":"s"},{"name":"sort-up","type":"s"},{"name":"soundcloud","type":"b"},{"name":"sourcetree","type":"b"},{"name":"spa","type":"s"},{"name":"space-shuttle","type":"s"},{"name":"speakap","type":"b"},{"name":"speaker-deck","type":"b"},{"name":"spell-check","type":"s"},{"name":"spider","type":"s"},{"name":"spinner","type":"s"},{"name":"splotch","type":"s"},{"name":"spotify","type":"b"},{"name":"spray-can","type":"s"},{"name":"square","type":"s"},{"name":"square","type":"r"},{"name":"square-full","type":"s"},{"name":"square-root-alt","type":"s"},{"name":"squarespace","type":"b"},{"name":"stack-exchange","type":"b"},{"name":"stack-overflow","type":"b"},{"name":"stackpath","type":"b"},{"name":"stamp","type":"s"},{"name":"star","type":"s"},{"name":"star","type":"r"},{"name":"star-and-crescent","type":"s"},{"name":"star-half","type":"s"},{"name":"star-half","type":"r"},{"name":"star-half-alt","type":"s"},{"name":"star-of-david","type":"s"},{"name":"star-of-life","type":"s"},{"name":"staylinked","type":"b"},{"name":"steam","type":"b"},{"name":"steam-square","type":"b"},{"name":"steam-symbol","type":"b"},{"name":"step-backward","type":"s"},{"name":"step-forward","type":"s"},{"name":"stethoscope","type":"s"},{"name":"sticker-mule","type":"b"},{"name":"sticky-note","type":"s"},{"name":"sticky-note","type":"r"},{"name":"stop","type":"s"},{"name":"stop-circle","type":"s"},{"name":"stop-circle","type":"r"},{"name":"stopwatch","type":"s"},{"name":"stopwatch-20","type":"s"},{"name":"store","type":"s"},{"name":"store-alt","type":"s"},{"name":"store-alt-slash","type":"s"},{"name":"store-slash","type":"s"},{"name":"strava","type":"b"},{"name":"stream","type":"s"},{"name":"street-view","type":"s"},{"name":"strikethrough","type":"s"},{"name":"stripe","type":"b"},{"name":"stripe-s","type":"b"},{"name":"stroopwafel","type":"s"},{"name":"studiovinari","type":"b"},{"name":"stumbleupon","type":"b"},{"name":"stumbleupon-circle","type":"b"},{"name":"subscript","type":"s"},{"name":"subway","type":"s"},{"name":"suitcase","type":"s"},{"name":"suitcase-rolling","type":"s"},{"name":"sun","type":"s"},{"name":"sun","type":"r"},{"name":"superpowers","type":"b"},{"name":"superscript","type":"s"},{"name":"supple","type":"b"},{"name":"surprise","type":"s"},{"name":"surprise","type":"r"},{"name":"suse","type":"b"},{"name":"swatchbook","type":"s"},{"name":"swift","type":"b"},{"name":"swimmer","type":"s"},{"name":"swimming-pool","type":"s"},{"name":"symfony","type":"b"},{"name":"synagogue","type":"s"},{"name":"sync","type":"s"},{"name":"sync-alt","type":"s"},{"name":"syringe","type":"s"},{"name":"table","type":"s"},{"name":"table-tennis","type":"s"},{"name":"tablet","type":"s"},{"name":"tablet-alt","type":"s"},{"name":"tablets","type":"s"},{"name":"tachometer-alt","type":"s"},{"name":"tag","type":"s"},{"name":"tags","type":"s"},{"name":"tape","type":"s"},{"name":"tasks","type":"s"},{"name":"taxi","type":"s"},{"name":"teamspeak","type":"b"},{"name":"teeth","type":"s"},{"name":"teeth-open","type":"s"},{"name":"telegram","type":"b"},{"name":"telegram-plane","type":"b"},{"name":"temperature-high","type":"s"},{"name":"temperature-low","type":"s"},{"name":"tencent-weibo","type":"b"},{"name":"tenge","type":"s"},{"name":"terminal","type":"s"},{"name":"text-height","type":"s"},{"name":"text-width","type":"s"},{"name":"th","type":"s"},{"name":"th-large","type":"s"},{"name":"th-list","type":"s"},{"name":"the-red-yeti","type":"b"},{"name":"theater-masks","type":"s"},{"name":"themeco","type":"b"},{"name":"themeisle","type":"b"},{"name":"thermometer","type":"s"},{"name":"thermometer-empty","type":"s"},{"name":"thermometer-full","type":"s"},{"name":"thermometer-half","type":"s"},{"name":"thermometer-quarter","type":"s"},{"name":"thermometer-three-quarters","type":"s"},{"name":"think-peaks","type":"b"},{"name":"thumbs-down","type":"s"},{"name":"thumbs-down","type":"r"},{"name":"thumbs-up","type":"s"},{"name":"thumbs-up","type":"r"},{"name":"thumbtack","type":"s"},{"name":"ticket-alt","type":"s"},{"name":"tiktok","type":"b"},{"name":"times","type":"s"},{"name":"times-circle","type":"s"},{"name":"times-circle","type":"r"},{"name":"tint","type":"s"},{"name":"tint-slash","type":"s"},{"name":"tired","type":"s"},{"name":"tired","type":"r"},{"name":"toggle-off","type":"s"},{"name":"toggle-on","type":"s"},{"name":"toilet","type":"s"},{"name":"toilet-paper","type":"s"},{"name":"toilet-paper-slash","type":"s"},{"name":"toolbox","type":"s"},{"name":"tools","type":"s"},{"name":"tooth","type":"s"},{"name":"torah","type":"s"},{"name":"torii-gate","type":"s"},{"name":"tractor","type":"s"},{"name":"trade-federation","type":"b"},{"name":"trademark","type":"s"},{"name":"traffic-light","type":"s"},{"name":"trailer","type":"s"},{"name":"train","type":"s"},{"name":"tram","type":"s"},{"name":"transgender","type":"s"},{"name":"transgender-alt","type":"s"},{"name":"trash","type":"s"},{"name":"trash-alt","type":"s"},{"name":"trash-alt","type":"r"},{"name":"trash-restore","type":"s"},{"name":"trash-restore-alt","type":"s"},{"name":"tree","type":"s"},{"name":"trello","type":"b"},{"name":"tripadvisor","type":"b"},{"name":"trophy","type":"s"},{"name":"truck","type":"s"},{"name":"truck-loading","type":"s"},{"name":"truck-monster","type":"s"},{"name":"truck-moving","type":"s"},{"name":"truck-pickup","type":"s"},{"name":"tshirt","type":"s"},{"name":"tty","type":"s"},{"name":"tumblr","type":"b"},{"name":"tumblr-square","type":"b"},{"name":"tv","type":"s"},{"name":"twitch","type":"b"},{"name":"twitter","type":"b"},{"name":"twitter-square","type":"b"},{"name":"typo3","type":"b"},{"name":"uber","type":"b"},{"name":"ubuntu","type":"b"},{"name":"uikit","type":"b"},{"name":"umbraco","type":"b"},{"name":"umbrella","type":"s"},{"name":"umbrella-beach","type":"s"},{"name":"uncharted","type":"b"},{"name":"underline","type":"s"},{"name":"undo","type":"s"},{"name":"undo-alt","type":"s"},{"name":"uniregistry","type":"b"},{"name":"unity","type":"b"},{"name":"universal-access","type":"s"},{"name":"university","type":"s"},{"name":"unlink","type":"s"},{"name":"unlock","type":"s"},{"name":"unlock-alt","type":"s"},{"name":"unsplash","type":"b"},{"name":"untappd","type":"b"},{"name":"upload","type":"s"},{"name":"ups","type":"b"},{"name":"usb","type":"b"},{"name":"user","type":"s"},{"name":"user","type":"r"},{"name":"user-alt","type":"s"},{"name":"user-alt-slash","type":"s"},{"name":"user-astronaut","type":"s"},{"name":"user-check","type":"s"},{"name":"user-circle","type":"s"},{"name":"user-circle","type":"r"},{"name":"user-clock","type":"s"},{"name":"user-cog","type":"s"},{"name":"user-edit","type":"s"},{"name":"user-friends","type":"s"},{"name":"user-graduate","type":"s"},{"name":"user-injured","type":"s"},{"name":"user-lock","type":"s"},{"name":"user-md","type":"s"},{"name":"user-minus","type":"s"},{"name":"user-ninja","type":"s"},{"name":"user-nurse","type":"s"},{"name":"user-plus","type":"s"},{"name":"user-secret","type":"s"},{"name":"user-shield","type":"s"},{"name":"user-slash","type":"s"},{"name":"user-tag","type":"s"},{"name":"user-tie","type":"s"},{"name":"user-times","type":"s"},{"name":"users","type":"s"},{"name":"users-cog","type":"s"},{"name":"users-slash","type":"s"},{"name":"usps","type":"b"},{"name":"ussunnah","type":"b"},{"name":"utensil-spoon","type":"s"},{"name":"utensils","type":"s"},{"name":"vaadin","type":"b"},{"name":"vector-square","type":"s"},{"name":"venus","type":"s"},{"name":"venus-double","type":"s"},{"name":"venus-mars","type":"s"},{"name":"vest","type":"s"},{"name":"vest-patches","type":"s"},{"name":"viacoin","type":"b"},{"name":"viadeo","type":"b"},{"name":"viadeo-square","type":"b"},{"name":"vial","type":"s"},{"name":"vials","type":"s"},{"name":"viber","type":"b"},{"name":"video","type":"s"},{"name":"video-slash","type":"s"},{"name":"vihara","type":"s"},{"name":"vimeo","type":"b"},{"name":"vimeo-square","type":"b"},{"name":"vimeo-v","type":"b"},{"name":"vine","type":"b"},{"name":"virus","type":"s"},{"name":"virus-slash","type":"s"},{"name":"viruses","type":"s"},{"name":"vk","type":"b"},{"name":"vnv","type":"b"},{"name":"voicemail","type":"s"},{"name":"volleyball-ball","type":"s"},{"name":"volume-down","type":"s"},{"name":"volume-mute","type":"s"},{"name":"volume-off","type":"s"},{"name":"volume-up","type":"s"},{"name":"vote-yea","type":"s"},{"name":"vr-cardboard","type":"s"},{"name":"vuejs","type":"b"},{"name":"walking","type":"s"},{"name":"wallet","type":"s"},{"name":"warehouse","type":"s"},{"name":"watchman-monitoring","type":"b"},{"name":"water","type":"s"},{"name":"wave-square","type":"s"},{"name":"waze","type":"b"},{"name":"weebly","type":"b"},{"name":"weibo","type":"b"},{"name":"weight","type":"s"},{"name":"weight-hanging","type":"s"},{"name":"weixin","type":"b"},{"name":"whatsapp","type":"b"},{"name":"whatsapp-square","type":"b"},{"name":"wheelchair","type":"s"},{"name":"whmcs","type":"b"},{"name":"wifi","type":"s"},{"name":"wikipedia-w","type":"b"},{"name":"wind","type":"s"},{"name":"window-close","type":"s"},{"name":"window-close","type":"r"},{"name":"window-maximize","type":"s"},{"name":"window-maximize","type":"r"},{"name":"window-minimize","type":"s"},{"name":"window-minimize","type":"r"},{"name":"window-restore","type":"s"},{"name":"window-restore","type":"r"},{"name":"windows","type":"b"},{"name":"wine-bottle","type":"s"},{"name":"wine-glass","type":"s"},{"name":"wine-glass-alt","type":"s"},{"name":"wix","type":"b"},{"name":"wizards-of-the-coast","type":"b"},{"name":"wodu","type":"b"},{"name":"wolf-pack-battalion","type":"b"},{"name":"won-sign","type":"s"},{"name":"wordpress","type":"b"},{"name":"wordpress-simple","type":"b"},{"name":"wpbeginner","type":"b"},{"name":"wpexplorer","type":"b"},{"name":"wpforms","type":"b"},{"name":"wpressr","type":"b"},{"name":"wrench","type":"s"},{"name":"x-ray","type":"s"},{"name":"xbox","type":"b"},{"name":"xing","type":"b"},{"name":"xing-square","type":"b"},{"name":"y-combinator","type":"b"},{"name":"yahoo","type":"b"},{"name":"yammer","type":"b"},{"name":"yandex","type":"b"},{"name":"yandex-international","type":"b"},{"name":"yarn","type":"b"},{"name":"yelp","type":"b"},{"name":"yen-sign","type":"s"},{"name":"yin-yang","type":"s"},{"name":"yoast","type":"b"},{"name":"youtube","type":"b"},{"name":"youtube-square","type":"b"},{"name":"zhihu","type":"b"}]');
    }
}