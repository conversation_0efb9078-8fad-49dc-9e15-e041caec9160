import { uniqueId } from '../functions';

export class TagsPicker {

    constructor(element) {
        
        if (typeof element == 'undefined') {
            return;
        }

        this.element = $(element);
        this.tags = [];
        //this.type = type;
        this.container = this.element.find('.tags-container');
        this.input = this.element.find('.tags-input');
        this.language = this.element.data('locale');

        this.uniqid = uniqueId();

        this.element
            .attr('data-uniq-id', this.uniqid)
            .prop('data-uniq-id', this.uniqid);

        this.xhr;

        this.load();

        this.events();
    }

    events() {

        let self = this;

        this.input.on('keypress', function() {
            
            if ($(this).val().trim().toLowerCase().length >= 2) {
                self.search($(this).val());
            } else {
                $('#'+self.uniqid+'.tag-search-results').remove();
            }
        });

        this.input.on('keydown', function(e) {

            let v = $(this).val().trim().toLowerCase();

            if(e.key !== 'Enter' || v.length < 3) {    
                return;
            } 

            e.preventDefault();
              
            self.add({
                "name": v
            });

            $(this).val('');
        });

        $(document).on('click', 'div[data-uniq-id="'+self.uniqid+'"] .tag > div:last-child', function() {

            let tagElement = $(this).closest('.tag');

            self.remove({
             //   "id": tagElement.data('id'),
                "name": tagElement.data('name')
            });
        });

        $(document).on('click', 'div[data-uniq-id="'+self.uniqid+'"] .tag-search-results ul li', function() {

            self.add({
              //  "id": $(this).data('id'),
                "name": $(this).data('name')
            });

            self.input.val('');
            self.element.find('.tag-search-results').remove();
        });
    }

    load() {

        let self = this; 
        
        this.input.data('tags').forEach(function(item, index) {
            self.add(item);
        });

        this.checkNoTags();
        this.setData();
    }

    search(query) {

        return;

        $('#'+this.uniqid+'.tag-search-results').remove();

        let self = this,
            xhr = this.xhr;

        if (typeof xhr == 'object') {
            xhr.abort();
        }

        xhr = $.ajax({
            url: config.domain+'/'+config.app+'/functions/module.tagspicker.search',
            type: 'post',
            data: {
                "query": query.trim(),
                "language": this.language
            },
            dataType: 'json',
            success: function(response) {

                if (response.error == 0){

                    let list = [];
                    response.tags.forEach(function(item, index) {

                        if (!self.exists(item.title)) {
                            list.push(`<li data-id="`+item.id+`" data-title="`+item.title+`">`+item.title+`</li>`);
                        }
                    });

                    if (list.length > 0) {
                        self.element.parent().append($(`
                            <div class="tag-search-results" id="`+self.uniqid+`" style="top:`+(self.element.outerHeight() - 1)+`px">
                                <ul class="list-unstyled">
                                    `+list.join('')+`
                                </ul>
                            </div>
                            `)
                        );
                    }
                }
            }
        });
    }

    add(item) {

        let self = this;

        if (item.name.length > 0 && !this.exists(item.name)) {

            this.container.append($(`
                <div class="tag" data-name="`+item.name+`">
                    <div class="row">
                        <div>`+item.name+`</div>
                        <div class="btn-tag-delete"><i class="fa fa-times"></i></div>
                    </div>
                </div>`));
            
            
            this.tags.push({
                'name': item.name,
             //   'type': self.type
            });
        }

        this.checkNoTags();
        this.setData();
    }

    remove(item) 
    {
        this.container.find('.tag[data-name="'+item.name+'"]').remove();

        if (this.exists(item.name)) {
            this.tags.splice(this.getIndexByName(item.name), 1);
        }

        this.checkNoTags();
        this.setData();
    }

    checkNoTags() 
    {
        if (this.container.find('.tag').length == 0) {
            this.container.append($(`
                <div class="no-tags alert alert-warning">
                    There are no tags yet!
                </div>`));

        } else {
            this.container.find('.no-tags').remove();
        }
    }

    exists(name) 
    {
        let b = false;
        name = name.toString().trim().toLowerCase();

        this.tags.forEach(function(item) {
            if (item.name == name) {
                b = true;
                return;
            }
        });

        return b;
    }

    getIndexByName(name) 
    {

        let i = false;
        this.tags.forEach(function(item, index) {
            if (item.name == name) {
                i = index;
            }
        });

        return i;
    }

    setData() 
    {
        this.element.find('.tags-data').val(JSON.stringify(this.tags));
    }
}