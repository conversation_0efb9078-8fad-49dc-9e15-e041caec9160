
export class Attachments {

    constructor(element) {

        if (typeof element == 'undefined') {
            return;
        }

        if (typeof element == 'string') {
            this.element = $(element);
        } else {
            if (typeof element.element != 'undefined') {
                this.element = $(element.element);
            } else {
                return;
            }
        }

        this.hash = Math.floor(Math.random() * 10000);

        this.element.data('hash', this.hash);

        this.events();
    }

    events() {

        let self = this,
            selector = `[data-hash=${self.hash}]`;

        
    }
}