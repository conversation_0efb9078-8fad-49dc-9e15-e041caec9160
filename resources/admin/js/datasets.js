(function (global) {
    if (!global.dataSets) {
        global.dataSets = {};
    }

    function getNested(obj, path) {
        return path.split(".").reduce((o, k) => (o ? o[k] : undefined), obj);
    }

    function compare(a, op, b) {
        switch (op) {
            case "<": return a < b;
            case ">": return a > b;
            case "<=": return a <= b;
            case ">=": return a >= b;
            case "=":
            case "==": return a == b;
            case "!=": return a != b;
            default: return false;
        }
    }

    class DataQuery {
        constructor(dataSetName) {
            this.dataSetName = dataSetName;
            this.customUrl = null;
            this.filters = [];
            this.data = null; // Resolved data: array for fetch(), single item for fetchFirst()
            this._fetchPromise = null;
            this._fetchFirstPromise = null;
            this._emptyCallback = null;
        }

        url(urlPath) {
            this.customUrl = urlPath;
            return this;
        }

        where(attribute, operator, value) {
            this.filters.push({ type: "where", attribute, operator, value });
            return this;
        }

        whereIn(attribute, values) {
            if (typeof values === "string") {
                values = values.split(",").map(v => v.trim());
            }
            this.filters.push({ type: "whereIn", attribute, values });
            return this;
        }

        whereNotIn(attribute, values) {
            if (typeof values === "string") {
                values = values.split(",").map(v => v.trim());
            }
            this.filters.push({ type: "whereNotIn", attribute, values });
            return this;
        }

        buildUrl() {
            let base = this.customUrl
                ? this.customUrl.replace(/\/$/, "")
                : "/admin/dataset/" + this.dataSetName;
            let params = { dataset: this.dataSetName };
            if (this.filters.length > 0) {
                params.filters = JSON.stringify(this.filters);
            }
            return base + "?" + $.param(params);
        }

        mergeData(existing, incoming, indexKey) {
            if (!indexKey) {
                return existing.concat(incoming);
            }
            const merged = [...existing];
            const indexMap = {};
            merged.forEach((item, idx) => {
                if (item[indexKey] !== undefined) {
                    indexMap[item[indexKey]] = idx;
                }
            });
            incoming.forEach((item) => {
                const key = item[indexKey];
                if (key !== undefined && indexMap.hasOwnProperty(key)) {
                    merged[indexMap[key]] = item;
                } else {
                    merged.push(item);
                }
            });
            return merged;
        }

        applyFilters(data) {
            if (!this.filters.length) {
                return data;
            }
            return data.filter((item) => {
                return this.filters.every((filter) => {
                    const val = getNested(item, filter.attribute);
                    if (filter.type === "where") {
                        return compare(val, filter.operator, filter.value);
                    } else if (filter.type === "whereIn") {
                        return filter.values.includes(val);
                    } else if (filter.type === "whereNotIn") {
                        return !filter.values.includes(val);
                    }
                    return true;
                });
            });
        }

        fetch() {
            if (!this._fetchPromise) {
                this._fetchPromise = $.ajax({
                    url: this.buildUrl(),
                    method: "GET",
                    dataType: "json"
                }).then((response) => {
                    let newData = response.data || [];
                    if (typeof newData === "string") {
                        try {
                            newData = JSON.parse(newData);
                        } catch (e) {
                            newData = [];
                        }
                    }
                    const indexKey = response.index_key || null;
                    const existingObj = global.dataSets[this.dataSetName] &&
                        global.dataSets[this.dataSetName].data
                        ? global.dataSets[this.dataSetName]
                        : { data: [] };
                    const merged = this.mergeData(existingObj.data, newData, indexKey);
                    global.dataSets[this.dataSetName] = {
                        name: response.name || this.dataSetName,
                        count: merged.length,
                        index_key: indexKey,
                        data: merged,
                    };
                    this.data = this.applyFilters(merged);
                    return this.data;
                });
            }
            return this;
        }

        fetchFirst() {
            if (!this._fetchFirstPromise) {
                this._fetchFirstPromise = this.fetch()._fetchPromise.then((data) => {
                    const first = (Array.isArray(data) && data.length > 0) ? data[0] : null;
                    this.data = first;
                    return first;
                });
            }
            return this;
        }

        get() {
            const ds = global.dataSets[this.dataSetName];
            const data = ds && ds.data && Array.isArray(ds.data) ? ds.data : [];
            return this.applyFilters(data);
        }

        getFirst() {
            const data = this.get();
            return (Array.isArray(data) && data.length > 0) ? data[0] : null;
        }

        getOrFetch() {
            const ds = global.dataSets[this.dataSetName];
            const data = ds && ds.data ? ds.data : [];
            const filtered = this.applyFilters(data);
            if (filtered.length > 0) {
                this.data = filtered;
                this._fetchPromise = $.Deferred().resolve(this.data).promise();
                return this;
            }
            return this.fetch();
        }

        getOrFetchFirst() {
            const ds = global.dataSets[this.dataSetName];
            const data = ds && ds.data ? ds.data : [];
            const filtered = this.applyFilters(data);
            if (filtered.length > 0) {
                const first = filtered[0];
                this.data = first;
                this._fetchFirstPromise = $.Deferred().resolve(first).promise();
                return this;
            }
            return this.fetchFirst();
        }

        done(onFulfilled, onRejected) {
            let promise;
            if (this._fetchFirstPromise) {
                promise = this._fetchFirstPromise;
            } else if (this._fetchPromise) {
                promise = this._fetchPromise.then((data) => {
                    return (Array.isArray(data) && data.length > 0) ? data[0] : null;
                });
            } else {
                promise = this.fetch()._fetchPromise.then((data) => {
                    return (Array.isArray(data) && data.length > 0) ? data[0] : null;
                });
            }
            promise.then((result) => {
                if (result === null) {
                    if (typeof this._emptyCallback === "function") {
                        this._emptyCallback(result);
                    }
                } else {
                    if (onFulfilled) {
                        onFulfilled(result);
                    }
                }
                this.data = result;
            }, onRejected);
            return this;
        }

        catch(onRejected) {
            let promise;
            if (this._fetchFirstPromise) {
                promise = this._fetchFirstPromise;
            } else if (this._fetchPromise) {
                promise = this._fetchPromise.then((data) => {
                    return (Array.isArray(data) && data.length > 0) ? data[0] : null;
                });
            } else {
                promise = this.fetch()._fetchPromise.then((data) => {
                    return (Array.isArray(data) && data.length > 0) ? data[0] : null;
                });
            }
            promise.catch(onRejected);
            return this;
        }

        empty(callback) {
            this._emptyCallback = callback;
            return this;
        }

        emptyOrCatch(callback) {
            let promise;
            if (this._fetchFirstPromise) {
                promise = this._fetchFirstPromise;
            } else if (this._fetchPromise) {
                promise = this._fetchPromise.then((data) => {
                    return (Array.isArray(data) && data.length > 0) ? data[0] : null;
                });
            } else {
                promise = this.fetch()._fetchPromise.then((data) => {
                    return (Array.isArray(data) && data.length > 0) ? data[0] : null;
                });
            }
            promise.then((result) => {
                if (result === null && typeof callback === "function") {
                    callback(null);
                }
            }).catch((error) => {
                if (typeof callback === "function") {
                    callback(error);
                }
            });
            return this;
        }
    }

    global._data = {
        set: function (dataSetName) {
            return new DataQuery(dataSetName);
        },
        get: function (dataSetName) {
            const ds = global.dataSets[dataSetName];
            return ds && ds.data && Array.isArray(ds.data) ? ds.data : [];
        }
    };
})(window);