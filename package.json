{"private": true, "type": "module", "scripts": {"dev": "npx cross-env SASS_SILENCE_DEPRECATION_WARNINGS=true vite", "build": "vite build", "build:prod": "vite build", "test-env": "npx cross-env SASS_SILENCE_DEPRECATION_WARNINGS=true node -e \"console.log(process.env.SASS_SILENCE_DEPRECATION_WARNINGS)\""}, "devDependencies": {"@babel/core": "^7.25.8", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-transform-modules-umd": "^7.25.7", "@babel/plugin-transform-runtime": "^7.25.7", "@babel/preset-env": "^7.23.6", "@popperjs/core": "^2.11.8", "@rollup/plugin-inject": "^5.0.5", "@types/glob": "^8.1.0", "autoprefixer": "^10.4.20", "axios": "^1.7.4", "babel-plugin-add-module-exports": "^1.0.4", "babel-preset-es2015": "^6.24.1", "bootstrap": "^5.3.3", "chokidar": "^4.0.3", "concurrently": "^9.0.1", "cross-env": "^7.0.3", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.47", "purgecss": "^6.0.0", "rollup-plugin-visualizer": "^5.11.0", "sass": "^1.83.4", "tailwindcss": "^3.4.13", "terser": "^5.34.1", "vite": "^5.4.10", "vite-plugin-javascript-obfuscator": "^3.1.0", "vite-plugin-vendor": "^1.3.3"}, "dependencies": {"@alpinejs/collapse": "^3.14.8", "@babel/cli": "^7.23.4", "@babel/node": "^7.22.19", "@babel/traverse": "^7.25.7", "@fancyapps/fancybox": "^3.5.7", "@fortawesome/fontawesome-free": "^6.5.1", "@originjs/vite-plugin-commonjs": "^1.0.3", "@vitejs/plugin-legacy": "^5.4.2", "alpinejs": "^3.14.8", "bootstrap-datepicker": "^1.10.0", "bootstrap-select": "1.14.0-beta3", "dotenv": "^16.3.1", "glob": "^11.0.0", "jquery": "^3.7.1", "jquery-ui": "^1.14.0", "jquery-ui-dist": "^1.13.3", "lodash": "^4.17.21", "owl.carousel": "^2.3.4", "puppeteer": "^23.5.2", "remove": "^0.1.5", "tinymce": "^7.4.1", "vite-plugin-babel": "^1.2.0", "vite-plugin-glob": "^0.3.2", "vite-plugin-sass-glob-import": "^4.0.0", "vite-plugin-static-copy": "^0.17.0"}}