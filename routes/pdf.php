<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;

//if (! Schema::hasTable('slugs')) {
//    return;
//}

Route::prefix('/pdf/')
    ->name('pdf.')
    ->group(function () {

    Cache::rememberForever('route.pdf', fn () => collect(glob(resource_path('pdf/views/documents/') . '*.blade.php'))
        ->map(fn ($path) => Str::remove('.blade', pathinfo($path)['filename'])))
        ->each(function ($document) {
//        Route::get(strtolower($document) . '/{model?}', [
//            "App\Http\Controllers\PDF\\" . $document . "PDFController",
//            'index',
//        ])->name(strtolower($document));
    });
});
