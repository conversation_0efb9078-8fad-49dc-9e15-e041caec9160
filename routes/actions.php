<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;

//if (! Schema::hasTable('slugs')) {
//    return;
//}

Route::prefix('/actions')
    ->name('action.')
    ->group(function () {

    Cache::rememberForever('route.actions', function () {

        return collect(glob(app_path() . '/Http/Controllers/Actions/*ActionController.php'))
            ->map(function ($item) {
                if (preg_match('/.*Actions(.{1})(.*)ActionController.*$/', $item, $m)) {
                    return $m[2];
                }
            })
            ->filter();

    })->each(function ($controllerName) {

        $controller = "App\\Http\\Controllers\\Actions\\" . $controllerName . "ActionController";

        $reflection = new ReflectionClass($controller);

        $methods = [];
        foreach ($reflection->getMethods() as $method) {

            if (! in_array($method?->getReturnType()?->getName(), [
                'Illuminate\Http\JsonResponse',
                'Illuminate\Http\JsonResource',
            ]) || ! preg_match('/^(get|post)(.*)$/', $method->name, $m)) {
                continue;
            }

            $methods[] = [
                'reflection' => $method,
                'requestMethod' => $m[1],
                'requestName' => $m[2],
            ];
        }

        if (count($methods) == 0) {
        return;
        }

        Route::prefix('/' . strtolower($controllerName))
            ->name(strtolower($controllerName) . '.')
            ->controller($controller)
            ->group(function () use ($methods) {

            foreach ($methods as $methodData) {

                $method = $methodData['reflection'];

                $authGuard = false;
                foreach ($method->getParameters() as $param) {
                    if ($param->name == 'auth') {
                        $authGuard = $param->getDefaultValue();
                        break;
                    }
                }

                $route = Route::{$methodData['requestMethod']}('/' . Str::snake($methodData['requestName']), $method->name);
                $route->name(Str::snake($methodData['requestName']));

                if ($authGuard !== false) {

                    $route->middleware(['auth:' . $authGuard]);
                }
            }
        });
    });

    Route::fallback(function () {
        return response()->json(['error' => 'Endpoint does not exist.'], 404);
    });
});
