<?php

use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\DataSetController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;

if (! function_exists('usesTrait')) {
    require_once __DIR__ . "/../app/helpers.php";
}

//if (! Schema::hasTable('slugs')) {
//    return;
//}

// niet in auth middleware?
Route::get('/previews/buttonmanager', function () {
    return view('portal.previews.buttonmanager');
});

Route::prefix('/admin')
    ->name('admin.')
    ->group(function () {

    Route::controller(AuthController::class)
        ->name('auth.')
        ->group(function () {
            Route::get('', 'index')->name('index');
            Route::post('/login', 'login')->name('login');
            Route::get('/logout', 'logout')->name('logout')->middleware(['auth:admin']);
        }
    );

    Route::group(['middleware' => 'auth:admin'], function () {

        Route::controller(DataSetController::class)
            ->name('dataset.')
            ->prefix('/dataset')
            ->group(function () {
                Route::get('{dataset?}', 'index')->name('index');
            })
        ;

        foreach (config('modules') as $name => $module) {
            $prefix = ($module['routes_prefix'] ?? null);

            $module['routes_prefix'] = $prefix === false
                ? ''
                : (is_null($prefix)
                    ? '/' . $name
                    : $prefix
                )
            ;

            $controller = $module['controller'] ?? $name;

            $controllerName = 'App\Http\Controllers\Admin\\' . Str::ucfirst($controller) . 'Controller';

            Route::controller($controllerName)
                ->name($name . '.')
                ->group(function () use ($module, $name) {

                    $model = Str::camel(isset($module['model'])
                        ? $module['model']
                        : Str::singular($name)
                    );

                    $prefix = $module['routes_prefix'] ?? '';

                    if (($module['routes_override_default'] ?? false) === false) {
                        Route::prefix($prefix)->group(function () use ($model) {
                            Route::get('', 'index')->name('index');
                            Route::post('', 'store')->name('store');
                            Route::get('/create', 'create')->name('create');
                            Route::get('/{' . $model . '}/edit', 'edit')->name('edit');
                            Route::get('/{' . $model . '}/destroy', 'destroy')->name('destroy');
                        });
                    }

                    if (Arr::get($module, 'routes.filter') === null &&
                        isset($module['model']) &&
                        ($module['is_filterable'] ?? false) &&
                        (usesTrait('App\Traits\IsFilterable', 'App\\Models\\' . $module['model']))) {
                        Route::prefix($prefix)->group(function () {
                            Route::get('filter', 'filter')->name('filter');
                            Route::post('filter', 'filter')->name('filter');
                        });
                    }

                    if (Arr::get($module, 'routes.sort') === null &&
                        isset($module['model']) &&
                        ($module['is_sortable'] ?? false) &&
                        (usesTrait('App\Traits\IsSortable', 'App\\Models\\' . $module['model']))) {
                        Route::prefix($prefix)->group(function () {
                            Route::get('/sort', 'sort')->name('sort');
                        });
                    }

                    foreach (($module['routes'] ?? []) as $routeName => $routeData) {

                        if (! is_array($routeData)) {
                            // DEPRECATED implementation
                            $routeName = null;
                            if (strpos($routeData, '|') !== false) {
                                [$routeName, $routeData] = explode('|', $routeData);
                            }

                            [$method, $routeData] = explode(':', $routeData);
                            [$requestMethod, $routeData] = explode('.', $routeData);

                            Route::$requestMethod($prefix . $routeData, $method)->name($routeName ?? $method);

                        } else {

                            // PROPER implementation
                            $routeName = $routeData['name'] ?? $routeName;

                            $method = $routeData['method'] ?? 'get';

                            // set action if not set
                            $routeData['action'] ??= Str::camel(collect(explode('.', $routeName))
                                ->map(function ($part) {
                                    return ucfirst($part);
                                })
                                ->join('')
                            );

                            // check if controller exists and set action as array
                            if (isset($routeData['controller']) &&
                                class_exists($controllerName = 'App\\Http\\Controllers\\Admin\\' . Str::ucfirst(rtrim($routeData['controller'], 'Controller')) . "Controller")) {

                                $routeData['action'] = [
                                    ($controllerName),
                                    $routeData['action'],
                                ];
                            }

                            $method = is_array($method)
                                ? $method
                                : [$method]
                            ;

                            $prefix = $routeData['prefix'] ?? $module['routes_prefix'] ?? '';

                            $prefix = $prefix ? '' : $prefix;

                            Route::prefix($prefix)->group(function () use ($method, $routeData, $routeName) {

                                $route = Route::match($method, $routeData['path'], $routeData['action'])->name($routeName);

                                if (isset($routeData['where']) &&
                                    is_array($routeData['where'])) {
                                    $route->where($routeData['where']);
                                }
                            });
                        }
                    }
                }
            );
        }
    });

    Route::fallback([AuthController::class, 'index']);
});
