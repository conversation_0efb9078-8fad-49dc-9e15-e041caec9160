<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\MorphTo;

class Menu extends BaseModel
{
    protected $fillable = [
        'systemname',
        'menuable_id',
        'menuable_type',
        'sort',
    ];
    protected $casts = [
        'id' => 'integer',
        'systemname' => 'string',
        'menuable_id' => 'integer',
        'menuable_type' => 'string',
        'sort' => 'integer',
        'updated_at' => 'datetime:d-m-Y H:i:s',
        'created_at' => 'datetime:d-m-Y H:i:s',
    ];

    public function menuable(): morphTo
    {
        return $this->morphTo();
    }
}
