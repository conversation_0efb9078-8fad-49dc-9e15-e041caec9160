<?php

namespace App\Models;

use App\Enums\Gender;
use Closure;
use Illuminate\Auth\Authenticatable as AuthAuthenticatable;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Permission\Traits\HasRoles;

class Admin extends BaseModel implements Authenticatable
{
    use AuthAuthenticatable,
        HasPermissions,
        HasRoles
    ;

    protected $fillable = [
        'exact_id',
        'locale_id',
        'country_id',

        'firstname',
        'lastname',
        'username',
        'gender',
        'phonenumber',
        'mobile',
        'email',
        'is_backoffice_manager',
        'is_active',
        'in_picture',
        'deleted_at',
    ];

    protected $translatable = [
        'greeting',
    ];

    protected $casts = [
        'gender' => Gender::class,
        'password' => 'hashed',
    ];

    protected $hidden = [
        'password',
    ];

    protected $guarded = [
        'password',
    ];

    public function autoloadRelationsUsing(Closure $callback, $context = null)
    {
        $this->autoloadRelationsCallback = null;

        foreach ($this->relations as $key => $value) {
            $this->propagateRelationAutoloadCallbackToRelation($key, $value, $context);
        }

        return $this;
    }

    public function getFullNameAttribute($key): string
    {
        return ucwords(implode(' ', [$this->firstname, $this->lastname]));
    }

    public function locale(): HasOne
    {
        return $this->hasOne(Locale::class, 'id');
    }

    public function image(): MorphOne
    {
        return $this->morphOne(Image::class, 'imageable')
            ->latest();
    }

    public function settings(): MorphMany
    {
        return $this->morphMany(Settings::class, 'settingable');
    }

    // public function companies(): HasMany
    // {
    //     return $this->Hasmany(Company::class , 'admin_backoffice_id');
    // }
}
