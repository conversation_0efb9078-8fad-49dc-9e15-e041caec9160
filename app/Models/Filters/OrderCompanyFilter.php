<?php

namespace App\Models\Filters;

use App\Models\Company;
use Lacodix\LaravelModelFilter\Enums\FilterMode;
use Lacod<PERSON>\LaravelModelFilter\Filters\SelectFilter;
use Str;

class OrderCompanyFilter extends SelectFilter
{
    public FilterMode $mode = FilterMode::CONTAINS;

    protected string $field = 'company_id';
    protected string $titleColumn = 'name';
    protected string $title = 'Company';

    public function options(): array
    {
        return Company::query()
            ->whereHas(Str::camel(Str::plural($this->model->getMorphClass())))
            ->orderBy('name')
            ->select('id', 'name')
            ->pluck('name', 'id')
            ->toArray()
        ;
    }
}
