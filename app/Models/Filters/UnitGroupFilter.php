<?php

namespace App\Models\Filters;

use App\Models\UnitGroup;
use App\Models\UnitGroupUnit;
use Illuminate\Database\Eloquent\Builder;
use Lacodix\LaravelModelFilter\Enums\FilterMode;
use Lacodix\LaravelModelFilter\Filters\SelectFilter;

class UnitGroupFilter extends SelectFilter
{
    public FilterMode $mode = FilterMode::CONTAINS;

    protected string $field = 'unit_group_id';

    protected string $title = 'Groups';

    public function options(): array
    {
        static $cached;

        if ($cached !== null) {
            return $cached;
        }

        // Get currently selected filter value(s)
        $selected = (array) ($this->values[$this->field] ?? []);

        // Get used item group IDs
        $related = UnitGroupUnit::query()
            ->select($this->getQualifiedField())
            ->distinct()
            ->pluck($this->getQualifiedField())
            ->toArray()
        ;

        // Fetch options for these IDs
        return $cached = UnitGroup::query()
            ->whereIn('id', array_unique(array_merge($related, $selected)))
            ->pluck('id', 'systemname')
            ->toArray()
        ;
    }

    public function apply(Builder $query): Builder
    {
        return $query->whereHas('groups', function (Builder $q) {
            $q->whereIn(
                $this->getQualifiedField(),
                $this->values[$this->field] ?: []
            );
        });
    }
}
