<?php

namespace App\Models\Filters;

use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Builder;
use <PERSON>od<PERSON>\LaravelModelFilter\Enums\FilterMode;
use Lacodix\LaravelModelFilter\Filters\SelectFilter;

class WarehouseFilter extends SelectFilter
{
    public FilterMode $mode = FilterMode::CONTAINS;

    protected string $field = 'warehouse_id';
    protected string $title = 'Warehouse';

    public static ?array $optionsContent;

    public function options(): array
    {
        static $cached;

        if ($cached !== null) {
            return $cached;
        }

        // Get currently selected filter value(s)
        $selected = (array) ($this->values[$this->queryName()] ?? []);

        if (! method_exists($this->model(), 'warehouse')) {
            return [];
        }

        $related = $this->model()::query()
            ->whereHas('warehouse')
            ->with('warehouse:warehouses.id,warehouses.systemname')
            ->get()
            ->pluck('warehouse')
            ->filter()
            ->unique('id')
            ->pluck('id')
            ->toArray()
        ;

        // Fetch options for these IDs
        return $cached = Warehouse::query()
            ->whereIn('id', array_unique(array_merge($related, $selected)))
            ->pluck('id', 'systemname')
            ->toArray()
        ;
    }

    public function apply(Builder $query): Builder
    {
        $values = $this->values[$this->field] ?? null;

        $query->whereHas('warehouse', function (Builder $query) use ($values) {
            $query->whereIn('warehouses.id', $values);
        });

        return $query;
    }
}
