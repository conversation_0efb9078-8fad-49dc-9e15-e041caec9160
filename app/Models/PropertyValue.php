<?php

namespace App\Models;

use App\Traits\HasTranslations;
use App\Traits\IsLockable;
use App\Traits\IsSortable;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class PropertyValue extends BaseModel
{
    use Cachable;
    use HasTranslations;
    use IsLockable;
    use IsSortable;

    protected $table = 'properties_values';

    protected $fillable = [
        'property_id',
        'systemname',
        'value',
        'sort',
    ];

    protected $translatable = [
        'name',
        'content',
    ];

    protected $sortable = [
        'systemname',
    ];

    protected $lockable = [
        'property_id',
        'value',
    ];

    protected $lockableRelations = [
        'products',
        'itemUnitRelations',
        'itemVariantUnitRelations',
    ];

    public function property(): BelongsTo
    {
        return $this->belongsTo(
            Property::class
        );
    }

    public function products(): MorphToMany
    {
        return $this->morphedByMany(
            Product::class,
            'relatable',
            'properties_values_relations'
        );
    }

    public function itemUnitRelations(): MorphMany
    {
        return $this->morphMany(
            ItemUnitRelation::class,
            'relatable',
            'relatable_type',
            'relatable_id'
        );
    }

    public function itemVariantUnitRelations(): MorphMany
    {
        return $this->morphMany(
            ItemVariantUnitRelation::class,
            'relatable',
            'relatable_type',
            'relatable_id'
        );
    }
}
