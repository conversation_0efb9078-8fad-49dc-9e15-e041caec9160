<?php

namespace App\Models;

use App\Traits\IsLockable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Znck\Eloquent\Traits\BelongsToThrough as TraitsBelongsToThrough;

class ItemVariantUnitRelation extends BaseModel
{
    use IsLockable;

    use TraitsBelongsToThrough;

    protected $table = 'items_variants_units_relations';

    protected $fillable = [
        'id',
        'item_variant_unit_id',
        'relatable_type',
        'relatable_id',
        'value',
    ];

    public function unit(): BelongsTo
    {
        return $this->belongsTo(
            ItemVariantUnit::class,
            'item_variant_unit_id'
        );
    }

    public function relatable(): MorphTo
    {
        return $this->morphTo();
    }
}
