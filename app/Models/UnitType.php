<?php

namespace App\Models;

use App\Enums\UnitTypeMode;
use App\Models\Filters\UnitTypeModeFilter;
use App\Traits\HasTranslations;
use App\Traits\IsFilterable;
use App\Traits\IsLockable;
use App\Traits\IsSortable;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class UnitType extends BaseModel
{
    use Cachable;
    use HasTranslations;
    use IsFilterable;
    use IsLockable;
    use IsSortable;

    protected $table = 'units_types';

    protected $fillable = [
        'id',
        'unit_base_id',
        'systemname',
        'mode',
    ];

    protected $casts = [
        'mode' => UnitTypeMode::class,
    ];

    protected $translatable = [
        'name',
    ];

    protected $lockable = [
        'unit_base_id',
        'mode',
    ];

    protected $lockableRelations = [
        'units.itemUnits',
        'units.itemUnitRelations',
        'units.itemVariantUnits',
        'units.itemUnitVariantRelations',
        'units.propertiesBySystemContextUnit',
        'units.propertiesBySystemContextUnitCompositionalUnit',
        // 'units.isUnitBaseForUnitType'
    ];

    protected $sortable = [
        'systemname',
        'mode',
        'units_count',
    ];

    protected array $filters = [
        UnitTypeModeFilter::class,
    ];

    public function units(): HasMany
    {
        return $this->hasMany(
            Unit::class,
            'unit_type_id'
        );
    }

    public function baseUnit(): BelongsTo
    {
        return $this->belongsTo(
            Unit::class,
            'unit_base_id', // FK on unit_types → units.id
            'id'
        );
    }

    // public function baseUnit(): HasOne
    // {
    //     return $this->hasOne(
    //         Unit::class,
    //         'id',
    //         'unit_base_id'
    //     );
    // }
}
