<?php

namespace App\Models;

use App\Traits\GetStatusDot;
use App\Traits\HasMenus;
use App\Traits\HasProperties;
use App\Traits\HasPropertyValues;
use App\Traits\HasTranslations;
use App\Traits\Pageable;
use App\Traits\Sluggable;
use App\Traits\Treeable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Category extends BaseModel
{
    use GetStatusDot,
        HasMenus,
        HasProperties,

        HasTranslations,

        Pageable,
        Sluggable,
        Treeable;
    use HasPropertyValues;

  //  public $table = 'categories';

    protected $fillable = [
        'id',
        'systemname',

        'is_highlight',
        'sort',
        'sort_menu',
        'sort_menu_top',
        'sort_menu_1',
        'sort_menu_2',
        'sort_menu_footer',
        'sort_menu_dashboard',
        'has_card_image',
    ];

    protected $translatable = [];

    protected $casts = [
        'sort' => 'integer',
        'sort_menu' => 'integer',
        'sort_menu_top' => 'integer',
        'sort_menu_1' => 'integer',
        'sort_menu_2' => 'integer',
        'sort_menu_footer' => 'integer',
        'sort_menu_dashboard' => 'integer',
        'has_card_image' => 'boolean',
    ];

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'products_categories', 'category_id', 'product_id');
    }

    // TODO: freek
    public function menus(): MorphMany
    {
        return $this->MorphMany(Menu::class, 'menuable');
    }
}
