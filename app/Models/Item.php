<?php

namespace App\Models;

use App\Enums\ItemType;
use App\Models\Filters\ItemGroupFilter;
use App\Models\Filters\ItemTypeFilter;
use App\Models\Filters\ItemUnitGroupSalesFilter;
use App\Models\Filters\ItemUnitGroupWarehouseFilter;
use App\Models\Filters\SupplierFilter;
use App\Traits\HasLedgerAccounts;
use App\Traits\HasPropertyValues;
use App\Traits\IsFilterable;
use App\Traits\IsLockable;
use App\Traits\IsSortable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Item extends BaseModel
{
    use HasLedgerAccounts;
    use HasPropertyValues;
    use IsFilterable;

    use IsLockable;
    use IsSortable;

    protected $table = 'items';

    protected $fillable = [
        'id',
        'item_group_id',
        'item_related_id',
        'exact_id',
        'company_id',
        'code',
        'systemname',
        'type',
        'is_custom',
    ];

    protected $casts = [
        'type' => ItemType::class,
    ];

    protected $sortable = [
        'systemname',
        'code',
        'group.systemname',
        'type',
        'products_count',
        'related_by_count',
        'related.systemname',
        'units.warehouse.groupunit.systemname' => 'units[unit_group_id=1].unitGroupUnit.unit.systemname',
        'units.sales.groupunit.systemname' => 'units[unit_group_id=2].unitGroupUnit.unit.systemname',
    ];

    protected $lockable = [
        'item_related_id',
        'company_id',
    ];

    protected $lockableRelations = [
        'relatedBy',
        'variants.relatedBy',
        'variants.warehouseStock',
        'products',
    ];

    protected $filters = [
        ItemTypeFilter::class,
        ItemGroupFilter::class,
        ItemUnitGroupWarehouseFilter::class,
        ItemUnitGroupSalesFilter::class,
        SupplierFilter::class,
    ];

    public $systemlabelAttributes = [
        'code',
        'systemname',
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(
            Company::class,
            'company_id'
        );
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(
            ItemGroup::class,
            'item_group_id'
        );
    }

    public function related(): BelongsTo
    {
        return $this->belongsTo(
            Item::class,
            'item_related_id'
        );
    }

    public function relatedBy(): HasMany
    {
        return $this->hasMany(
            Item::class,
            'item_related_id',
            'id'
        );
    }

    public function units(): HasMany
    {
        return $this->hasMany(
            ItemUnit::class,
            'item_id'
        );
    }

    public function variants(): HasMany
    {
        return $this->hasMany(
            ItemVariant::class,
            'item_id'
        );
    }

    public function rules(): HasMany
    {
        return $this->hasMany(
            ItemRule::class,
            'item_id'
        );
    }

    public function products(): HasMany
    {
        return $this->hasMany(
            Product::class,
            'item_id'
        );
    }

    public function getTypeAttribute()
    {
        if (is_null($this->related_item_id)) {
            return ItemType::tryFrom($this->attributes['type'] ?? ItemType::DEFAULT->value);
        }

        return $this->relatedItem
            ? $this->relatedItem->type
            : null
        ;
    }
}
