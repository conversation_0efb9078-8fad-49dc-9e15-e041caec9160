<?php

namespace App\Models;

use App\Enums\Gender;
use App\Enums\User\Type;
use App\Helpers\Temp;
use App\Mail\ResetUserPassword;
use App\Mail\UserActivation;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;

class User extends BaseModel implements Authenticatable
{
    use \Illuminate\Auth\Authenticatable;

    protected $fillable = [

        'locale_id',
        'country_id',
        'salesforce_id',
        'exact_id',

        'gender',
        'firstname',
        'lastname',

        'phonenumber',
        'phone_country_id',
        'email',
        'can_login',
        'is_verified',

        'token',
        'type',
        'token_expires_at',
        'activation_sent_at',
        'deleted_at',
        'password',
    ];

    protected $casts = [
        'gender' => Gender::class,
        'password' => 'hashed',
        'token' => 'string',
        'type' => Type::class,
    ];

    protected $hidden = [
        'password',
    ];

    protected $guarded = [
        'password',
    ];

    public function companies(): BelongsToMany
    {
        return $this->BelongsToMany(
            Company::class,
            'companies_users'
        );
    }

    public static function isLoggedIn()
    {

        return session()->has('user_id') && session()->has('company_id')
            ? true
            : false
        ;
    }

    public function sentActivation()
    {
        $this->update([
            'token' => hash('sha256', $this->id),
            'activation_sent_at' => now(),
            'type' => Type::ACTIVATION,
            'token_expires_at' => now()->addDays(1),
        ]);

        // Mail::send(new UserActivation($this));

    }

    public function resetPassword()
    {
        $this->update([
            'token' => hash('sha256', $this->id),
            'type' => Type::PASSWORD,
            'token_expires_at' => now()->addDays(1),
            'password' => null,
        ]);

        // Mail::send(new ResetUserPassword($this));
    }

    public function savePassword($args = [])
    {
        $this->update([
            'token' => null,
            'type' => null,
            'token_expires_at' => null,
            'password' => Hash::make($args['password']),
        ]);
    }

    public function activate($args = [])
    {
        $this->update([
            'token' => null,
            'is_verified' => true,
            'password' => Hash::make($args['password']),
            'type' => null,
            'token_expires_at' => null,
        ]);

    }

    public function getFullName()
    {

        if ($this->lastname === "") {
            return ucfirst($this->firstname);
        }

        $name = implode(' ', [ucfirst($this->firstname), ucfirst($this->lastname)]);

        return $name;
    }

    public static function login(
        string $emailaddress,
        string $password,
        string $gateway = 'portal'
    ) {

        if (($user = self::where([
            "email" => $emailaddress,
            "is_verified" => true,
            'can_login' => 1,
        ])->first()) === false || $user === null) {
            return false;
        }

        if ($gateway == 'portal' &&
            ! Hash::check($password, $user->password)
            ) {
            return false;
        }

        session()->put([
            'user_id' => $user->id,
            'user' => $user,
            'locale' => $user->language,
        ]);

        return $user;
    }

    public function isVerified()
    {
        return $this->is_verified;
    }

    public static function empty(): void
    {
        foreach ([
            'user_id',
            'user',
            'locale',
        ] as $k) {
            if (session()->has($k)) {
                session()->forget($k);
            }
        }
    }

    public function formatPhone()
    {
        $country = Country::where(['id' => $this->phone_country_id ?? 151])->first();

        return [];

        // return Temp::formatPhone([
        //     'number' => $this->phonenumber,
        //     'country' => $country->code,
        //     'format' => 'user'
        // ]);
    }
}
