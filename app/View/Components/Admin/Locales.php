<?php

namespace App\View\Components\Admin;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Locales extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(
        public $toggle = null,
        public string $class = '',
    ) {

        dd('COMPONENT LOCALES');
        if (is_null($this->toggle)) {
            $this->toggle = true;
        }

    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View
    {
        return view('admin::components.locales');
    }
}
