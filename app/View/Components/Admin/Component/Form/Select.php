<?php

namespace App\View\Components\Admin\Component\Form;

use App\Enums\FormSelectOptionsPosition;
use App\Services\FormService;
use App\Traits\Blade\HandlesValidationErrors;
use BackedEnum;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Str;
use UnitEnum;

class Select extends Component
{
    use HandlesValidationErrors;

    public bool $hasSingleOptionSelected = false;

    public function __construct(
        public null|array|Collection|Model $bind = null,
        public ?string $locale,
        public string $name,
        public mixed $default = null,
        public bool $floating = false,
        bool $showErrors = true,
        public ?bool $isLocked = null,

        public string|array|Collection|BackedEnum|UnitEnum $options = [],
        public string|array|Collection|BackedEnum|UnitEnum|null $optionsExtra = null,
        public string|FormSelectOptionsPosition $optionsExtraPosition = FormSelectOptionsPosition::DEFAULT,

        // :optionsExtra="[ ['variant', 'Variant specific'] ]"
        //         optionsExtraPosition="start"
        public ?string $optionValueAttribute = 'id',
        public ?string $optionTextAttribute = 'systemname',
        public string $optionValuePrefix = '',

        public ?string $valueAttribute = null,

        public mixed $selected = null,
        public mixed $optionsDisabled = [],

        public bool $multiple = false,
        public ?string $placeholder = null,

        public bool|int|string $col = false,

        public bool $singleOptionSelected = false,
        public string $singleOptionSelectedElement = 'disabled' // 'disabled', 'text'
    ) {
        $this->showErrors = $showErrors;
        $this->floating = $floating && ! $multiple;

        $this->name = app(FormService::class)->getInputName($this->name, $this->locale);

        $this->setIsLocked();

        if (is_string($optionsExtraPosition)) {
            $this->optionsExtraPosition = defined(FormSelectOptionsPosition::class . '::' . $optionsExtraPosition)
                ? constant(FormSelectOptionsPosition::class . '::' . $optionsExtraPosition)
                : FormSelectOptionsPosition::DEFAULT
            ;
        }

        // normalize $optionsDisabled to array
        if (is_string($this->optionsDisabled)) {
            if (preg_match('/^\[.*\]$/', $this->optionsDisabled)) {
                $this->optionsDisabled = json_decode($this->optionsDisabled, true);
            } else {
                $this->optionsDisabled = explode(',', $this->optionsDisabled);
            }
        }

        if ($this->optionsDisabled instanceof Arrayable) {
            $this->optionsDisabled = $this->optionsDisabled->toArray();
        }

        $this->normalizeOptions();
        $this->setSelected($this->bind);
        $this->normalizeOptionsExtra();
        $this->setOptions();
        $this->mixAllOptions();
        $this->setSingleOptionSelected();
    }

    private function setSingleOptionSelected(): void
    {
        if (! $this->singleOptionSelected ||
            ! is_array($this->options) ||
            count($this->options) != 1 ||
            ! is_array($this->selected) ||
            count(array_filter(array_values(Arr::flatten($this->selected)))) > 0) {
            return;
        }

        $this->selected = [
            $this->options[0][$this->optionValueAttribute] ?? null,
        ];

        $this->hasSingleOptionSelected = true;

        // dd($this->selected);
        // dd($this->options);
    }

    private function normalizeOptions(): void
    {
        if (is_null($this->options)) {
            $this->optionValueAttribute = null;
            $this->optionTextAttribute = null;

            return;
        }

        // check if options is option elements string
        if ((
                is_string($this->options) &&
                preg_match_all('/<option.*>.*<\/option>/mis', $this->options)
            ) || (
                is_array($this->options) &&
                is_string(current($this->options)) &&
                preg_match_all('/<option.*>.*<\/option>/mis', current($this->options))
            )) {

            $this->optionValueAttribute = null;
            $this->optionTextAttribute = null;

            return;
        }

        // normalize Enum to array
        if (is_string($this->options) &&
            strpos($this->options, '\\Enums\\') !== false &&
            class_exists($this->options)) {
            $this->options = $this->options::options();
            $this->optionValueAttribute = 'value';
            $this->optionTextAttribute = 'description';
        }

        // normalize options to array
        if (! is_array($this->options) && (
            $this->options instanceof Collection ||
            $this->options instanceof Arrayable)) {
            $this->options = $this->options->toArray();
        }

        // transform none-associative array
        if (is_array($this->options) &&
            Arr::isAssoc($this->options)) {

            $this->options = $this->transformOptionsRecursive(
                $this->options,
                $this->optionValueAttribute,
                $this->optionTextAttribute
            );

         //   $this->optionValueAttribute = 'value';
          //  $this->optionTextAttribute = 'title';
            return;
        }

        // return if optiontext is set
        if ($this->optionTextAttribute !== 'systemname') {
            return;
        }

        // try to find a suitable optiontext
        $current = current($this->options);
        foreach ([
            'systemlabel',
            'systemname',
            'name',
            'value',
        ] as $key) {
            if (isset($current[$key])) {
                $this->optionTextAttribute = $key;
                break;
            }
        }
    }

    private function setSelected($bind)
    {
        $inputName = Str::bracketsToDots(Str::before($this->valueAttribute ?? $this->name, '[]'));

        $this->selected ??= app(FormService::class)->getBoundValue(
            $bind,
            $inputName
        );

        // set default selected
        if (! is_null($this->default) &&
            is_null($this->selected)) {
            $this->selected = $this->default;
        }

        if ($this->selected instanceof BackedEnum ||
            $this->selected instanceof UnitEnum) {
            $this->selected = $this->selected->value;
        }

        if ($this->selected instanceof Arrayable) {
            $this->selected = $this->selected->toArray();
        }

        if (! is_array($this->selected)) {
            $this->selected = [$this->selected];
        }

        $this->selected = old(Str::bracketsToDots(Str::before($this->name, '[]')), $this->selected);

        if (! is_array($this->selected)) {
            $this->selected = [$this->selected];
        }

        if ($this->singleOptionSelected) {

        }
    }

    private function mixAllOptions(): void
    {
        if (is_null($this->optionsExtra)) {
        return;
        }

        if ($this->optionsExtraPosition == FormSelectOptionsPosition::START ||
            $this->optionsExtraPosition == FormSelectOptionsPosition::DEFAULT) {

            $this->options = array_merge(
                $this->optionsExtra,
                $this->options
            );

            return;
        }

        if ($this->optionsExtraPosition == FormSelectOptionsPosition::END) {

            $this->options = array_merge(
                $this->options,
                $this->optionsExtra
            );

            return;
        }

        $this->options = array_merge(
            $this->options,
            $this->optionsExtra
        );

        if ($this->optionsExtraPosition == FormSelectOptionsPosition::SORT_TEXT ||
            $this->optionsExtraPosition == FormSelectOptionsPosition::SORT_TEXT_ASC) {
            $this->options = collect($this->options)
                ->sortBy($this->optionTextAttribute)
                ->toArray()
            ;

            return;
        }

        if ($this->optionsExtraPosition == FormSelectOptionsPosition::SORT_TEXT_DESC) {
            $this->options = collect($this->options)
                ->sortBy($this->optionTextAttribute, SORT_DESC)
                ->toArray()
            ;

            return;
        }

        if ($this->optionsExtraPosition == FormSelectOptionsPosition::SORT_VALUE ||
            $this->optionsExtraPosition == FormSelectOptionsPosition::SORT_VALUE_ASC) {
            $this->options = collect($this->options)
                ->sortBy($this->optionValueAttribute)
                ->toArray()
            ;

            return;
        }

        if ($this->optionsExtraPosition == FormSelectOptionsPosition::SORT_VALUE_DESC) {
            $this->options = collect($this->options)
                ->sortBy($this->optionValueAttribute, SORT_DESC)
                ->toArray()
            ;

            return;
        }
    }

    private function normalizeOptionsExtra(): void
    {
        if (is_null($this->optionsExtra)) {
            return;
        }

        // check if options is option elements string
        if (is_string($this->options) &&
            preg_match('/<option.*>.*<\/option>/', $this->options)) {
            return;
        }

        // normalize Enum to array
        if (is_string($this->optionsExtra) &&
            strpos($this->optionsExtra, '\\Enums\\') !== false &&
            class_exists($this->optionsExtra)) {
            $this->optionsExtra = \Arr::undot(collect(\Arr::dot($this->optionsExtra::options()))->mapWithKeys(function ($value, $key) {
                $key = preg_replace('/\.title$/', '.' . $this->optionTextAttribute, $key);
                $key = preg_replace('/\.value$/', '.' . $this->optionValueAttribute, $key);

                return [$key => $value];
            })->toArray());

            return;
        }

        // normalize options to array
        if (! is_array($this->options) && (
            $this->options instanceof Collection ||
            $this->options instanceof Arrayable)) {
            $this->options = $this->options->toArray();
        }

        // transform none-associative array
        if (is_array($this->options) &&
            array_keys($this->options) !== range(0, count($this->options) - 1)) {

            $this->options = $this->transformOptionsRecursive(
                $this->options,
                $this->optionValueAttribute,
                $this->optionTextAttribute
            );
        }

    }

    private function transformOptionsRecursive(
        $options,
        $valueAttribute = 'value',
        $textAttribute = 'title'
    ): array {
        return collect($options)->map(function ($value, $key) use ($valueAttribute, $textAttribute) {

            if (is_array($value) &&
                isset($value['options'])) {
                return [
                    'optgroup' => $value['title'] ?? $key,
                    'options' => $this->transformOptionsRecursive(
                        $value['options'],
                        $valueAttribute,
                        $textAttribute
                    ),
                ];
            }

            return [
                $valueAttribute => $key,
                $textAttribute => $value,
            ];

        })->toArray();
    }

    private function setOptions(): void
    {
        if (is_null($this->options) ||
            ! is_string($this->options) ||
            preg_match('/<option.*>.*<\/option>/', $this->options)) {
            return;
        }

        if (strpos($this->options, '\\Enums\\') !== false &&
            class_exists($this->options)) {
            $this->options = $this->options::options();

            return;
        }

        $this->options = [];
    }

    /**
     * Get the selected options with the value and text attributes
     *
     * @return array
     */
    public function getOptionsSelected()
    {
        if (is_null($this->selected)) {
            return [];
        }

        $optionValueAttribute = $this->optionValueAttribute;

        $optionsSelected = [];
        foreach ($this->selected as $selected) {

            $option = collect($this->options)
                ->filter(function ($option) use ($selected, $optionValueAttribute) {
                    return ! is_string($option) && $option[$optionValueAttribute] == $selected;
                })?->first()
            ;

            if (is_null($option)) {
                continue;
            }

            $optionsSelected[] = [
                'value' => $option[$this->optionValueAttribute] ?? null,
                'text' => $option[$this->optionTextAttribute] ?? '',
            ];
        }

        return $optionsSelected;
    }

    public function renderOptions(
        array $options,
        int $depth = 0,
        ?string $parentId = null
    ): string {
        $html = [];

        foreach ($options as $i => $option) {

            $optionValue = $option[$this->optionValueAttribute] ?? null;
            $optionText = $option[$this->optionTextAttribute] ?? '';
            $isSelected = in_array($optionValue, $this->selected);
            $isDisabled = in_array($optionValue, $this->optionsDisabled) || (isset($option['_option_selectable']) && $option['_option_selectable'] === false);

            $indent = str_repeat('&nbsp;', $depth * 5);

            if (isset($option['optgroup']) &&
                isset($option['options'])) {

                $optGroupValue = 'optgroup-' . Str::slug($option['optgroup']);

                // Simulated optgroup
                $html[] = sprintf(
                    '<option value="%s" class="option-optgroup" data-depth="%d"%s>%s%s</option>',
                    $optGroupValue,
                    $depth,
                    isset($option['_option_selectable']) && $option['_option_selectable'] === false ? ' disabled' : '',
                    $indent,
                    $option['optgroup']
                );

                $children = $option['options'];
                $lastIndex = count($children) - 1;

                foreach ($children as $j => $child) {

                    $childValue = $child[$this->optionValueAttribute] ?? null;
                    $childText = $child[$this->optionTextAttribute] ?? '';
                    $childSelected = in_array($childValue, $this->selected);
                    $childDisabled = in_array($childValue, $this->optionsDisabled)
                        || (isset($child['_option_selectable']) && $child['_option_selectable'] === false);
                    $childIndent = str_repeat('&nbsp;', ($depth + 1) * 5);

                    $classes = ['option-optgroup-child'];
                    if ($j === 0) {
                    $classes[] = 'option-first';
                    }
                    if ($j === $lastIndex) {
                    $classes[] = 'option-last';
                    }

                    if (isset($child['optgroup']) && isset($child['options'])) {
                        // Nested simulated optgroup
                        $html[] = $this->renderOptions([$child], $depth + 1, $optionValue);

                        continue;
                    }

                    $html[] = sprintf(
                        '<option value="%s"%s%s data-parent="%s" class="%s">%s%s</option>',
                        $this->optionValuePrefix . $childValue,
                        $childSelected ? ' selected' : '',
                        $childDisabled ? ' disabled' : '',
                        $optGroupValue,
                        implode(' ', $classes),
                        $childIndent,
                        $childText
                    );
                }

                continue;
            }

            // Flat option without group
            $html[] = sprintf(
                '<option value="%s"%s%s>%s%s</option>',
                $this->optionValuePrefix . $optionValue,
                $isSelected ? ' selected' : '',
                $isDisabled ? ' disabled' : '',
                $indent,
                $optionText
            );
        }

        return implode("\n", $html);
    }
}
