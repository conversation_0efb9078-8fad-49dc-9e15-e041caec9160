@extends('admin::layouts.app')

@use('App\Support\StrSupport as Str')
@use('App\Enums\SystemContext')

@section('title', true)

@section('flags')
    <x-admin::icon icon="lock" class="flag flag-is-locked" :if="$data->exists && $data->is_locked" />
@endsection

@section('controls')
    <x-admin.button button="back" />
    <x-admin.button button="destroy" data-name="{{$data->systemlabel}}" :if="$data->exists && !$data->is_locked" />
    <x-admin.button button="store" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.'.App::get('module').'.index')}}">{{ucfirst(App::get('module'))}}</a></li>
    <li><a href="{{route('admin.'.App::get('module').'.edit', $company->id )}}">{{ $company->systemlabel }}</a></li>
    <li><a href="{{route('admin.'.App::get('module').'.items.index', $company->id )}}">Items</a></li>
    <li>{{ !$data->exists ? 'Create' : $data->systemlabel }}</li>
@endsection

@section('form')
    <x-admin.form
        :bind="$data" 
        class="form-items" 
        action="{{route('admin.'.App::get('module').'.items.store', $company->id)}}"
        x-data="{ 
            code: '{{ old('code', $data?->code )}}',
            item_related_id: '{{ old('item_related_id', $data )}}',
            unit_group_warehouse_unit_id: '{{ old('units.1.unit_group_unit_id', $data?->units?->firstWhere('unit_group_id', 1)?->unit_group_unit_id )}}',
            unit_group_sales_unit_id: '{{ old('units.2.unit_group_unit_id', $data?->units?->firstWhere('unit_group_id', 2)?->unit_group_unit_id )}}',
            unit_group_pricing_unit_id: '{{ old('units.3.unit_group_unit_id', $data?->units?->firstWhere('unit_group_id', 3)?->unit_group_unit_id )}}',
            unit_group_weight_unit_id: '{{ old('units.4.unit_group_unit_id', $data?->units?->firstWhere('unit_group_id', 4)?->unit_group_unit_id )}}'
        }"
    />

    <x-admin.form.input type="hidden" name="id" />
    <x-admin.form.input type="hidden" name="company_id" value="{{ $company->id }}" />
@endsection

@section('content')

    <x-admin.panel title="General information">

        <x-admin.form.row> {{-- code --}}
            <x-admin.form.label for="code" label="Code" />
            <x-admin.form.input name="code" required="true" x-model="code" data-value="null" />
        </x-admin.form.row>

        <x-admin.form.row> {{-- systemname --}}
            <x-admin.form.label for="systemname" label="systemname" />
            <x-admin.form.input name="systemname" data-value="null" />
        </x-admin.form.row>

        <x-admin.form.row> {{-- related item --}}
            <x-admin.form.label for="item_related_id" label="Related item" />
            <x-admin.form.select 
                x-model="item_related_id"
                name="item_related_id" 
                :options="$itemsRelated" 
                required="true"
            />
        </x-admin.form.row>

        @foreach ($unitGroups as $unitGroup)

            @php
                $itemUnit = $data->units?->firstWhere('unit_group_id', $unitGroup->id);
            @endphp


            <x-admin.form.row 
                class="row-unit-group" 
                data-id="{{ $unitGroup->id }}"
                :x-show="$unitGroup->value === 'warehouse' ? 'item_related_id!=\'\'': null"
                :x-collapse="$unitGroup->value === 'warehouse' ? true : null"
            >
                
                <x-admin.form.input 
                    type="hidden" 
                    name="units[{{ $unitGroup->id }}][id]" 
                    value="{{ old('units.'.$unitGroup->id.'.id', $itemUnit?->id) }}" 
                />
                
                <x-admin.form.input 
                    type="hidden" 
                    name="units[{{ $unitGroup->id }}][unit_group_id]" 
                    valueAttribute="null"
                    value="{{ $unitGroup->id }}"
                    data-value="{{$unitGroup->id}}" 
                />

                <x-admin.form.label 
                    for="units[{{ $unitGroup->id }}][unit_group_unit_id]" 
                    label="{{ $unitGroup->systemname }} unit" 
                />

                @if ($unitGroup->value == 'pricing')
                    <x-admin.form.input 
                        col="1" 
                        :bind="$itemUnit" 
                        valueAttribute="value" 
                        type="money" 
                        name="units[{{ $unitGroup->id }}][value]" 
                        required="true" 
                    />
                @endif

                <x-admin.form.select 
                    col="{{ $unitGroup->value == 'pricing' ? 2 : 3 }}"
                    :options="$unitGroup->units->map(function($unit) {
                        return [
                            'id' => $unit?->id,
                            'systemname' => $unit?->unit?->systemname
                        ];
                    })" 
                    name="units[{{ $unitGroup->id }}][unit_group_unit_id]" 
                    :bind="null"
                    :selected="old('units.'.$unitGroup->id.'.unit_group_unit_id', $itemUnit?->unit_group_unit_id)"
                    class="unit-select"
                    required="true"
                />

                <x-admin.form.col class="col-3 relations mb-n3">

                    @template('item_unit_relation', false)
                    <x-admin.form.row  
                        class="row-item-unit-relation"
                        data-id="[[ID]]" 
                        data-name="[[SYSTEMLABEL]]"
                        x-data="{ 
                            model: '[[MODEL]]'
                        }"
                    >
                        <x-admin.form.input {{-- relation ID --}}
                            type="hidden" 
                            name="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][id]" 
                            value="[[ID]]" 
                        />

                        <x-admin.form.input {{-- unit compositional ID --}}
                            type="hidden" 
                            class="unit_compositional_id"
                            name="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][unit_compositional_id]" 
                            value="[[UNIT_COMPOSITIONAL_ID]]" 
                        />

                        <x-admin.form.label 
                            col="4" 
                            for="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][model]" 
                            label="[[SYSTEMLABEL]]" 
                        />

                        <x-admin.form.select {{-- model --}}
                            col="8"
                            name="units[[[UNIT_GROUP_ID]]][relations][[[ID]]][model]" 
                            x-model="model"
                            :bind="null"
                            data-live-search="true"
                        >
                            @template('item_unit_relation_options')
                                @template('item_unit_relation_option')
                                <option value="[[VALUE]]">[[TITLE]]</option>
                                @endtemplate
                            @endtemplate                                
                        </x-admin.form.select>
         
                    </x-admin.form.row>
                    @endtemplate

                    @php
                        $relations = [];
                        $oldRelations = collect(old('units.'.$unitGroup->id.'.relations', []));
                       
                        if ($unitGroup->value == 'weight') {

                            $relation = $itemUnit?->relations?->first();

                            $relationOld = $oldRelations?->first();

                            $relatable = $relation ?? $relationOld['model'] ?? null;

                            $relations[] = [
                                'id' => 
                                    $relation?->id ?? 
                                    $relationOld?->id ?? 
                                    Str::uniqId(),
                                'unit_group_id' => $unitGroup->id,
                                'unit_compositional_id' => null,
                                'systemlabel' => 'per',
                                'item_unit_relation_options' => $unitGroupWeightUnits->map(function($unit) {
                                    return [
                                        'value' => 'Unit::'.$unit->id,
                                        'title' => $unit->systemlabel
                                    ];
                                }),
                                'model' => $relatable instanceof \App\Models\BaseModel
                                    ? $relatable->relatable_type.'::'.$relatable->relatable_id
                                    : $relatable
                            ];

                        } else {

                            $unitCompositionals = $itemUnit?->unitGroupUnit?->unit?->compositionals;

                            if (count($oldRelation = $oldRelations?->pluck('unit_compositional_id')) > 0) {

                               // dd($oldRelation);

                                $unitCompositionals = $unitGroup->units->map(function($unitGroupUnit) use ($oldRelation) {

                                   // dd($unitGroupUnit);

                                    return $unitGroupUnit->unit?->compositionals?->whereIn('id', $oldRelation)?->count() > 0
                                        ? $unitGroupUnit->unit?->compositionals
                                        : null
                                    ;

                                })?->filter()?->first();
                            }
       
                            $relations = ($unitCompositionals?->sortBy('id')?->map(function($compositional) use ($unitGroup, $itemUnit, $properties, $oldRelations) {

                                $property = $properties?->firstWhere(function($property) use ($compositional) {
                                    return 
                                        $property->system_context === SystemContext::UNIT && 
                                        $property->system_context_unit_id === $compositional->unit_id &&
                                        $property->system_context_unit_compositional_id === $compositional->id
                                    ;
                                });

                                $relation = $itemUnit?->relations
                                    ->whereIn('relatable_id', $property?->values?->pluck('id'))
                                    ?->first()
                                ;

                                $relationOld = $oldRelations?->firstWhere('unit_compositional_id', $compositional->id);

                                $relatable = $relation ?? $relationOld['model'] ?? null;
                               
                                
                                $options = $property?->values?->map(function($propertyValue) {
                                    return [
                                        'value' => 'PropertyValue::'.$propertyValue->id,
                                        'title' => $propertyValue->systemlabel
                                    ];
                                });

                                if (is_null($options) ||
                                    $options->count() === 0) {
                                    return null;
                                }

                                return [
                                    'id' =>
                                        $relation?->id ??
                                        $relationOld?->id ??
                                        Str::uniqId(),
                                    'unit_group_id' => $unitGroup->id,
                                    'unit_compositional_id' => $compositional->id,
                                    'systemlabel' => $compositional->systemname,
                                    'item_unit_relation_options' => $options,
                                    'model' => $relatable instanceof \App\Models\BaseModel
                                        ? $relatable->relatable_type.'::'.$relatable->relatable_id
                                        : $relatable
                                ];
                            }) ?? collect([]))->filter();
                        }
                    @endphp

                    @foreach ($relations as $relation)                                

                        @capture('item_unit_relation_options')
                            @foreach ($relation['item_unit_relation_options'] as $option)
                                @usetemplate('item_unit_relation_option', $option)
                            @endforeach
                        @endcapture

                        @php
                            $relation['item_unit_relation_options'] = $item_unit_relation_options;
                        @endphp
                            
                        @usetemplate('item_unit_relation', $relation)

                        @php
                            Log::debug(['relation', $relation]);
                        @endphp

                    @endforeach

                </x-admin.form.col>

            </x-admin.form.row>

        @endforeach

    </x-admin.panel>

    <x-admin.panel type="overview" class="item-variants" title="Warehouse item variants">

        <div class="overview overview-static">
            <div class="overview-header">
                
                @template('item-variant-header')
                <div class="row row-item">
                    <div class="col-1">&nbsp</div>
                    <div class="col-3">Code</div>
                    <div class="col-3">Name</div>
                    <div class="col-2 text-end">price per [[pricing_unit={{$data?->units?->firstWhere('unit_group_id', 3)?->unitGroupUnit?->unit?->systemname}}]]</div>
                    <div class="col">
                        <div class="row units">
                            @template('units-header')

                                @template('unit-relations-header', false)
                                    <div class="col text-end">[[name]]</div>
                                @endtemplate

                                @foreach (($data?->units?->firstWhere('unit_group_id', 1)?->unitGroupUnit?->unit?->compositionals->toArray() ?? []) as $relation)
                                    @usetemplate('unit-relations-header', [
                                        'name' => $properties?->firstWhere(function($property) use ($relation) {
                                            return 
                                                $property->system_context === SystemContext::UNIT && 
                                                $property->system_context_unit_id === $relation['unit_id'] &&
                                                $property->system_context_unit_compositional_id === $relation['id']
                                            ;
                                        })->systemname
                                    ])
                                @endforeach

                            @endtemplate
                        </div>
                    </div>
                </div>
                @endtemplate

            </div>

            <div class="overview-body item-variants-container">

                @template('item-variant', false)
                <div class="row row-item">
                    <x-admin.form.toggle col="1" class="variant-toggle-active" name="variants[[[id]]][is_active]" checked="[[is_active]]" />

                    <x-admin.form.col col="3">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text">[[CODE_PREFIX]]</span>
                            <x-admin.form.input :col="false" class="code" name="variants[[[id]]][code]" value="[[code]]" />
                        </div>
                    </x-admin.form.col>

                  
                    <x-admin.form.input col="3" class="name" name="variants[[[id]]][name]" value="[[name]]" prefix="[[name_prefix]]" />
                    <x-admin.form.input col="2" type="money" name="variants[[[id]]][value]" value="[[value]]"  />
                    <div class="col">
                        <div class="row">
                            @template('relations')
                                @template('unit-relation')
                                <div class="col text-end composable [[visibility]]">
                                    [[name]]
                                    <input type="hidden" name="variants[[[variant_id]]][relations][[[id]]][id]" value="[[id]]" />
                                    <input type="hidden" name="variants[[[variant_id]]][relations][[[id]]][unit_compositional_id]" value="[[unit_compositional_id]]" />
                                    <input type="hidden" name="variants[[[variant_id]]][relations][[[id]]][model]" value="[[model]]" />
                                </div>
                                @endtemplate
                            @endtemplate
                        </div>
                    </div>
                    <input type="hidden" class="variant-id" name="variants[[[id]]][id]" value="[[id]]" />
                    <input type="hidden" name="variants[[[id]]][uniq_id]" value="[[uniq_id]]" />
                </div>
                @endtemplate

                
                @foreach ($data->variants as $variant)                                

                    @capture('relations')
                        @foreach ($variant?->units?->where('unit_group_id', 3)?->first()?->relations as $relation)
                            @usetemplate('unit-relation', [
                                'id'             => $relation->id,
                                'variant_id'     => $variant->id,
                                'name'           => $relation->relatable->systemname,
                                'visibility'     => 'visible',
                                'relatable_id'   => $relation->relatable_id,
                                'relatable_type' => $relation->relatable_type,
                            ])
                        @endforeach
                    @endcapture
            
                    @usetemplate('item-variant', [
                        'id' => $variant->id,
                        'code' => $variant->code,
                        'code_prefix' => ($data->code ?? null) !== null
                            ?  $data->code.' -'
                            : '[empty]',
                        'name' => $variant->systemname,
                        'name_prefix' => ($data->systemname ?? null) !== null
                            ?  $data->systemname.' -'
                            : '[empty]',
                        'value' => $variant?->units?->where('unit_group_id', 3)?->first()?->value,
                        'value_formatted' => Number::convertStorageToMoney($variant?->units?->where('unit_group_id', 3)?->first()?->value, 2),
                        'uniq_id' => $variant->warehouse_uniq_id,
                        'is_active' => $variant->is_active ? 'true' : 'false',
                        'is_active' =>  'true',
                        'relations' => $relations,
                    ])

                @endforeach
            </div>

            <div class="overview-empty alert alert-info mt-3">No variants possible or required.</div>
            <div class="overview-errors alert alert-danger mt-3 collape collapsed">Please select a pricing unit first.</div>
        </div>

        
    
    </x-admin.panel>

    <x-admin.panel type="overview" title="Rules & Constraints">

        @slot('controls')
            <div type="button" class="btn btn-sm btn-primary btn-rule-add">
                Add rule
            </div>
        @endslot

         <div class="overview overview-rules d-none">

            <div class="overview-header">
                <div class="row row-item">
                    <div class="col-4">
                        <div class="row">
                            <div class="col-5">Type</div>
                            <div class="col">Applies to</div>
                        </div>                    
                    </div>
                    <div class="col-5">Specification</div>
                    <div class="col-3">
                        <div class="row">
                            <div class="col">Pricing</div>
                            <div class="col-auto text-end">Controls</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="overview-body rules-container"> 

                @template('row-rule', false)
                <div 
                    class="row row-item row-rule" 
                    x-data="{ 
                        id: '[[id]]',
                        is_active: [[is_active]],
                        type: '[[type]]',
                        apply_to: '[[apply_to]]',
                        unit_group: '[[unit_group]]',
                        unit_specification: '[[unit_specification]]',
                        unit_specification_min: '[[unit_specification_min]]',
                        unit_specification_max: '[[unit_specification_max]]',
                        pricing_type: '[[pricing_type]]',
                        pricing_price: '[[pricing_price]]',
                        pricing_percentage: '[[pricing_percentage]]',
                    }"
                >
                    <input type="hidden" name="rules[[[id]]][id]" value="[[id]]" x-model="id" />

                    <div class="col-4">
                        <div class="row">

                            <div class="col-2 col-md-1">
                                <x-admin.form.toggle 
                                    class="rule-toggle-active" 
                                    id="rule_is_active_[[id]]" 
                                    name="rules[[[id]]][is_active]" 
                                    x-model="is_active"
                                />
                            </div>

                            <div class="col-4">
                                <x-admin.form.select 
                                    id="rule_type_[[id]]"
                                    name="rules[[[id]]][type]" 
                                    class="rule-type"
                                    required="true"
                                    x-model="type"
                                    :options="\App\Enums\ItemRuleType::class"
                                />
                            </div>

                            <div class="col">
                                <x-admin.form.select 
                                    id="rule_apply_to_[[id]]"
                                    name="rules[[[id]]][apply_to]" 
                                    class="rule-apply-to"
                                    required="true"
                                    x-model="apply_to"
                                    :options="[
                                        'item' => 'Item', 
                                        'variants' => [
                                            'title' => 'Variants',
                                            'options' => $data->variants->pluck('name', 'warehouse_uniq_id')->toArray()
                                        ]
                                    ]" 
                                />
                            </div>
                        </div>
                    </div>

                    <div class="col-5">
                        
                        <div class="input-group input-group-sm">
                    
                            <div class="input-group-addon w-25">
                                <x-admin.form.select 
                                    id="rule_unit_group_[[id]]"
                                    name="rules[[[id]]][unit_group]" 
                                    class="unit-group"
                                    x-model="unit_group"
                                    required="true"
                                    :options="[
                                        'warehouse' => 'Warehouse', 
                                        'sales' => 'Sales',
                                        'pricing' => 'Pricing'
                                    ]" 
                                />
                            </div>

                            <div class="input-group-addon w-25">
                                <x-admin.form.select 
                                    id="rule_unit_specification_[[id]]"
                                    name="rules[[[id]]][unit_specification]" 
                                    class="unit-specification"
                                    required="true"
                                    x-model="unit_specification"
                                    :options="\App\Enums\ItemRuleUnitSpecification::class"
                                />
                            </div>

                            <x-admin.form.input 
                                type="decimal"
                                id="rule_unit_specification_min_[[id]]"
                                name="rules[[[id]]][unit_specification_min]" 
                                class="form-control unit-specification-min w-10"
                                data-decimals="2"
                                value="[[unit_specification_min]]"
                                x-model="unit_specification_min"
                                x-show="
                                    unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::MIN }}' || 
                                    unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::RANGE }}'"
                            />

                            <span 
                                class="input-group-text unit-specification-to px-1"
                                x-show="unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::RANGE }}'"
                                >to</span>

                            <x-admin.form.input 
                                type="decimal"
                                id="rule_unit_specification_max_[[id]]"
                                name="rules[[[id]]][unit_specification_max]" 
                                class="form-control unit-specification-max w-10"
                                data-decimals="2"
                                value="[[unit_specification_max]]"
                                x-model="unit_specification_max"
                                x-show="
                                    unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::MAX }}' || 
                                    unit_specification == '{{ \App\Enums\ItemRuleUnitSpecification::RANGE }}'"
                            />

                            <span 
                                class="input-group-text unit-group-value"
                                x-show="unit_group"
                            >[[unit_group_value]]</span>
                        </div>                  

                    </div>

                    <div class="col-3">
                        <div class="row">

                            <div class="col">

                                <div x-show="type == 'pricing'">
                                    <div class="input-group input-group-sm">
                                
                                        <div class="input-group-addon">
                                            <x-admin.form.select 
                                                id="rule_pricing_type_[[id]]"
                                                name="rules[[[id]]][pricing_type]" 
                                                x-model="pricing_type"
                                                class="pricing-type"
                                                :options="\App\Enums\ItemRulePricingType::class" 
                                            />
                                        </div>
                                        
                                        <x-admin.form.input 
                                            id="rule_pricing_price_value_[[id]]"
                                            name="_rules[[[id]]][pricing_price_value]" 
                                            type="money" 
                                            data-currency="€" 
                                            data-decimals="2"
                                            value="[[pricing_price]]"
                                            class="form-control"
                                            :noInputGroup="true"
                                            x-model="pricing_price"
                                            x-show="
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PRICE_FIXED }}' || 
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PRICE_SUBSTRACT }}' || 
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PRICE_ADD }}'
                                            "
                                        />

                                        <x-admin.form.input 
                                            id="rule_pricing_percentage_value_[[id]]"
                                            name="_rules[[[id]]][pricing_percentage_value]" 
                                            type="decimal" 
                                            :noInputGroup="true"
                                            data-decimals="2"
                                            value="[[pricing_percentage]]"
                                            class="form-control"
                                            x-model="pricing_percentage"
                                            x-show="
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PERCENTAGE_SUBSTRACT }}' || 
                                                pricing_type == '{{ \App\Enums\ItemRulePricingType::PERCENTAGE_ADD }}'
                                            "
                                        />
                                    </div> 
                                </div>  

                            </div>

                            <div class="col-auto text-end">
                                <div type="button" class="btn btn-sm btn-success">
                                    <i class="fa-solid fa-plus"></i>
                                </div>
                                <div type="button" class="btn btn-sm btn-danger btn-rule-delete">
                                    <i class="fa-solid fa-trash-can"></i>
                                </div>
                            </div>
                        
                        </div>
                    </div>
                </div>
                @endtemplate

                @foreach ((old('rule', null) ?? $data?->rules?->toArray()) as $rule)                               

                    @usetemplate('row-rule', [
                        'id' => $rule['id'],
                        'type' => $rule['type'],
                        'apply_to' => is_null(($rule['apply_to'] ?? $rule['item_variant_id'])) 
                            ? 'item' 
                            : $rule['apply_to'] ?? $data->variants->where('id', $rule['item_variant_id'])->first()->warehouse_uniq_id,
                        'is_active' => $rule['is_active'] ? 'true' : 'false',
                        'unit_group' => $rule['unit_group'] ?? $groups->where('id', $rule['unit_group_id'])->first()->value,
                        'unit_specification' => $rule['unit_specification'],
                        'unit_specification_min' => $rule['unit_specification_min'],
                        'unit_specification_max' => $rule['unit_specification_max'],
                        'unit_specification_max_formatted' => is_null($rule['unit_specification_max'])
                            ? ''
                            : Number::convertStorageToDecimal($rule['unit_specification_max'], 2),
                        'unit_specification_min_formatted' => is_null($rule['unit_specification_min'])
                            ? ''
                            : Number::convertStorageToDecimal($rule['unit_specification_min'], 2),
                        'pricing_type' => $rule['pricing_type'],
                        'pricing_price' => in_array($rule['pricing_type'], ['percentage_add', 'percentage_substract'])
                            ? null
                            : $rule['pricing_price_value'] ?? $rule['pricing_value'] ?? '',
                        'pricing_price_formatted' => in_array($rule['pricing_type'], ['percentage_add', 'percentage_substract'])
                            ? null
                            : Number::convertStorageToMoney($rule['pricing_price_value'] ?? $rule['pricing_value'] ?? null, 2),
                        'pricing_percentage' => !in_array($rule['pricing_type'], ['percentage_add', 'percentage_substract'])
                            ? null
                            : $rule['pricing_percentage_value'] ?? $rule['pricing_value'] ?? '',
                        'pricing_percentage_formatted' => !in_array($rule['pricing_type'], ['percentage_add', 'percentage_substract'])
                            ? null
                            : Number::convertStorageToDecimal(($rule['pricing_percentage_value'] ?? $rule['pricing_value'] ?? null), 2),
                       
                    ]) 

                @endforeach

            </div>
        </div>

        <div class="message message-no-data message-rule alert alert-info mt-3">There are no rules or rules yet.</div>

    </x-admin.panel>

@endsection