<?php

namespace App\View\Components\Portal;

use App\Traits\Filterable;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Route;
use Illuminate\View\Component;

class Limiter extends Component
{
    public $isExeeced;
    public $paginationOptions;
    public $count;
    public $filterData;

    /**
     * Create a new component instance.
     */
    public function __construct(public $any)
    {
        $this->isExeeced = false;
        $this->paginationOptions = Filterable::$paginationOptions;
        $this->count = $any->total();
        $route = explode('.', Route::currentRouteName())[0];

        if (isset(session($route)['limit'])) {

            $this->filterData = session($route)['limit'];
        }
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View
    {
        return view('portal.components.limiter');
    }
}
