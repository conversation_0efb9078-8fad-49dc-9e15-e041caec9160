<?php

namespace App\Http\Controllers\Portal;

use App\Http\Controllers\ModuleController;
use App\Http\Requests\contacts\ActivateUserRequest;
use App\Http\Requests\contacts\LoginContactRequest;
use App\Http\Requests\contacts\PasswordMailRequest;
use App\Http\Requests\contacts\ResetPasswordRequest;
use App\Models\Company;
use App\Models\User;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

class AuthController extends ModuleController
{
    public function index(): View
    {
        dd('gdfgdfgffdgdfgfg');

        return view('pages.auth.index');
    }

    public function login(LoginContactRequest $request): RedirectResponse|View
    {
        dd('hfhgfhfhgfhgfhfh');

        $data = $request->validated();

        if (($user = User::login(
            $data['email'],
            $data['password'])
        ) === false) {
            session()->flash('message', 'Login invalid');

            return redirect()->back();
        }

        if ($user->companies->count() == 1) {
            $user->companies->first()->login();

            return to_route('web.dashboard.index');
        }

        return view('portal.pages.login.companies', [
            'companies' => $user->companies,
        ]);
    }

    public function logout(): RedirectResponse
    {

        User::empty();
        Company::empty();

        return to_route('web.auth.index');
    }

    public function passwordForget(): View
    {

        return view('pages.auth.forgot_password');
    }

    public function passwordMail(PasswordMailRequest $request): RedirectResponse
    {

        $user = User::where('email', $request->email)->first();

        if ($user != null) {
            $user->resetPassword();
        }

        session()->flash('message', 'If you have an account on this system, you will receive a password reset email shortly.');

        return redirect('/');
    }

    public function passwordReset($token = null): View
    {
        return view('pages.auth.reset_password', compact('token'));
    }

    public function passwordStore($token, ResetPasswordRequest $request): RedirectResponse
    {
        $user = User::where('token', $token)->first();

        $data = $request->validated();

        $user->savePassword([
            'password' => $data['password'],
        ]);

        if (($user = User::login(
            $user->email,
            $data['password'])
        ) === false) {

            session()->flash('message', 'Login invalid');

            return redirect()->back();
        }

        return to_route('auth.index');
    }

    public function activation($token = null): View
    {
        return view('pages.auth.activation', compact('token'));
    }

    public function activate($token, ActivateUserRequest $request): RedirectResponse
    {
        $user = User::where('token', $token)->first();

        $data = $request->validated();

        $user->activate([
            'password' => $data['password'],
        ]);

        if (($user = User::login(
            $user->email,
            $data['password'])
        ) === false) {

            session()->flash('message', 'Login invalid');

            return redirect()->back();
        }

        return to_route('auth.index');
    }
}
