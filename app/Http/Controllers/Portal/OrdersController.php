<?php

namespace App\Http\Controllers\Portal;

use App\Http\Controllers\ModuleController;
use App\Models\Order;
use App\Traits\Filterable;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;

class OrdersController extends ModuleController
{
    use Filterable;

    public function index(Request $request): View
    {

        Filterable::$paginationOptions = [10, 25, 50, 100];

        $paginationLimit = 10;
        $page = 1;
        $sort = ['order_number', 'desc'];

        if (isset($request->limiter)) {
            $paginationLimit = $request->limiter;
            session(['orders.limit' => $paginationLimit]);
        }

        if (isset(session('orders')['limit'])) {
            $paginationLimit = session('orders')['limit'];
        }

        if (isset($request->page)) {
            session(['orders.current_page' => $request->page]);
        }

        if (isset(session('orders')['current_page'])) {
            $page = session('orders')['current_page'];
        }

        if (isset($request->sort)) {
            session(['orders.sort' => $request->sort]);
        }

        if (isset(session('orders')['sort'])) {
            $sort = explode('.', session('orders')['sort']);
        }

        $data = Order::orderBy($sort[0], $sort[1])->paginate($paginationLimit, ['*'], 'page', $page);

        return view('portal.pages.orders.index', compact('data', 'sort'));
    }

    public function specific(Order $order): View
    {
        return view('portal.pages.orders.specific', compact('order'));
    }
}
