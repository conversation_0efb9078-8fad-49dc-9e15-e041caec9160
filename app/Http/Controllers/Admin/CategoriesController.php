<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ModuleController;
use App\Http\Requests\categories\StoreCategoryRequest;
use App\Models\Category;
use App\Models\LedgerCategory;
use App\Models\Menu;
use ExactOnline;
use Illuminate\Contracts\View\View;
use Picqer\Financials\Exact\Account;

class CategoriesController extends ModuleController
{
    public function index(): View
    {
        // $connection = app()->make('Exact\Connection');
        // $account = new Account($connection);
        // dd($account->get());

       // dd(ExactOnline::Account()->get());

        return view('admin::pages.categories.index', [
            'data' => Category::getStructuredList(
                count: 'products'
            ),
        ]);
    }

    public function create(): View
    {
        return $this->edit(new Category());
    }

    public function edit(Category $data): View
    {
        $data = $data->load('tags');

        return view('admin::pages.categories.create', [
            'data' => $data,
            'ledgerAccounts' => LedgerCategory::getStructuredList(
                with: ['accounts']
            ),
            'categories' => Category::getOptionsList(
                Category::getStructuredList(),
                selected: $data->parent_id,
                disabled: $data->id
            ),
        ]);
    }

    public function store(StoreCategoryRequest $request): View
    {
        $id = $request->input('id');

        $model = Category::findOrNew($id);
        $model->fill($request->validated());

        $model->save();

        $model->menus()->delete();

       foreach (config('modules.menus') as $key => $menu) {

           if ($request->has($key)) {
               $menu = new Menu();
               $menu->menuable_id = $model->id;
               $menu->systemname = $key;

               $highestSort = Menu::where('systemname', $key)->max('sort');
               $menu->sort = $highestSort + 1;

               $model->menus()->save($menu);
           }
       }

       session()->flash('message', $model->id ? 'Category updated successfully' : 'Category created successfully');

       return self::index();
    }

    public function destroy(Category $data): View
    {
        $data->delete();
        session()->flash('message', 'Category deleted successfully');

        return self::index();
    }
}
