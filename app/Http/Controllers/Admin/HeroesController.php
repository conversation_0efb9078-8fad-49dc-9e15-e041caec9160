<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ModuleController;
use App\Http\Requests\heroes\StoreHeroRequest;
use App\Models\Button;
use App\Models\Hero;
use App\Models\Image;
use Illuminate\Contracts\View\View;

class HeroesController extends ModuleController
{
    public function index(): View
    {
        $data = Hero::all();

        return view('admin::pages.heroes.index', compact('data'));
    }

    public function create(): View
    {
        $data = new Hero();

        return view('admin::pages.heroes.create', compact('data'));
    }

    public function edit(Hero $data): View
    {
        return view('admin::pages.heroes.create', compact('data'));
    }

    public function store(StoreHeroRequest $request): View
    {

        $buttonCheck = Button::verify($request->input('buttons'));

        if (is_array($buttonCheck)) {
            return redirect()->back()->withErrors($buttonCheck)->withInput();
        }

        $id = $request->input('id');

        $model = Hero::findOrNew($id);
        $model->fill($request->validated());

        $model->save();

        if ($request->hasFile('image')) {

            Image::upload(
                $request->file('image'),
                $model->id,
                $model->getMorphClass(),
            );
        }

        Button::store([
            'buttonable_type' => $model->getMorphClass(),
            'buttonable_id' => $model->id,
            'buttons' => $request->input('buttons'),
        ]);

        session()->flash('message', $model->id ? 'Hero updated successfully' : 'Hero created successfully');

        return self::index();
    }

    public function destroy(Hero $data): View
    {
        $data->delete();
        session()->flash('message', 'Hero deleted successfully');

        return self::index();
    }
}
