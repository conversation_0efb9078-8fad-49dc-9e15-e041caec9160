<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     */
    protected function redirectTo(Request $request): ?string
    {
       // dd((App::get('guard') == 'admin' ? 'admin' : 'web').'.auth.index');
        return $request->expectsJson()
            ? null
            : route((App::get('guard') == 'admin' ? 'admin' : 'web') . '.auth.index');
    }
}
