<?php

namespace App\Http\Requests;

use App\Enums\SystemContext;
use App\Models\Property;
use App\Rules\Translatable;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StorePropertyValueRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id' => [
                'nullable',
                'integer',
                'exists:properties_values,id',
            ],
            'property_id' => [
                'required',
                'integer',
                'exists:properties,id',
            ],
            'systemname' => [
                'required',
                'string',
                'min:1',
                'max:300',
                Rule::unique('properties_values')
                    ->where(function ($query) {
                        $query->whereNot('id', $this->input('id'));
                        $query->where('property_id', $this->input('property_id'));
                    }
                ),
            ],
            'value' => [
                'nullable',
                'string',
                Rule::requiredIf(
                    function () {
                        return Property::query()
                            ->where('id', intval($this->input('property_id')))
                            ->first()?->system_context === SystemContext::UNIT
                        ;
                    }
                ),
                Rule::unique('properties_values', 'value')
                    ->where(function (Builder $query) {
                        $query->where('property_id', intval($this->input('property_id')));
                    })
                    ->ignore($this->input('id')),
            ],
            'name' => new Translatable(true),
            'content' => new Translatable(false),
        ];
    }

    public function messages()
    {
        return [
            'systemname.required' => 'The system name is required.',
            'sorting.required' => 'The sorting is required.',
            'systemname.string' => 'The system name must be a string.',
            'systemname.min' => 'The system name must be at least :min characters.',
            'systemname.max' => 'The system name must not exceed :max characters.',
            'systemname.unique' => 'The system name is already taken.',
            'status.boolean' => 'The status field must be a boolean.',
            'is_highlighted.boolean' => 'The is_highlighted field must be a boolean.',
            'published_at.required' => 'The published at field is required.',
            'published_at.date' => 'The published at field must be a valid date.',
            'expired_at.required' => 'The expired at field is required.',
            'expired_at.date' => 'The expired at field must be a valid date.',
            'type_id.required' => 'The type field is required.',
            'type_id.string' => 'The type field must be a string.',
            'status_nl.boolean' => 'The status_nl field must be a boolean.',
            'status_de.boolean' => 'The status_de field must be a boolean.',
            'status_en.boolean' => 'The status_en field must be a boolean.',
            'name_nl.required' => 'The title (NL) field is required when status_nl is true.',
            'name_de.required' => 'The title (DE) field is required when status_de is true.',
            'name_en.required' => 'The title (EN) field is required when status_en is true.',
            'categories.required' => 'Categories are required if you dont check globally available.',
        ];
    }
}
