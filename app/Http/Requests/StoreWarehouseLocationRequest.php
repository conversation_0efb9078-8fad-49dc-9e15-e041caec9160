<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreWarehouseLocationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
       // dd($this->input());

        return [
            'id' => [
                'nullable',
                'integer',
                'exists:warehouses_locations,id',
            ],
            'warehouse_id' => [
                'required',
                'integer',
                'exists:warehouses,id',
            ],
            'systemname' => [
                'required',
                'string',
                'min:2',
                'max:256',
                Rule::unique('warehouses_locations', 'systemname')
                    ->ignore($this->input('id'))
                    ->where(function ($query) {
                        return $query->where('warehouse_id', intval($this->input('warehouse_id')));
                    }),
            ],
        ];
    }
}
