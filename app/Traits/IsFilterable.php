<?php

namespace App\Traits;

use App\Services\BladeService;
use Cache;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Lacodix\LaravelModelFilter\Traits\HasFilters;

trait IsFilterable
{
    use HasFilters;

    public static function bootIsFilterable()
    {
        app(BladeService::class)->addAsset(resource_path('admin/js/widgets/filters.js'));
    }

    public function getFilters(
        string $group = '__default'
    ): array {
        return Cache::get(
            self::getFilterStorageKey($group),
            []
        );
    }

    public function storeFilters(
        string $group = '__default'
    ): void {
        Cache::forever(
            self::getFilterStorageKey($group),
            $this->getOnlyFilterUsableValues(
                request()->all(),
                $group
            )->toArray()
        );
    }

    public static function resetFilters(
        string $group = '__default'
    ): void {
        Cache::forget(self::getFilterStorageKey($group));
    }

    public function scopeApplyFilter(
        Builder $query,
        ?array $values = null,
        string $group = '__default'
    ): Builder {
        return $query->filter(
            $values ?? $this->getFilters(),
            $group
        );
    }

    public static function getFilterStorageKey(
        string $group = '__default'
    ): string {
        return implode('.', [
            'filters',
            auth()->user()->getTable(),
            auth()->user()->id,
            (new static())->getMorphClass(),
            $group,
        ]);
    }

    protected function getOnlyFilterUsableValues(array $values, string $group): Collection
    {

        return collect($values)
            ->only($this->getAllFilterQueryNames($group))
            ->filter(static fn ($value) => isset($value) && $value !== '')
            ->map(function ($value, $filterKey) use ($group) {

                $filters = $this->filterInstances[$group] ?? null;

                $filter = $filters?->first(function ($filter) use ($filterKey) {
                    return ($filter?->queryName() ?? null) === $filterKey;
                });

                if ($filter instanceof \Lacodix\LaravelModelFilter\Filters\SelectFilter &&
                    ($filter->mode ?? null) === \Lacodix\LaravelModelFilter\Enums\FilterMode::EQUAL) {
                    return $value;
                }

                return is_array($value)
                    ? $value
                    : [$value]
                ;
            });
    }
}
