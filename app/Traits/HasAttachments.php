<?php

namespace App\Traits;

use App\Models\Attachment;
use App\Observers\AttachableObserver;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait HasAttachments
{
    public static function bootHasAttachments(): void
    {
        static::observe(app(AttachableObserver::class));
    }

    public function attachable(): MorphMany
    {
        return $this->MorphMany(Attachment::class, 'attachable');
    }

    public function linkable(): MorphMany
    {
        return $this->MorphMany(Attachment::class, 'linkable');
    }
}
