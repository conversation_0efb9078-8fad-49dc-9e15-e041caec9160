<?php

namespace App\Services\APIS;

use App\Models\Document;
use Arr;
use Illuminate\Support\Facades\Storage;

class GoogleDocumentAIAPIService extends APIService
{
    public function getDocumentData(Document $document)
    {

        $endpointSegments = implode('/', [
            'v1',
            'projects',     Arr::get($this->config, 'project'),
            'locations',    Arr::get($this->config, 'location'),
            'processors',
        ]);

        $responses = $this->batchRequest(
            method: 'post',
            requests: [
                'invoice' => $endpointSegments . '/' . $this->getProcessor('invoice'),
                'form' => $endpointSegments . '/' . $this->getProcessor('form'),
                'ocr' => $endpointSegments . '/' . $this->getProcessor('ocr'),
            ],
            data: [
                "skipHumanReview" => true,
                "rawDocument" => [
                    "mimeType" => "application/pdf",
                    "content" => base64_encode(Storage::get($document->latestVersion->getStoragePath())),
                ],
            ]
        );

        if (in_array(false, $responses)) {
        return false;
        }

        // $responses = json_decode(Storage::get('responses.json'), true);

        return $responses;
    }

    public function getProcessor($processorType)
    {
        $processor = Arr::get($this->config, 'processors.' . $processorType, []);

        $processorString = ($processor['id'] ?? '');

        if (isset($processor['version'])) {
            $processorString .= '/processorVersions/' . $processor['version'];
        }

        return $processorString . ':process';
    }
}
