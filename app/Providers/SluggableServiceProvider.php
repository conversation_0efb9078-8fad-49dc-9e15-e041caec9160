<?php

namespace App\Providers;

use App\Observers\SluggableObserver;
use App\Services\SlugService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;

class SluggableServiceProvider extends ServiceProvider
{
    /**
     * Register the application services.
     *
     * @return void
     */
    public function register()
    {
       // Log::debug('SluggableServiceProvider register');
    }

    /**
     * Bootstrap the application services.
     *
     * @return void
     */
    public function boot()
    {
       // Log::debug('SluggableServiceProvider boot');

//        $this->app->singleton(SluggableObserver::class, function ($app) {
//            return new SluggableObserver(
//                new SlugService(),
//                $app['events']
//            );
//        });

    }
}
