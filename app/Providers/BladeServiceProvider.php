<?php

namespace App\Providers;

use App\Services\BladeService;
use Blade;
use Illuminate\Container\Container;
use Illuminate\Support\ServiceProvider;

class BladeServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton(BladeService::class, function () {
            return new BladeService(fn () => Container::getInstance());
        });

        // Alias for the Blade facade
        $this->app->alias(BladeService::class, 'bladeService');
    }

    public function boot()
    {
        // /TEMPLATES
        Blade::directive('template', function (
            $templateName,
            $outputContent = true
        ) {
            return "<?php echo app(\App\Services\BladeService::class)->pushToStack('templates', {$templateName}, $outputContent); ob_start();?>";
        });

        Blade::directive('endtemplate', function () {
            return "<?php "
                 . "\$content = ob_get_clean(); "
                 . "\$name = app(\App\Services\BladeService::class)->popFromStack('templates'); "
                 . "echo app(\App\Services\BladeService::class)->addToStack('templates', \$name, \$content);?>";
        });

        Blade::directive('usetemplate', function ($expression) {
            return "<?=app(\\App\\Services\\BladeService::class)->useStack('templates', {$expression}); ?>";
        });

        Blade::directive('renderStack', function ($stackName) {
            return "<?=app(\App\Services\BladeService::class)->renderStack({$stackName});?>";
        });

        // /CAPTURE CONTENT
        Blade::directive('capture', function ($expression) {
            // The expression should be the variable name as a quoted string, e.g. 'units'
            return "<?php ob_start(); \$__capture_var = {$expression}; ?>";
        });

        Blade::directive('endcapture', function () {
            return "<?php \${\$__capture_var} = ob_get_clean(); ?>";
        });

        // /DATASETS
        Blade::directive('dataSet', function ($expression): string {
            return "<?php app(\App\Services\BladeService::class)->addToDataSet({$expression}); ?>";
        });

        Blade::directive('dataSetEntry', function ($expression): string {
            return "<?php app(\App\Services\BladeService::class)->addEntryToDataSet({$expression}); ?>";
        });

        // renderDataSets
        Blade::directive('renderDataSets', function (): string {
            return "<?php echo app(\App\Services\BladeService::class)->renderDataSets(); ?>";
        });

    }
}
