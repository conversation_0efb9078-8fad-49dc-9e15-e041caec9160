<?php

namespace App\Observers;

use App\Services\SlugService;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class SluggableObserver
{
    private $events;

    private $slugService;

    public function __construct(
        Dispatcher $events
    ) {
        $this->slugService = app(SlugService::class);
        $this->events = $events;
    }

    public function deleting(Model $model): void
    {
        $this->slugService->promote($model);

        // slug ids of itself
        $slugRootIds = $model->slugs()->get()->pluck('id');

        // all decendant slug ids
        $slugDecendantsIds = $model->slugs()
            ->with('descendants')->get()
            ->flattenTree('children')->pluck('id')
            ->diff($slugRootIds);

        $model->slugs()->delete();
    }

    public function saving(Model $model): void
    {

        //    // Perform your slug validation here
        //     $validator = Validator::make(['slug' => $model->slug], [
        //         'slug' => [
        //             'required',
        //             'string',
        //             'max:255',
        //             'unique:pages,slug,' . $model->id,
        //             // Add other rules as necessary
        //         ],
        //     ]);

        //     $validator->getMessageBag()->add('password', 'Password wrong');

        // if ($validator->fails()) {
        //  //   dd($validator);
        //     // Stop the save operation by throwing a validation exception
        //     throw new ValidationException($validator);
        // }

       // $this->slugService->validate($model);

       $this->slugService->validate($model);
    }

    public function saved(Model $model)
    {
        Log::debug('SluggableObserver Saved Start');

        if ($this->fireSluggingEvent($model) === false) {
            return false;
        }

        $wasSlugged = $this->slugService->slug($model);

       // $this->fireSluggedEvent($model, 'status');

        // if ($wasSlugged) {
        //     return $model->saveQuietly();
        // }

        Log::debug('SluggableObserver Saved End');
    }

    public function creating(Model $model): void
    {
        Log::debug('SluggableObserver Creating');
    }

    public function created(Model $model): void
    {
        Log::debug('SluggableObserver Created');
    }

    public function updated(Model $model): void
    {
        Log::debug('SluggableObserver Updated');
    }

    public function updating(Model $model): void
    {
        Log::debug('SluggableObserver Updating');
    }

    protected function fireSluggingEvent(Model $model): ?bool
    {
        return $this->events->until('eloquent.slugging: ' . get_class($model), [
            $model, 'saved',
        ]);
    }

    protected function fireSluggedEvent(Model $model, string $status): void
    {
        $this->events->dispatch('eloquent.slugged: ' . get_class($model), [
            $model,
            $status,
        ]);
    }
}
