<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Cache;

/*
new MimeType('document.text')
$argument can be in formats:
    document
    document.text
    document.text|pdf
    document.text|pdf,image,video
*/

class MimeType implements ValidationRule
{
    public function __construct(
        public string|array $mimeTypeSpecifications = []
    ) {
        if (is_string($mimeTypeSpecifications)) {
            $this->mimeTypeSpecifications = explode(',', $mimeTypeSpecifications);
        }
    }

    public function validate(
        string $attribute,
        mixed $value,
        Closure $fail
    ): void {
        $mimeTypesSpecifications = $this->mimeTypeSpecifications;

        $mimeTypes = Cache::remember(
            'mimetypes_' . implode('_', $this->mimeTypeSpecifications),
            86400 * 10,
            function () use ($mimeTypesSpecifications) {

                $mimeTypes = collect(config('mimetypes'));

                foreach ($mimeTypesSpecifications as $mimeTypeSpec) {

                    $spec = explode('.', $mimeTypeSpec);

                    $mimeTypes = $mimeTypes->where('group', $spec[0]);

                    if (count($spec) > 1) {

                        $mimeTypes = $mimeTypes->filter(function ($i) use ($spec) {

                            foreach (explode('|', $spec[1]) as $s) {
                                if (! in_array($s, $i['subgroups'] ?? [])) {
                                return false;
                                }
                            }

                            return true;
                        });
                    }
                }

                return $mimeTypes;
            }
        );

        // acceptable mimeType
        if (! isset($mimeTypes[$value->getMimeType()])) {
            $fail("The $attribute is not an acceptable file type.");
        }

        // mimeType matches extension
        if (! in_array(
            pathinfo($value->getClientOriginalName())['extension'],
            $mimeTypes[$value->getMimeType()]['extensions']
        )) {
            $fail("The file extension does not match the type of file.");
        }
    }
}
