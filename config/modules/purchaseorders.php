<?php

return [
    'model' => 'PurchaseOrder',
    'controller' => 'PurchaseOrders',
    'order_number_initial' => 5000000,
    'routes' => [
        // 'pdf:get./{propertyvalue}/pdf',
        // 'import|search:get./import',
        // 'import.search|search:post./import/search',
        // 'import.create|import:get./import/{id}',
        // 'items.index|items:get./{model}/items',
        // 'items.create|itemsCreate:get./{company}/items/create',
        // 'items.edit|itemsEdit:get./{company}/items/{item}/edit',
    ],
];
