{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.4", "barryvdh/laravel-debugbar": "^3.15", "brick/money": "^0.9.0", "brick/phonenumber": "^0.6.0", "brick/postcode": "^0.3.1", "cocur/slugify": "^4.5", "commerceguys/addressing": "^2.2", "dcblogdev/laravel-microsoft-graph": "^4.0", "dipeshsukhia/laravel-html-minify": "^3.3", "giggsey/libphonenumber-for-php-lite": "*", "globalcitizen/php-iban": "^4.2", "google/apiclient": "^2.18", "google/cloud": "^0.267.0", "google/cloud-ai-platform": "*", "google/cloud-document-ai": "*", "guzzlehttp/guzzle": "^7.9", "guzzlehttp/psr7": "^2.7", "jenssegers/agent": "^2.6", "lacodix/laravel-model-filter": "^3.3", "laravel/framework": "^12.0", "laravel/horizon": "^5.33", "laravel/octane": "^2.9", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.0", "mikebronner/laravel-model-caching": "^12.0", "mikebronner/laravel-pivot-events": "*", "mpociot/vat-calculator": "^1.5", "omniphx/forrest": "^2.6", "php-http/curl-client": "^2.3", "predis/predis": "^2.3", "salsify/json-streaming-parser": "^8.3", "spatie/browsershot": "^4.3", "spatie/laravel-activitylog": "^4.9", "spatie/laravel-blade-javascript": "^2.8", "spatie/laravel-data": "^4.13", "spatie/laravel-deleted-models": "^1.1", "spatie/laravel-permission": "^6.9", "spatie/regex": "^3.1", "staudenmeir/belongs-to-through": "^2.5", "symfony/yaml": "^7.1", "tinymce/tinymce": "^6.7", "viison/address-splitter": "^0.3.4", "websmurf/laravel-exact-online": "^0.6.0"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.1", "fakerphp/faker": "^1.23", "laravel/pail": "^1.1", "laravel/pint": "^1.24", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#ffc5ff,#93c5fd,#c4b5fd,#fb7185\" \"php artisan queue:listen --tries=1\" \"npm run dev\" \"php artisan octane:start --watch\" --names=queue,dev,octane"], "build": "npm run build:prod && php artisan optimize:clear && php artisan optimize && php artisan octane:start --port=9999 --workers=4 --task-workers=6"}, "extra": {"laravel": {"dont-discover": ["barryvdh/laravel-ide-helper", "laravel/telescope"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}